# Opus0 - Comprehensive Codebase Documentation

## Table of Contents
1. [Overview](#overview)
2. [Architecture](#architecture)
3. [System Components](#system-components)
4. [Data Flow](#data-flow)
5. [Technology Stack](#technology-stack)
6. [Directory Structure](#directory-structure)
7. [Agent System](#agent-system)
8. [API Endpoints](#api-endpoints)
9. [Database Schema](#database-schema)
10. [Configuration](#configuration)
11. [Deployment](#deployment)
12. [Development Guidelines](#development-guidelines)

## Overview

**Opus0** is a distributed multi-agent AI platform developed by Synagi AI that implements a sophisticated manager-worker framework for coordinating specialized AI agents. The system breaks down complex user tasks into manageable subtasks and distributes them across a network of specialized worker agents, resulting in improved execution speed, cost-efficiency, and task accuracy.

### Key Features
- **Distributed Multi-Agent Architecture**: Manager-worker framework with specialized agents
- **Dynamic Task Decomposition**: Intelligent breaking down of complex tasks into subtasks
- **Contextual Collaboration**: Agents share relevant context without overloading
- **Real-time Scaling**: Dynamic agent allocation based on workload
- **Autonomous Operation**: End-to-end task planning and validation
- **Quality-Efficiency Index (QEI)**: 2× better performance than comparable systems

## Architecture

### High-Level Architecture

```mermaid
graph TB
    User[User Interface] --> WebClient[React Web Client]
    WebClient --> FastAPI[FastAPI Backend]
    FastAPI --> SocketIO[Socket.IO Communication]
    
    FastAPI --> TaskPlanner[Task Planner]
    TaskPlanner --> OperationsManager[Operations Manager]
    OperationsManager --> AgentFactory[Agent Factory]
    
    AgentFactory --> LLMWorker[LLM Worker]
    AgentFactory --> ReasoningWorker[Reasoning Worker]
    AgentFactory --> ResearcherAgent[Researcher Agent]
    AgentFactory --> WebScraperAgent[Web Scraper Agent]
    AgentFactory --> LinkAnalysisAgent[Link Analysis Agent]
    AgentFactory --> PDFGeneratorAgent[PDF Generator Agent]
    AgentFactory --> CodeInterpreterAgent[Code Interpreter Agent]
    
    OperationsManager --> Aggregator[Aggregator Agent]
    
    FastAPI --> MongoDB[(MongoDB)]
    FastAPI --> CloudflareR2[(Cloudflare R2)]
    
    subgraph "Agent Communication"
        MessageQueue[Message Queue System]
        TaskStatus[Task Status JSON]
        KnowledgeBase[Knowledge Base]
    end
```

### System Flow

1. **User Input Processing**: User submits a request through the React web client
2. **Task Planning**: TaskPlanner breaks down the request into subtasks
3. **Task Assignment**: OperationsManager assigns subtasks to appropriate workers
4. **Agent Routing**: AgentFactory routes subtasks to specialized worker agents
5. **Parallel Execution**: Worker agents execute subtasks concurrently
6. **Result Aggregation**: AggregatorAgent combines results into final response
7. **Response Delivery**: Final response streamed back to user interface

## System Components

### Core Managers

#### TaskPlanner
- **Purpose**: Decomposes user requests into manageable subtasks
- **Technology**: Uses GPT-4 via LangChain for intelligent task breakdown
- **Output**: Generates `task_status.json` with subtasks and dependencies
- **Key Features**:
  - Dependency analysis between subtasks
  - Upload file management
  - Context-aware task decomposition

#### OperationsManager
- **Purpose**: Orchestrates subtask execution and monitors progress
- **Responsibilities**:
  - Assigns pending subtasks to workers
  - Monitors task dependencies
  - Processes completion messages
  - Updates task status
- **Communication**: Uses message queue system for worker coordination

#### AgentFactory
- **Purpose**: Routes subtasks to appropriate specialized worker agents
- **Routing Logic**: Uses Gemini 2.5 Flash for intelligent agent selection
- **Concurrency**: Manages up to 5 concurrent worker agents
- **Agent Types**: Routes to 7 different specialized worker types

### Worker Agents

#### LLMWorkerAgent
- **Purpose**: Handles general language tasks
- **Use Cases**: Writing, summarization, translation, basic Q&A
- **Technology**: Various LLM providers (OpenAI, Google, Anthropic)

#### ReasoningWorkerAgent
- **Purpose**: Complex mathematical and logical reasoning
- **Use Cases**: Advanced coding, mathematical proofs, complex analysis
- **Technology**: Specialized reasoning models

#### ResearcherAgent
- **Purpose**: Autonomous multi-round web research
- **Features**:
  - Iterative search and content extraction
  - Multiple source analysis
  - Concise summary generation
- **Technology**: Brave Search API, Tavily integration

#### WebScraperAgent
- **Purpose**: Web scraping and data extraction
- **Technology**: Crawl4AI, Playwright for headless browsing
- **Capabilities**: Dynamic content extraction, structured data parsing

#### LinkAnalysisAgent
- **Purpose**: Analyzes provided URLs and YouTube videos
- **Features**: Content extraction, video transcript analysis
- **Technology**: YouTube Transcript API, web content parsers

#### PDFGeneratorAgent
- **Purpose**: Creates formatted PDF documents
- **Use Cases**: Reports, documentation, structured content
- **Technology**: Custom PDF generation pipeline

#### CodeInterpreterAgent
- **Purpose**: Executes Python code in sandboxed environment
- **Technology**: E2B Code Interpreter
- **Security**: Isolated execution environment

### Support Components

#### AggregatorAgent
- **Purpose**: Combines worker outputs into coherent final response
- **Technology**: Gemini 2.5 Flash for content synthesis
- **Features**: Context-aware aggregation, streaming response

#### BaseAgent
- **Purpose**: Abstract base class for all agents
- **Features**:
  - Logging and timing utilities
  - Token counting and cost tracking
  - Runtime progress reporting
  - Error handling and feedback processing

## Data Flow

### Message Processing Flow

```mermaid
sequenceDiagram
    participant User
    participant WebClient
    participant FastAPI
    participant TaskPlanner
    participant OpsManager
    participant AgentFactory
    participant Workers
    participant Aggregator

    User->>WebClient: Submit Request
    WebClient->>FastAPI: Socket.IO Message
    FastAPI->>TaskPlanner: Process Message
    TaskPlanner->>TaskPlanner: Generate Subtasks
    TaskPlanner->>OpsManager: Task Status Ready
    
    par Parallel Execution
        OpsManager->>AgentFactory: Assign Subtasks
        AgentFactory->>Workers: Route to Specialists
        Workers->>Workers: Execute Tasks
        Workers->>OpsManager: Report Completion
    end
    
    OpsManager->>Aggregator: All Tasks Complete
    Aggregator->>WebClient: Stream Final Response
    WebClient->>User: Display Result
```

### File System Organization

```
data/
├── sessions/
│   └── {chat_id}/
│       ├── agent_comm/          # Inter-agent messages
│       ├── knowledge_base/      # Task status and outputs
│       ├── public_pdfs/         # Generated PDF files
│       └── user_uploads/        # User-uploaded files
├── agent_analysis.log           # Performance metrics
└── app.log                      # Application logs
```

## Technology Stack

### Backend (ai-backend/)
- **Framework**: FastAPI with async support
- **Language**: Python 3.10+
- **Package Manager**: Poetry
- **Key Dependencies**:
  - `langchain` - LLM orchestration
  - `langchain-openai` - OpenAI integration
  - `langchain-google-genai` - Google AI integration
  - `langchain-anthropic` - Anthropic integration
  - `python-socketio` - Real-time communication
  - `pymongo` - MongoDB integration
  - `boto3` - AWS S3/Cloudflare R2 integration
  - `crawl4ai` - Web scraping
  - `playwright` - Browser automation
  - `e2b-code-interpreter` - Code execution
  - `gpt-researcher` - Research capabilities

### Frontend (web-client/)
- **Framework**: React 19 with TypeScript
- **Build Tool**: Vite
- **Styling**: Tailwind CSS 4.1
- **State Management**: Zustand
- **Key Dependencies**:
  - `socket.io-client` - Real-time communication
  - `react-markdown` - Markdown rendering
  - `framer-motion` - Animations
  - `lucide-react` - Icons

### Infrastructure
- **Database**: MongoDB with connection pooling
- **File Storage**: Cloudflare R2 (S3-compatible)
- **Containerization**: Docker with multi-stage builds
- **Orchestration**: Docker Compose

## Directory Structure

```
opus0-base/
├── ai-backend/                  # Python FastAPI backend
│   ├── app/
│   │   ├── agents/             # Agent implementations
│   │   │   ├── managers/       # Manager agents
│   │   │   │   ├── task_planner.py
│   │   │   │   └── operations_manager.py
│   │   │   ├── workers/        # Worker agents
│   │   │   │   ├── llm_worker_agent.py
│   │   │   │   ├── reasoning_worker_agent.py
│   │   │   │   ├── researcher_agent/
│   │   │   │   ├── web_scraper_agent.py
│   │   │   │   ├── link_analysis_agent.py
│   │   │   │   ├── pdf_generator_agent.py
│   │   │   │   └── code_interpreter_agent.py
│   │   │   ├── agent_factory.py
│   │   │   ├── aggregator_agent.py
│   │   │   └── base_agent.py
│   │   ├── api/                # REST API routes
│   │   │   └── routes/
│   │   │       ├── chat.py     # Main chat endpoint
│   │   │       ├── auth.py     # Authentication
│   │   │       ├── files.py    # File serving
│   │   │       ├── uploads.py  # File uploads
│   │   │       └── docs.py     # Documentation
│   │   ├── core/               # Core configuration
│   │   │   └── config.py
│   │   ├── db/                 # Database models
│   │   │   └── files.py
│   │   ├── services/           # External services
│   │   │   └── langchain_service.py
│   │   ├── utils/              # Utility functions
│   │   │   ├── communication.py
│   │   │   ├── task_status.py
│   │   │   ├── token_counter.py
│   │   │   ├── logging_config.py
│   │   │   └── live_updates.py
│   │   └── main.py             # Application entry point
│   ├── pyproject.toml          # Python dependencies
│   ├── Dockerfile
│   └── run.py                  # Development server
├── web-client/                 # React frontend
│   ├── src/
│   │   ├── components/         # React components
│   │   │   ├── chat/          # Chat interface
│   │   │   ├── layout/        # Layout components
│   │   │   └── Login/         # Authentication
│   │   ├── lib/               # Utility libraries
│   │   │   ├── chat/          # Chat state management
│   │   │   ├── r2/            # File upload utilities
│   │   │   └── socket.ts      # Socket.IO client
│   │   ├── App.tsx
│   │   └── main.tsx
│   ├── package.json           # Node.js dependencies
│   ├── tailwind.config.js
│   ├── vite.config.ts
│   └── Dockerfile
├── docker-compose.yml         # Container orchestration
├── README.md                  # Project overview
├── AGENTS.md                  # Agent documentation
└── CODING_CONVENTIONS.md      # Development standards
```

## Agent System

### Agent Hierarchy

The Opus0 system implements a sophisticated three-tier agent hierarchy:

1. **Manager Tier**: High-level coordination and planning
2. **Factory Tier**: Task routing and worker management
3. **Worker Tier**: Specialized task execution

### Agent Communication Protocol

Agents communicate through a file-based message queue system:

```python
# Message Structure
{
    "subtask_id": "unique_identifier",
    "subtask_description": "task_description",
    "user_message": "original_user_request",
    "deps_info": "dependency_information",
    "uploaded_images": ["image1.jpg", "image2.png"],
    "history": "conversation_context",
    "parameters": {}
}
```

### Task Status Management

The system maintains task state in `task_status.json`:

```json
{
    "criteria": "Overall task description",
    "subtasks": [
        {
            "id": "1",
            "desc": "Subtask description",
            "deps": ["prerequisite_task_ids"],
            "uploads": ["required_files"],
            "status": "pending|in_progress|completed|failed",
            "output_file": "result_file.md"
        }
    ]
}
```

### Agent Routing Logic

The AgentFactory uses an intelligent routing system:

```python
# Router Decision Matrix
AGENT_SELECTION_CRITERIA = {
    "llm": "General language tasks, writing, basic Q&A",
    "reasoning": "Complex math, advanced coding, logical analysis",
    "web_scraper": "Data extraction, web scraping",
    "researcher": "Multi-round research, source analysis",
    "link_analysis": "URL/video content analysis",
    "pdf_generator": "Document creation and formatting",
    "code_interpreter": "Python code execution"
}
```

## API Endpoints

### Core Endpoints

#### Socket.IO Events

**Client → Server Events:**
- `connect`: Establish connection
- `chat_created`: Initialize new chat session
- `chat_message`: Submit user message
- `store_ai_message`: Persist AI response
- `feedback`: Submit user feedback

**Server → Client Events:**
- `chat_response`: Stream response chunks
- `runtime_logs`: Real-time progress updates

#### REST API Routes

**Authentication (`/auth`)**
- `POST /auth/login`: User authentication
- `POST /auth/logout`: Session termination

**File Management (`/files`, `/uploads`, `/docs`)**
- `GET /files/{filename}`: Serve static files
- `POST /uploads`: Handle file uploads
- `GET /docs/{filename}`: Download generated PDFs

### Request/Response Flow

```mermaid
sequenceDiagram
    participant Client
    participant SocketIO
    participant ChatRoute
    participant TaskPlanner
    participant Workers
    participant Aggregator

    Client->>SocketIO: chat_message
    SocketIO->>ChatRoute: process_message()
    ChatRoute->>TaskPlanner: execute_task()
    TaskPlanner-->>ChatRoute: task_status.json

    loop For each subtask
        ChatRoute->>Workers: execute_subtask()
        Workers-->>ChatRoute: completion_message
    end

    ChatRoute->>Aggregator: generate_final_response_stream()
    Aggregator-->>SocketIO: response_chunks
    SocketIO-->>Client: chat_response events
```

## Database Schema

### MongoDB Collections

#### chat_histories
```javascript
{
    _id: ObjectId,
    conversation_id: String,    // Unique chat identifier
    user_id: String,           // User identifier
    messages: [
        {
            role: "user|assistant",
            content: String,
            timestamp: Date,
            attachments: [String],
            reasoning_title: String,    // For AI messages
            runtime_logs: [Object]      // Execution logs
        }
    ]
}
```

#### feedback
```javascript
{
    _id: ObjectId,
    chat_id: String,
    value: "up|down",
    timestamp: Date
}
```

### File Storage (Cloudflare R2)

**Bucket Structure:**
```
bucket/
├── uploads/
│   ├── {chat_id}/
│   │   ├── documents/     # User uploaded docs
│   │   └── images/        # User uploaded images
└── generated/
    └── {chat_id}/
        └── pdfs/          # System generated PDFs
```

## Configuration

### Environment Variables

**Required Configuration (`.env`):**

```bash
# API Keys
OPENAI_API_KEY=sk-...
GOOGLE_API_KEY=...
ANTHROPIC_API_KEY=...
BRAVE_SEARCH_API_KEY=...
TAVILY_API_KEY=...

# Database
MONGODB_URL=mongodb://...
MONGODB_DB_NAME=opus0

# File Storage
R2_ENDPOINT=https://...
R2_BUCKET_NAME=opus0-storage
R2_ACCESS_KEY_ID=...
R2_SECRET_ACCESS_KEY=...

# Application
FRONTEND_URL=http://localhost:5173
CORS_ORIGINS=["http://localhost:5173"]
```

**Optional Configuration:**
```bash
# Logging
LOG_LEVEL=INFO
LOG_FILE=app.log

# Performance
MAX_CONCURRENT_WORKERS=5
SLEEP_INTERVAL=1.0
FAST_INTERVAL=0.1

# Model Selection
DEFAULT_LLM_MODEL=gpt-4
DEFAULT_REASONING_MODEL=gpt-4
```

### Model Configuration

The system supports multiple LLM providers:

```python
# Model Configurations
SUPPORTED_MODELS = {
    "openai": ["gpt-4", "gpt-4-turbo", "gpt-3.5-turbo"],
    "google": ["gemini-pro", "gemini-2.5-flash-preview-04-17"],
    "anthropic": ["claude-3-sonnet", "claude-3-haiku"],
    "deepseek": ["deepseek-chat", "deepseek-coder"]
}
```

## Deployment

### Local Development

**Prerequisites:**
- Python 3.10+ with Poetry
- Node.js 18+ with npm
- MongoDB instance
- Cloudflare R2 bucket (optional for file storage)

**Setup Steps:**

1. **Clone Repository:**
```bash
git clone https://github.com/garv-2501/opus0-v2-latest.git
cd opus0
```

2. **Backend Setup:**
```bash
cd ai-backend
poetry install
poetry run playwright install
cp .env.example .env
# Edit .env with your API keys
poetry run python run.py  # Starts on http://localhost:8000
```

3. **Frontend Setup:**
```bash
cd ../web-client
npm install
npm run dev  # Starts on http://localhost:5173
```

### Docker Deployment

**Single Container:**
```bash
# Backend only
docker build -t opus0-backend ./ai-backend
docker run -p 8000:8000 --env-file ai-backend/.env opus0-backend

# Frontend only
docker build -t opus0-frontend ./web-client
docker run -p 5173:80 opus0-frontend
```

**Docker Compose (Recommended):**
```bash
docker compose build
docker compose up
```

**Production Docker Compose:**
```yaml
version: "3.8"
services:
  backend:
    build: ./ai-backend
    env_file: ./ai-backend/.env
    volumes:
      - ./data:/data
      - ./logs:/app/logs
    ports:
      - "8000:8000"
    restart: unless-stopped

  frontend:
    build: ./web-client
    depends_on:
      - backend
    ports:
      - "80:80"
    restart: unless-stopped

  mongodb:
    image: mongo:7
    volumes:
      - mongodb_data:/data/db
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password
    ports:
      - "27017:27017"

volumes:
  mongodb_data:
```

### Production Considerations

**Scaling:**
- Use container orchestration (Kubernetes, Docker Swarm)
- Implement load balancing for multiple backend instances
- Configure MongoDB replica sets for high availability
- Use Redis for session management and caching

**Security:**
- Enable HTTPS with SSL certificates
- Implement proper authentication and authorization
- Use secrets management for API keys
- Configure firewall rules and network policies

**Monitoring:**
- Set up application performance monitoring (APM)
- Configure log aggregation and analysis
- Implement health checks and alerting
- Monitor resource usage and costs

## Development Guidelines

### Code Style Standards

**Python (Backend):**
- Follow PEP 8 style guidelines
- Use Black for code formatting
- Use Flake8 for linting
- Type hints required for all functions
- Docstrings for all public methods

**File Header Template:**
```python
# app/path/to/file.py
"""
Module: filename.py

Brief description of the module's purpose and functionality.
"""
```

**Section Separators:**
```python
# ──────────────────────────────────────────────────────────────
#                         SECTION NAME
# ──────────────────────────────────────────────────────────────
```

**TypeScript (Frontend):**
- Use ESLint with TypeScript rules
- Prefer functional components with hooks
- Use TypeScript strict mode
- Follow React best practices

### Testing Strategy

**Backend Testing:**
```bash
# Unit tests
poetry run pytest tests/unit/

# Integration tests
poetry run pytest tests/integration/

# Agent-specific tests
poetry run pytest tests/agents/
```

**Frontend Testing:**
```bash
# Component tests
npm run test

# E2E tests
npm run test:e2e
```

### Contributing Workflow

1. **Fork and Clone:**
```bash
git clone https://github.com/your-username/opus0.git
cd opus0
git remote add upstream https://github.com/garv-2501/opus0-v2-latest.git
```

2. **Create Feature Branch:**
```bash
git checkout -b feature/your-feature-name
```

3. **Development:**
- Follow coding standards
- Add tests for new functionality
- Update documentation as needed
- Test locally before committing

4. **Submit Pull Request:**
- Ensure all tests pass
- Include clear description of changes
- Reference any related issues

### Performance Optimization

**Backend Optimization:**
- Use async/await for I/O operations
- Implement connection pooling for databases
- Cache frequently accessed data
- Optimize LLM token usage
- Monitor and log performance metrics

**Frontend Optimization:**
- Implement code splitting and lazy loading
- Optimize bundle size with tree shaking
- Use React.memo for expensive components
- Implement virtual scrolling for large lists
- Optimize image loading and caching

### Debugging and Troubleshooting

**Common Issues:**

1. **Agent Communication Failures:**
   - Check message queue file permissions
   - Verify task_status.json format
   - Monitor agent logs for errors

2. **LLM API Errors:**
   - Verify API keys and quotas
   - Check rate limiting
   - Monitor token usage

3. **File Upload Issues:**
   - Verify R2 bucket configuration
   - Check file size limits
   - Validate CORS settings

**Debugging Tools:**
- Use structured logging with correlation IDs
- Implement distributed tracing
- Monitor real-time metrics
- Use debugging proxies for API calls

### Security Best Practices

**API Security:**
- Implement rate limiting
- Validate all input data
- Use HTTPS for all communications
- Implement proper CORS policies

**Data Protection:**
- Encrypt sensitive data at rest
- Use secure session management
- Implement data retention policies
- Regular security audits

**Agent Security:**
- Sandbox code execution environments
- Validate agent outputs
- Implement access controls
- Monitor for malicious activities

---

## Conclusion

Opus0 represents a sophisticated implementation of distributed multi-agent AI systems, providing a robust foundation for complex task automation and intelligent assistance. The modular architecture, comprehensive agent ecosystem, and scalable infrastructure make it suitable for both research and production deployments.

For additional support and contributions, please refer to the project repository and community guidelines.
