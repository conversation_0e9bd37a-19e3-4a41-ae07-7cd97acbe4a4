# RAG Example 1
# Task: Create a minimalist title slide with gradient header and styled text elements
# Explanation: Creates a complete presentation with a professional slide featuring gradient header background, company branding elements, multi-line title text, and decorative connector line with proper imports and file saving
# ======================================================================

from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.dml.color import RGBColor
from pptx.enum.shapes import MSO_SHAPE, MSO_CONNECTOR

# Load presentation and blank slide
prs = Presentation('presentation_path.pptx')
slide = prs.slides.add_slide(prs.slide_layouts[6])  # 6 = blank

# Constants for slide dimensions
SLD_W = prs.slide_width
SLD_H = prs.slide_height

# 1) Gradient header
header_height = Inches(2.5)
grad_shape = slide.shapes.add_shape(
    MSO_SHAPE.RECTANGLE, 0, 0, SLD_W, header_height
)
grad = grad_shape.fill
grad.gradient()                     # enable gradient fill
stops = grad.gradient_stops         # get stops collection
# Left stop (light green)
stops[0].position = 0.0
stops[0].color.rgb = RGBColor(126, 231, 135)
# Right stop (turquoise)
stops[1].position = 1.0
stops[1].color.rgb = RGBColor(91, 245, 225)
grad.gradient_angle = 0            # 0° = left→right
grad_shape.line.fill.background() # remove border line

# 2) Company name (top left)
tx = slide.shapes.add_textbox(Inches(0.5), Inches(0.2), Inches(3), Inches(0.4))
p = tx.text_frame.paragraphs[0]
p.text = "COMPANY NAME"
p.font.name = "Arial"
p.font.size = Pt(12)
p.font.color.rgb = RGBColor(0, 0, 0)

# 3) Date (top right)
tx = slide.shapes.add_textbox(SLD_W - Inches(2.5), Inches(0.2), Inches(2), Inches(0.4))
p = tx.text_frame.paragraphs[0]
p.text = "July 2024"
p.font.name = "Arial"
p.font.size = Pt(12)
p.font.color.rgb = RGBColor(0, 0, 0)
p.alignment = 2  # right-align

# 4) "Simple" (small) over title
tx = slide.shapes.add_textbox(Inches(0.5), header_height + Inches(0.3), Inches(2), Inches(0.6))
p = tx.text_frame.paragraphs[0]
p.text = "Simple"
p.font.name = "Arial"
p.font.size = Pt(24)
p.font.color.rgb = RGBColor(0, 0, 0)

# 5) "Performance Review" (big, two lines)
tx = slide.shapes.add_textbox(Inches(0.5), header_height + Inches(1.0), Inches(8), Inches(3))
tf = tx.text_frame
tf.clear()
p1 = tf.paragraphs[0]
p1.text = "Performance"
p1.font.name = "Arial"
p1.font.size = Pt(64)
p1.font.bold = True
p1.font.color.rgb = RGBColor(0, 0, 0)
# second line
p2 = tf.add_paragraph()
p2.text = "Review"
p2.font.name = "Arial"
p2.font.size = Pt(64)
p2.font.bold = True
p2.font.color.rgb = RGBColor(0, 0, 0)

# 6) Underline (connector line)
y_line = header_height + Inches(4.4)
line = slide.shapes.add_connector(
    MSO_CONNECTOR.STRAIGHT,
    Inches(0.5), y_line,
    Inches(8.0), y_line
)
line.line.width = Pt(2)
line.line.color.rgb = RGBColor(0, 0, 0)

# 7) "Slides" at end of line
tx = slide.shapes.add_textbox(Inches(8.2), y_line - Pt(2), Inches(2), Inches(0.5))
p = tx.text_frame.paragraphs[0]
p.text = "Slides"
p.font.name = "Arial"
p.font.size = Pt(18)
p.font.color.rgb = RGBColor(0, 0, 0)

# Save the file
prs.save('presentation_path.pptx')
