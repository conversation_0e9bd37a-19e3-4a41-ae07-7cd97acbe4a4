# RAG Example 2
# Task: Create content slide with title and body text paragraphs
# Explanation: Creates a content slide with company branding, title, and multi-paragraph body text using helper functions for positioning and proper text frame handling
# ======================================================================

from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.dml.color import RGBColor

# -------------------- 1. Load presentation & blank slide -------------------
prs   = Presentation('presentation_path.pptx')
slide = prs.slides.add_slide(prs.slide_layouts[6])   # blank

SLD_W, SLD_H = prs.slide_width, prs.slide_height

# Helper to place objects via inches
def px(left, top, width, height):
    return Inches(left), Inches(top), Inches(width), Inches(height)

# -------------------- 2. Company name (top-left) -----------------------------
tb = slide.shapes.add_textbox(*px(0.8, 0.5, 4, 0.5))
p  = tb.text_frame.paragraphs[0]
p.text = "COMPANY  NAME"
p.font.name = "Calibri Light"
p.font.size = Pt(12)
p.font.color.rgb = RGBColor(0, 0, 0)

# -------------------- 3. Date (top-right) ------------------------------------
date_tb = slide.shapes.add_textbox(SLD_W - Inches(2.3), Inches(0.5),
                                   Inches(2.0), Inches(0.5))
p = date_tb.text_frame.paragraphs[0]
p.text = "July 2024"
p.font.name = "Calibri Light"
p.font.size = Pt(12)
p.font.color.rgb = RGBColor(0, 0, 0)
p.alignment = 2   # right-align

# -------------------- 4. Slide title -----------------------------------------
title_tb = slide.shapes.add_textbox(*px(0.8, 1, 10, 1.2))
p = title_tb.text_frame.paragraphs[0]
p.text = "Title"
p.font.name = "Calibri Light"
p.font.size = Pt(60)
p.font.color.rgb = RGBColor(0, 0, 0)

# -------------------- 5. Body text (two paragraphs) ---------------------------
lorem = (
    "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Integer vitae "
    "leo sapien. Morbi eget dictum turpis. Class aptent taciti sociosqu ad "
    "litora torquent per conubia nostra, per inceptos himenaeos. Aliquam "
    "euismod pharetra laoreet. Phasellus et nisl at eros maximus luctus. "
    "Maecenas ornare ligula euismod sagittis pretium. Nunc vestibulum dictum "
    "nulla, sed aliquet metus ornare eu."
)

body_tb = slide.shapes.add_textbox(*px(0.5, 2.5, 9, 4.5))
tf = body_tb.text_frame
tf.word_wrap = True
tf.clear()

# First paragraph
p1 = tf.paragraphs[0]
p1.text = lorem
p1.font.name = "Calibri"
p1.font.size = Pt(20)
p1.font.color.rgb = RGBColor(0, 0, 0)

# Spacing between paragraphs
p_spacing = tf.add_paragraph()
p_spacing.text = ""  # blank paragraph for spacing

# Second paragraph (duplicate lorem text)
p2 = tf.add_paragraph()
p2.text = lorem
p2.font.name = "Calibri"
p2.font.size = Pt(20)
p2.font.color.rgb = RGBColor(0, 0, 0)

# -------------------- 6. Save -------------------------------------------------
prs.save('presentation_path.pptx')
