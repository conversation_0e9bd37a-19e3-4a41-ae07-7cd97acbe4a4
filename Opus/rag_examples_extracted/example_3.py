# RAG Example 3
# Task: Create bullet point slide with gradient banner and styled content
# Explanation: Creates a bullet point slide using Title and Content layout with gradient banner background, styled title text, and multiple bullet points with proper layering and font formatting
# ======================================================================

from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.dml.color import RGBColor
from pptx.enum.shapes import MSO_SHAPE
from pptx.oxml import parse_xml

# 1) Load presentation and add a "Title and Content" slide for bullets
prs = Presentation('presentation_path.pptx')
bullet_slide_layout = prs.slide_layouts[1]
slide = prs.slides.add_slide(bullet_slide_layout)

SLD_W, SLD_H = prs.slide_width, prs.slide_height

# 2) Draw the gradient banner behind the title
banner_h = Inches(1.8)
banner = slide.shapes.add_shape(
    MSO_SHAPE.RECTANGLE, 0, 0, SLD_W, banner_h
)
fill = banner.fill
fill.gradient()
stops = fill.gradient_stops
stops[0].position, stops[0].color.rgb = 0.0, RGBColor(126, 231, 135)
stops[1].position, stops[1].color.rgb = 1.0, RGBColor(91, 245, 225)
fill.gradient_angle = 0  # left→right
banner.line.fill.background()  # remove border

# 3) Send the banner to the back so title & bullets appear on top
spTree = slide.shapes._spTree
# remove and re-insert at position 2 (after slide background)
spTree.remove(banner._element)
spTree.insert(2, banner._element)

# 4) Header text over the banner (optional placeholders in layout)
# SKIP

# 5) Title text is already in the title placeholder; you can style it:
title_shape = slide.shapes.title
title_shape.text = "Title Here"
title_shape.text_frame.paragraphs[0].font.name = "Calibri Light"
title_shape.text_frame.paragraphs[0].font.size = Pt(60)

# 6) Populate bullets in the content placeholder
body_shape = slide.shapes.placeholders[1]
tf = body_shape.text_frame

# First bullet
tf.text = "Bulletpoint 1…"
p0 = tf.paragraphs[0]
p0.level = 0
p0.font.name = "Calibri"
p0.font.size = Pt(20)

# Additional bullets
points = [
    "Bulletpoint 2…",
    "Bulletpoint 3…",
    "Bulletpoint 4…",
    "Bulletpoint 5…",
    "Bulletpoint 6…",
    "Bulletpoint 7…",
]
for text in points:
    p = tf.add_paragraph()
    p.text = text
    p.level = 0
    p.font.name = "Calibri"
    p.font.size = Pt(20)

# 7) Save
prs.save('presentation_path.pptx')
