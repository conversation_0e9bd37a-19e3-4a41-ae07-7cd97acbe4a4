#!/usr/bin/env python3
"""
Test script to validate PPTX RAG examples for proper formatting and syntax.
"""

import ast
import json
import sys
from pathlib import Path

def test_pptx_rag_examples():
    """Test the PPTX code examples for syntax and formatting issues."""
    
    # Path to the RAG examples file
    rag_file = Path("opus0-base/ai-backend/app/rag_data/pptx_code_examples.jsonl")
    
    if not rag_file.exists():
        print(f"❌ RAG file not found: {rag_file}")
        return False
    
    print(f"🔍 Testing RAG examples from: {rag_file}")
    print("=" * 60)
    
    all_valid = True
    example_count = 0
    
    try:
        with open(rag_file, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line:
                    continue
                    
                try:
                    # Parse JSON
                    example = json.loads(line)
                    example_count += 1
                    
                    # Extract required fields
                    example_id = example.get('id', f'line_{line_num}')
                    task = example.get('task', 'Unknown task')
                    code = example.get('code', '')
                    
                    print(f"\n📝 Example {example_id}: {task}")
                    print("-" * 40)
                    
                    # Test 1: Check if code field exists and is not empty
                    if not code:
                        print(f"❌ Empty code field")
                        all_valid = False
                        continue
                    
                    # Test 2: Check for basic syntax issues
                    syntax_issues = check_syntax_issues(code)
                    if syntax_issues:
                        print(f"⚠️  Syntax issues found:")
                        for issue in syntax_issues:
                            print(f"   - {issue}")
                        all_valid = False
                    
                    # Test 3: Try to parse as Python AST
                    try:
                        ast.parse(code)
                        print(f"✅ Valid Python syntax")
                    except SyntaxError as e:
                        print(f"❌ Python syntax error: {e}")
                        print(f"   Line {e.lineno}: {e.text}")
                        all_valid = False
                    
                    # Test 4: Check for required imports
                    required_imports = [
                        'from pptx import Presentation',
                        'from pptx.util import',
                        'from pptx.dml.color import RGBColor'
                    ]
                    
                    missing_imports = []
                    for imp in required_imports:
                        if imp not in code:
                            missing_imports.append(imp)
                    
                    if missing_imports:
                        print(f"⚠️  Missing imports:")
                        for imp in missing_imports:
                            print(f"   - {imp}")
                    
                    # Test 5: Check for function definition
                    if 'def ' not in code:
                        print(f"⚠️  No function definition found")
                    
                    # Test 6: Check for save operation
                    if '.save(' not in code:
                        print(f"⚠️  No save operation found")
                    
                except json.JSONDecodeError as e:
                    print(f"❌ JSON parsing error on line {line_num}: {e}")
                    all_valid = False
                    
    except Exception as e:
        print(f"❌ Error reading file: {e}")
        return False
    
    print("\n" + "=" * 60)
    print(f"📊 SUMMARY:")
    print(f"   Total examples tested: {example_count}")
    print(f"   Overall result: {'✅ ALL VALID' if all_valid else '❌ ISSUES FOUND'}")
    
    return all_valid

def check_syntax_issues(code):
    """Check for common syntax issues that might not be caught by AST."""
    issues = []
    
    lines = code.split('\n')
    
    for i, line in enumerate(lines, 1):
        # Check for lines that are too long (might indicate missing newlines)
        if len(line) > 120:
            issues.append(f"Line {i} is very long ({len(line)} chars): {line[:50]}...")
        
        # Check for multiple statements on one line (basic check)
        if ';' in line and not line.strip().startswith('#'):
            issues.append(f"Line {i} contains semicolon (multiple statements?): {line.strip()}")
        
        # Check for missing spaces around operators
        if any(pattern in line for pattern in ['=', '==', '!=', '<=', '>=']):
            # Simple check for missing spaces (not comprehensive)
            if '=' in line and not any(op in line for op in [' = ', ' == ', ' != ', ' <= ', ' >= ']):
                if not line.strip().startswith('#') and '=' in line:
                    issues.append(f"Line {i} might have spacing issues: {line.strip()}")
        
        # Check for unclosed brackets/parentheses (basic check)
        open_parens = line.count('(') - line.count(')')
        open_brackets = line.count('[') - line.count(']')
        open_braces = line.count('{') - line.count('}')
        
        if open_parens != 0 or open_brackets != 0 or open_braces != 0:
            # This is normal for multi-line constructs, but flag extreme cases
            if abs(open_parens) > 3 or abs(open_brackets) > 3 or abs(open_braces) > 3:
                issues.append(f"Line {i} has many unclosed brackets: {line.strip()}")
    
    return issues

def test_specific_example(example_id):
    """Test a specific example by ID."""
    rag_file = Path("opus0-base/ai-backend/app/rag_data/pptx_code_examples.jsonl")
    
    if not rag_file.exists():
        print(f"❌ RAG file not found: {rag_file}")
        return
    
    with open(rag_file, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if not line:
                continue
            
            try:
                example = json.loads(line)
                if example.get('id') == example_id:
                    print(f"🔍 Testing example {example_id}:")
                    print("=" * 40)
                    print(example.get('code', ''))
                    print("=" * 40)
                    
                    # Test syntax
                    try:
                        ast.parse(example.get('code', ''))
                        print("✅ Valid Python syntax")
                    except SyntaxError as e:
                        print(f"❌ Syntax error: {e}")
                    
                    return
            except json.JSONDecodeError:
                continue
    
    print(f"❌ Example {example_id} not found")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        # Test specific example
        example_id = int(sys.argv[1]) if sys.argv[1].isdigit() else sys.argv[1]
        test_specific_example(example_id)
    else:
        # Test all examples
        success = test_pptx_rag_examples()
        sys.exit(0 if success else 1)
