# Code Style Guide for Opus0 AI-Backend

This document defines the standard coding practices and conventions for the Opus0 AI-Backend project. Following these guidelines will ensure consistency, readability, and maintainability across the codebase.

---

## 1. File Header

Every source file **must** begin with:

```python
# <relative_file_path>
"""
Module: <filename>.py

A brief description of the module’s purpose and functionality.
"""
```

**Example**:

```python
# app/agents/task_planner.py
"""
Module: task_planner.py

This module defines the TaskPlanner class, a manager bot responsible for generating the
`task_status.json` file based on the user input. It uses GPT-4 (via <PERSON><PERSON><PERSON><PERSON>) to break down
the main task into subtasks and assignment criteria, and then initializes the task status using
the generated plan. The class inherits from Base<PERSON>gent.
"""
```

---

## 2. Section Separators

Group logically related code blocks with a consistent separator:

```python
#──────────────────────────────────────────────────
#                         SECTION
#──────────────────────────────────────────────────
```

- Replace `SECTION` with descriptive titles, e.g., **INTERNAL HELPERS**, **CLASS DEFINITIONS**, **PUBLIC API**, **UNIT TESTS**.

**Example**:

```python
#──────────────────────────────────────────────────
#                         INTERNAL HELPERS
#──────────────────────────────────────────────────
```

---

## 3. Comments & Docstrings

- **Module docstrings** at the top of each file.
- **Class docstrings** immediately below the class definition.
- **Function docstrings** for all public functions/methods.
- **Inline comments** sparingly to clarify non-obvious logic.
- Follow [PEP 257](https://www.python.org/dev/peps/pep-0257/) conventions:

  - Triple-quoted strings
  - One-line summary, blank line, detailed description (if needed)

---

## 4. Functional Programming Practices

- Favor **pure functions**: no side effects, inputs → outputs only.
- Use **immutable** data structures when possible.
- Keep functions **small** and **single-purpose**.
- Prefer **higher-order functions** (`map`, `filter`, `reduce`) or comprehensions for transformations.

---

## 5. Object-Oriented Practices

- All agents inherit from `BaseAgent`.
- Apply the **Single Responsibility Principle**: classes should have one clear responsibility.
- Use **composition over inheritance** where multiple behaviors are needed.
- Keep methods **short** (ideally < 50 lines).

---

## 6. Naming Conventions

| Entity    | Style              | Example           |
| --------- | ------------------ | ----------------- |
| Files     | `snake_case.py`    | `task_planner.py` |
| Classes   | `CamelCase`        | `TaskPlanner`     |
| Functions | `snake_case`       | `plan_tasks()`    |
| Variables | `snake_case`       | `task_list`       |
| Constants | `UPPER_SNAKE_CASE` | `MAX_CONCURRENCY` |

---

## 7. Imports

1. **Standard library**
2. **Third-party packages**
3. **Local application imports**

All imports should be **absolute**, not relative.

```python
import asyncio
import logging

from langchain_openai import ChatOpenAI

from app.utils.task_status import read_task_status
```

---

## 8. Logging

- Initialize logger at the module level:

  ```python
  import logging
  logger = logging.getLogger(__name__)
  ```

- Use appropriate log levels (`DEBUG`, `INFO`, `WARNING`, `ERROR`, `CRITICAL`).

---

## 9. Type Hinting

- **Mandatory** for function signatures and return values.
- Use `typing` (e.g., `List`, `Dict`, `Optional`) for complex types.

```python
def plan_tasks(input_data: Dict[str, Any]) -> List[Task]:
    ...
```

---

## 10. Linting & Formatting

- **Black** for automatic code formatting.
- **Flake8** for linting (configurable via `.flake8`).
- Run both tools pre-commit or integrate with CI.

---

By adhering to these standards, we ensure our codebase remains clean, consistent, and scalable as the project grows.
