# ai-backend/Dockerfile
#
# Build the Opus0 back-end using Python 3.11.
# Installs dependencies with Poetry and sets up Playwright.

FROM python:3.11-slim

# Install build and runtime dependencies
RUN apt-get update \
    && apt-get install -y --no-install-recommends build-essential curl \
    && rm -rf /var/lib/apt/lists/*

# Install Poetry
ENV POETRY_VERSION=1.8.2 \
    POETRY_VIRTUALENVS_CREATE=false \
    PYTHONUNBUFFERED=1
RUN curl -sSL https://install.python-poetry.org | python3 - \
    && ln -s /root/.local/bin/poetry /usr/local/bin/poetry

WORKDIR /app
ENV OPUS_DATA_DIR=/data

# Copy dependency files first for caching
COPY pyproject.toml poetry.lock ./

RUN poetry install --no-interaction --no-root \
    && poetry run playwright install --with-deps

# Copy application code
COPY . .

# ensure runtime directories and log files exist
RUN mkdir -p /data/sessions \
    && touch /data/app.log /data/agent_analysis.log

# persist session data at runtime
VOLUME ["/data"]

EXPOSE 8000

CMD ["poetry", "run", "python", "run.py"]