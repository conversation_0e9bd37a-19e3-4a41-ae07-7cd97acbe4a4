#!/usr/bin/env python3
"""
Test script for XLSX generator chart functionality.
This script tests the chart creation methods independently.
"""

import sys
import os
import json
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Import the XLSX generator
from app.agents.workers.xlsx_generator_agent import XLSXGeneratorAgent

def test_chart_creation():
    """Test chart creation functionality"""
    print("Testing XLSX Chart Creation...")
    
    # Create a mock agent instance
    agent = XLSXGeneratorAgent(chat_id="test_chat", manager_queue=None)
    
    # Test data with charts
    test_data = {
        "worksheets": [
            {
                "name": "Sales Data",
                "tables": [
                    {
                        "headers": [
                            {"value": "Product", "data_type": "text"},
                            {"value": "Q1 Sales", "data_type": "currency"},
                            {"value": "Q2 Sales", "data_type": "currency"}
                        ],
                        "rows": [
                            [
                                {"value": "Software", "data_type": "text"},
                                {"value": 150000, "data_type": "currency"},
                                {"value": 180000, "data_type": "currency"}
                            ],
                            [
                                {"value": "Hardware", "data_type": "text"},
                                {"value": 80000, "data_type": "currency"},
                                {"value": 95000, "data_type": "currency"}
                            ],
                            [
                                {"value": "Services", "data_type": "text"},
                                {"value": 50000, "data_type": "currency"},
                                {"value": 60000, "data_type": "currency"}
                            ]
                        ]
                    }
                ],
                "charts": [
                    {
                        "type": "bar",
                        "title": "Q1 vs Q2 Sales",
                        "data_range": {
                            "categories": "A2:A4",
                            "values": "B2:C4"
                        },
                        "position": "E2",
                        "style": 10,
                        "options": {
                            "width": 15,
                            "height": 10,
                            "grouping": "clustered",
                            "show_legend": True,
                            "x_axis_title": "Product",
                            "y_axis_title": "Sales ($)"
                        }
                    },
                    {
                        "type": "pie",
                        "title": "Q1 Sales Distribution",
                        "data_range": {
                            "categories": "A2:A4",
                            "values": "B2:B4"
                        },
                        "position": "E18",
                        "style": 12,
                        "options": {
                            "width": 12,
                            "height": 8,
                            "show_legend": True
                        }
                    }
                ]
            }
        ]
    }
    
    outline_plan = {
        "title": "Sales Report",
        "worksheets": [
            {
                "name": "Sales Data",
                "purpose": "Quarterly sales analysis"
            }
        ],
        "formatting": {
            "currency_format": "$#,##0.00",
            "header_style": {
                "bold": True,
                "background_color": "CCCCCC"
            }
        }
    }
    
    # Test file creation
    test_output_dir = Path("test_output")
    test_output_dir.mkdir(exist_ok=True)
    test_file = test_output_dir / "test_charts.xlsx"
    
    try:
        # Create Excel file with charts
        agent.create_xlsx_from_json(test_data, outline_plan, test_file)
        
        if test_file.exists():
            print(f"✅ Successfully created Excel file with charts: {test_file}")
            print(f"   File size: {test_file.stat().st_size} bytes")
            
            # Test chart validation
            for worksheet in test_data["worksheets"]:
                for chart in worksheet.get("charts", []):
                    is_valid = agent._validate_chart_data(chart, worksheet)
                    chart_title = chart.get("title", "Untitled")
                    if is_valid:
                        print(f"✅ Chart validation passed: {chart_title}")
                    else:
                        print(f"❌ Chart validation failed: {chart_title}")
            
            # Test fallback chart generation
            fallback_charts = agent._get_fallback_chart_data(test_data["worksheets"][0])
            if fallback_charts:
                print(f"✅ Fallback chart generation works: {len(fallback_charts)} charts generated")
            else:
                print("ℹ️  No fallback charts generated (expected with existing charts)")
            
            return True
        else:
            print("❌ Failed to create Excel file")
            return False
            
    except Exception as e:
        print(f"❌ Error during chart creation: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_chart_validation():
    """Test chart validation functionality"""
    print("\nTesting Chart Validation...")
    
    agent = XLSXGeneratorAgent(chat_id="test_chat", manager_queue=None)
    
    # Test valid chart
    valid_chart = {
        "type": "bar",
        "title": "Valid Chart",
        "data_range": {
            "categories": "A2:A5",
            "values": "B2:B5"
        },
        "position": "D2"
    }
    
    # Test invalid chart (missing type)
    invalid_chart1 = {
        "title": "Invalid Chart",
        "data_range": {
            "values": "B2:B5"
        }
    }
    
    # Test invalid chart (invalid type)
    invalid_chart2 = {
        "type": "invalid_type",
        "title": "Invalid Type Chart",
        "data_range": {
            "values": "B2:B5"
        }
    }
    
    worksheet_data = {"tables": [{"headers": [], "rows": []}]}
    
    # Test validations
    tests = [
        (valid_chart, True, "Valid chart"),
        (invalid_chart1, False, "Chart missing type"),
        (invalid_chart2, False, "Chart with invalid type")
    ]
    
    for chart, expected, description in tests:
        result = agent._validate_chart_data(chart, worksheet_data)
        if result == expected:
            print(f"✅ {description}: validation {'passed' if expected else 'failed'} as expected")
        else:
            print(f"❌ {description}: validation {'passed' if result else 'failed'}, expected {'pass' if expected else 'fail'}")

if __name__ == "__main__":
    print("XLSX Chart Functionality Test")
    print("=" * 40)
    
    success = test_chart_creation()
    test_chart_validation()
    
    if success:
        print("\n🎉 Chart functionality test completed successfully!")
        print("You can open the generated Excel file to verify the charts were created correctly.")
    else:
        print("\n❌ Chart functionality test failed!")
        sys.exit(1)
