#!/usr/bin/env python3
"""
Integration test for SlidesGeneratorAgent

This script tests the integration of the slides agent with the existing system.
"""

import sys
import asyncio
import json
from pathlib import Path

# Add the app directory to the Python path
sys.path.insert(0, str(Path(__file__).parent / "app"))

try:
    from app.agents.workers.slides_generator_agent import SlidesGeneratorAgent
    from app.utils.example_rag import get_slides_examples_text, get_slides_content_examples_text
    INTEGRATION_AVAILABLE = True
except ImportError as e:
    INTEGRATION_AVAILABLE = False
    print(f"⚠️  Integration dependencies not available: {e}")

def test_basic_functionality():
    """Test basic slides generator functionality without LLM calls."""
    print("🧪 Testing basic slides generator functionality...")
    
    try:
        # Test agent initialization
        agent = SlidesGeneratorAgent("test_chat_123")
        print("✅ Agent initialization successful")
        
        # Test markdown parsing
        test_markdown = """# Slide 1: Title Slide
## Test Presentation
### A sample presentation

---

# Slide 2: Content Slide
## Overview
- Point 1
- Point 2
  - Sub-point A
  - Sub-point B
- Point 3

<!-- Speaker notes: This is a test slide -->

---

# Slide 3: Section Header
## Thank You"""
        
        slides_data = agent._parse_markdown_slides(test_markdown)
        print(f"✅ Markdown parsing successful - parsed {len(slides_data)} slides")
        
        # Validate parsed data structure
        assert len(slides_data) == 3, f"Expected 3 slides, got {len(slides_data)}"
        assert slides_data[0]["layout_type"] == "title_slide", "First slide should be title_slide"
        assert slides_data[1]["layout_type"] == "title_content", "Second slide should be title_content"
        assert slides_data[2]["layout_type"] == "section_header", "Third slide should be section_header"
        print("✅ Slide data structure validation successful")
        
        # Test file saving methods
        test_outline = {
            "title": "Test Presentation",
            "subtitle": "Integration Test",
            "total_slides": 3,
            "theme": {
                "primary_color": "#1f4e79",
                "secondary_color": "#ffffff",
                "accent_color": "#ff6b35",
                "font_title": "Calibri",
                "font_body": "Calibri"
            }
        }
        
        outline_path = agent.save_outline_to_md_file("test_subtask", test_outline)
        content_path = agent.save_content_to_md_file("test_subtask", test_markdown)
        print("✅ File saving methods successful")
        
        # Clean up test files
        if outline_path.exists():
            outline_path.unlink()
        if content_path.exists():
            content_path.unlink()
        print("✅ Test cleanup successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Basic functionality test failed: {e}")
        return False

def test_rag_integration():
    """Test RAG system integration."""
    print("\n🧪 Testing RAG integration...")
    
    try:
        # Test slides examples retrieval
        examples_text = get_slides_examples_text("renewable energy presentation", k=2)
        print(f"✅ Slides examples retrieval successful - {len(examples_text)} chars")
        
        # Test slides content examples retrieval
        content_examples = get_slides_content_examples_text("AI presentation content", k=2)
        print(f"✅ Slides content examples retrieval successful - {len(content_examples)} chars")
        
        # Validate examples contain expected structure
        assert "Example 1:" in examples_text, "Examples should be numbered"
        assert "User Message:" in examples_text, "Examples should contain user message"
        assert "Expected Output:" in examples_text, "Examples should contain expected output"
        print("✅ RAG examples structure validation successful")
        
        return True
        
    except Exception as e:
        print(f"❌ RAG integration test failed: {e}")
        return False

def test_pptx_creation():
    """Test PowerPoint file creation."""
    print("\n🧪 Testing PPTX creation...")
    
    try:
        agent = SlidesGeneratorAgent("test_chat_456")
        
        # Test markdown content
        test_markdown = """# Slide 1: Title Slide
## Integration Test Presentation
### Testing PPTX Generation

---

# Slide 2: Content Slide
## Test Features
- Markdown parsing
- Slide creation
- Text formatting
- Theme application

---

# Slide 3: Section Header
## Test Complete"""
        
        # Test outline
        test_outline = {
            "title": "Integration Test Presentation",
            "subtitle": "Testing PPTX Generation",
            "total_slides": 3,
            "theme": {
                "primary_color": "#2e7d32",
                "secondary_color": "#ffffff",
                "accent_color": "#ff9800",
                "font_title": "Calibri",
                "font_body": "Calibri"
            }
        }
        
        # Create test PPTX file
        test_pptx_path = Path("test_slides.pptx")
        agent.create_pptx_from_markdown(test_markdown, test_pptx_path, test_outline)
        
        # Validate file was created
        assert test_pptx_path.exists(), "PPTX file should be created"
        assert test_pptx_path.stat().st_size > 0, "PPTX file should not be empty"
        print("✅ PPTX file creation successful")
        
        # Clean up test file
        if test_pptx_path.exists():
            test_pptx_path.unlink()
        print("✅ PPTX test cleanup successful")
        
        return True
        
    except Exception as e:
        print(f"❌ PPTX creation test failed: {e}")
        return False

async def test_full_integration():
    """Test full integration with mock task details."""
    print("\n🧪 Testing full integration (without LLM calls)...")
    
    try:
        agent = SlidesGeneratorAgent("test_chat_789")
        
        # Mock task details
        task_details = {
            "subtask_id": "test_integration",
            "subtask_description": "Create a test presentation about renewable energy",
            "user_input": "Create a presentation about renewable energy trends",
            "deps_info": "Test data about solar and wind energy developments"
        }
        
        # Test outline generation (this will use fallback due to no API keys in test)
        outline = await agent.determine_slides_outline(
            subtask_desc=task_details["subtask_description"],
            user_input=task_details["user_input"],
            deps_info=task_details["deps_info"]
        )
        
        print("✅ Outline generation successful (fallback)")
        assert "title" in outline, "Outline should contain title"
        assert "slides" in outline, "Outline should contain slides array"
        
        # Test content generation (this will use fallback)
        content = await agent.generate_slides_content(
            outline_plan=outline,
            subtask_desc=task_details["subtask_description"],
            user_input=task_details["user_input"],
            deps_info=task_details["deps_info"]
        )
        
        print("✅ Content generation successful (fallback)")
        assert len(content) > 0, "Content should not be empty"
        assert "# Slide" in content, "Content should contain slide markers"
        
        return True
        
    except Exception as e:
        print(f"❌ Full integration test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 Starting SlidesGeneratorAgent Integration Tests\n")
    
    if not INTEGRATION_AVAILABLE:
        print("❌ Cannot run tests - integration dependencies not available")
        return False
    
    tests = [
        test_basic_functionality,
        test_rag_integration,
        test_pptx_creation,
    ]
    
    async_tests = [
        test_full_integration,
    ]
    
    results = []
    
    # Run synchronous tests
    for test in tests:
        results.append(test())
    
    # Run asynchronous tests
    for test in async_tests:
        results.append(asyncio.run(test()))
    
    # Summary
    passed = sum(results)
    total = len(results)
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! SlidesGeneratorAgent integration is working correctly.")
        return True
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
