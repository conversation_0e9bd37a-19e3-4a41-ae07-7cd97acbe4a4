#!/usr/bin/env python3
"""
Test PPTX creation functionality
"""

import sys
from pathlib import Path

# Add the app directory to the Python path
sys.path.insert(0, str(Path(__file__).parent / "app"))

def test_basic_pptx_creation():
    """Test basic PPTX file creation."""
    try:
        from app.agents.workers.slides_generator_agent import SlidesGeneratorAgent
        
        agent = SlidesGeneratorAgent("test_chat")
        
        # Test markdown content
        test_markdown = """# Slide 1: Title Slide
## Test Presentation
### A Simple Test

---

# Slide 2: Content Slide
## Overview
- Point 1
- Point 2
- Point 3

---

# Slide 3: Section Header
## Thank You"""
        
        # Test outline
        test_outline = {
            "title": "Test Presentation",
            "subtitle": "A Simple Test",
            "total_slides": 3,
            "theme": {
                "primary_color": "#1f4e79",
                "secondary_color": "#ffffff",
                "accent_color": "#ff6b35",
                "font_title": "<PERSON><PERSON><PERSON>",
                "font_body": "<PERSON><PERSON>ri"
            }
        }
        
        # Create test PPTX file
        test_pptx_path = Path("test_slides_output.pptx")
        
        print("🔨 Creating PPTX file...")
        agent.create_pptx_from_markdown(test_markdown, test_pptx_path, test_outline)
        
        # Validate file was created
        if test_pptx_path.exists():
            file_size = test_pptx_path.stat().st_size
            print(f"✅ PPTX file created successfully: {file_size} bytes")
            
            # Try to open with python-pptx to validate format
            from pptx import Presentation
            try:
                prs = Presentation(str(test_pptx_path))
                slide_count = len(prs.slides)
                print(f"✅ PPTX file is valid with {slide_count} slides")
                
                # Clean up
                test_pptx_path.unlink()
                print("✅ Test file cleaned up")
                return True
                
            except Exception as validation_error:
                print(f"❌ PPTX file validation failed: {validation_error}")
                test_pptx_path.unlink()
                return False
        else:
            print("❌ PPTX file was not created")
            return False
            
    except Exception as e:
        print(f"❌ PPTX creation test failed: {e}")
        return False

def test_markdown_parsing():
    """Test markdown parsing functionality."""
    try:
        from app.agents.workers.slides_generator_agent import SlidesGeneratorAgent
        
        agent = SlidesGeneratorAgent("test_chat")
        
        test_markdown = """# Slide 1: Title Slide
## Main Title
### Subtitle

---

# Slide 2: Content Slide
## Content Title
- Bullet 1
- Bullet 2
  - Sub bullet
- Bullet 3

Plain paragraph text.

<!-- Speaker notes: This is a test note -->

---

# Slide 3: Section Header
## Section Title"""
        
        print("🔍 Testing markdown parsing...")
        slides_data = agent._parse_markdown_slides(test_markdown)
        
        print(f"✅ Parsed {len(slides_data)} slides")
        
        # Validate slide data
        expected_slides = 3
        if len(slides_data) == expected_slides:
            print("✅ Correct number of slides parsed")
            
            # Check first slide (title slide)
            first_slide = slides_data[0]
            if first_slide.get("layout_type") == "title_slide":
                print("✅ First slide correctly identified as title slide")
            else:
                print(f"❌ First slide layout incorrect: {first_slide.get('layout_type')}")
                
            # Check content slide
            second_slide = slides_data[1]
            content = second_slide.get("content", [])
            if len(content) > 0:
                print(f"✅ Second slide has {len(content)} content items")
            else:
                print("❌ Second slide has no content")
                
            return True
        else:
            print(f"❌ Expected {expected_slides} slides, got {len(slides_data)}")
            return False
            
    except Exception as e:
        print(f"❌ Markdown parsing test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 Testing PPTX Creation Functionality\n")
    
    tests = [
        test_markdown_parsing,
        test_basic_pptx_creation,
    ]
    
    results = []
    
    for test in tests:
        print(f"Running {test.__name__}...")
        results.append(test())
        print()
    
    # Summary
    passed = sum(results)
    total = len(results)
    
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! PPTX creation is working correctly.")
        return True
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
