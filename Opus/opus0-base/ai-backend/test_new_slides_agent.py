#!/usr/bin/env python3
# test_slides_agent.py
"""
Test script for the SlidesGeneratorAgent.

This script tests the basic functionality of the slides generator agent
without requiring a full system setup.
"""

import asyncio
import json
import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

async def test_outline_generation():
    """Test the outline generation functionality."""
    try:
        from app.agents.workers.slides_generator_agent import SlidesGeneratorAgent
        
        print("🧪 Testing outline generation...")
        
        # Create agent instance
        agent = SlidesGeneratorAgent("test_agent_id", "test_chat_id")
        
        # Test outline generation
        outline = await agent.determine_slides_outline(
            subtask_desc="Create a presentation about renewable energy",
            user_input="I need slides about solar and wind power innovations",
            deps_info="Research data on renewable energy trends"
        )
        
        print(f"✅ Outline generated successfully")
        print(f"   Title: {outline.get('title', 'N/A')}")
        print(f"   Total slides: {outline.get('total_slides', 'N/A')}")
        print(f"   Slides count: {len(outline.get('slides', []))}")
        
        return True, outline
        
    except Exception as e:
        print(f"❌ Outline generation failed: {e}")
        return False, None

async def test_style_template_generation(outline):
    """Test the style template generation functionality."""
    try:
        from app.agents.workers.slides_generator_agent import SlidesGeneratorAgent
        
        print("\n🎨 Testing style template generation...")
        
        # Create agent instance
        agent = SlidesGeneratorAgent("test_agent_id", "test_chat_id")
        
        # Test style template generation
        style_template = await agent.generate_style_template(
            outline_plan=outline,
            user_input="I need slides about solar and wind power innovations",
            subtask_desc="Create a presentation about renewable energy"
        )
        
        print(f"✅ Style template generated successfully")
        print(f"   Primary color: {style_template.get('global_theme', {}).get('primary_color', 'N/A')}")
        print(f"   Title font: {style_template.get('global_theme', {}).get('title_font', 'N/A')}")
        print(f"   Styled slides: {len(style_template.get('slides', []))}")
        
        return True, style_template
        
    except Exception as e:
        print(f"❌ Style template generation failed: {e}")
        return False, None

async def test_code_generation(outline, style_template):
    """Test the python-pptx code generation functionality."""
    try:
        from app.agents.workers.slides_generator_agent import SlidesGeneratorAgent
        
        print("\n💻 Testing python-pptx code generation...")
        
        # Create agent instance
        agent = SlidesGeneratorAgent("test_agent_id", "test_chat_id")
        
        # Test code generation
        pptx_code = await agent.generate_pptx_code(
            outline_plan=outline,
            style_template=style_template,
            user_input="I need slides about solar and wind power innovations"
        )
        
        print(f"✅ Python-PPTX code generated successfully")
        print(f"   Code length: {len(pptx_code)} characters")
        print(f"   Contains 'Presentation': {'Presentation' in pptx_code}")
        print(f"   Contains 'create_presentation': {'create_presentation' in pptx_code}")
        
        # Show a snippet of the generated code
        print("\n📝 Code snippet (first 300 chars):")
        print("-" * 50)
        print(pptx_code[:300] + "..." if len(pptx_code) > 300 else pptx_code)
        print("-" * 50)
        
        return True, pptx_code
        
    except Exception as e:
        print(f"❌ Code generation failed: {e}")
        return False, None

def test_json_parsing():
    """Test JSON parsing functionality."""
    try:
        from app.agents.workers.slides_generator_agent import SlidesGeneratorAgent
        
        print("\n🔍 Testing JSON parsing...")
        
        # Create agent instance
        agent = SlidesGeneratorAgent("test_agent_id", "test_chat_id")
        
        # Test JSON parsing with markdown code blocks
        test_json = '''```json
{
  "title": "Test Presentation",
  "total_slides": 3,
  "slides": [
    {
      "slide_number": 1,
      "layout_type": "title_slide",
      "title": "Test Title"
    }
  ]
}
```'''
        
        parsed = agent._parse_json_response(test_json, "test")
        
        print(f"✅ JSON parsing successful")
        print(f"   Parsed title: {parsed.get('title', 'N/A')}")
        print(f"   Parsed slides: {len(parsed.get('slides', []))}")
        
        return True
        
    except Exception as e:
        print(f"❌ JSON parsing failed: {e}")
        return False

async def main():
    """Main test function."""
    print("🚀 Testing SlidesGeneratorAgent\n")
    
    # Test JSON parsing
    json_success = test_json_parsing()
    
    if not json_success:
        print("\n❌ Basic JSON parsing failed, stopping tests")
        return False
    
    # Test outline generation
    outline_success, outline = await test_outline_generation()
    
    if not outline_success or not outline:
        print("\n❌ Outline generation failed, stopping tests")
        return False
    
    # Test style template generation
    style_success, style_template = await test_style_template_generation(outline)
    
    if not style_success or not style_template:
        print("\n❌ Style template generation failed, stopping tests")
        return False
    
    # Test code generation
    code_success, pptx_code = await test_code_generation(outline, style_template)
    
    if not code_success:
        print("\n❌ Code generation failed")
        return False
    
    print("\n🎉 All tests passed successfully!")
    print("\n📋 Test Summary:")
    print("   ✅ JSON parsing")
    print("   ✅ Outline generation")
    print("   ✅ Style template generation")
    print("   ✅ Python-PPTX code generation")
    
    return True

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        sys.exit(1)
