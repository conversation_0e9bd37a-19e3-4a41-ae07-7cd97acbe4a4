# RAG Systems Test Script

This test script allows you to manually inspect the outputs of all four RAG systems to verify that formatting is correct and retrieval is working as expected.

## 🎯 What It Tests

The script tests four RAG systems:

1. **PDF Outline Examples** (`get_pdf_examples_text`)
   - Used for generating PDF outlines/structure
   - Dataset: `app/rag_data/pdf_examples.jsonl`

2. **PDF Section Examples** (`get_pdf_section_examples_text`)
   - Used for generating HTML content sections
   - Dataset: `app/rag_data/pdf_section_examples.jsonl`

3. **PDF Head Examples** (`get_pdf_head_examples_text`)
   - Used for generating `<head>` sections with CSS/fonts
   - Dataset: `app/rag_data/pdf_head_examples.jsonl`

4. **Subtask Refiner Examples** (`get_subtask_refiner_examples_text`)
   - Used for refining and splitting subtasks based on completed subtask outputs
   - Dataset: `app/rag_data/subtask_refiner_examples.jsonl`

## 🚀 How to Run

### Basic Test Suite
```bash
cd /path/to/opus0-base/ai-backend
python test_rag_systems.py
```

This will run predefined test queries for all three systems and show you the formatted outputs.

### Interactive Mode
```bash
python test_rag_systems.py --interactive
```

This allows you to test custom queries interactively:
- `outline <query>` - Test PDF outline examples
- `section <query>` - Test PDF section examples
- `head <query>` - Test PDF head examples
- `refiner <query>` - Test subtask refiner examples
- `quit` - Exit

## 📋 What to Check

When reviewing the outputs, verify:

### 1. **Formatting Correctness**
- Are examples formatted exactly like the original hardcoded examples?
- Do JSON structures look correct?
- Are HTML/CSS snippets properly formatted?

### 2. **Retrieval Relevance**
- Are the most relevant examples being retrieved for each query?
- Do wedding queries return wedding examples?
- Do resume queries return CV examples?

### 3. **Dataset Integrity**
- Are all dataset files present?
- Do they contain the expected number of examples?
- Are there any JSON parsing errors?

## 🔍 Example Test Queries

### Outline Examples
- "create a wedding invitation"
- "build a resume for software engineer"
- "make a travel itinerary for Japan"

### Section Examples  
- "create a timeline section"
- "wedding invitation design"
- "resume work experience section"

### Head Examples
- "wedding poster with elegant fonts"
- "professional resume styling"
- "Japan travel itinerary with cultural theme"

### Subtask Refiner Examples
- "find AI professors at universities"
- "generate PDFs for multiple resumes"
- "create detailed travel itinerary"
- "analyze video content"
- "answer questions from image"

## 🐛 Troubleshooting

### Import Errors
If you get import errors, make sure:
1. You're running from the `ai-backend` directory
2. The `app` directory exists and contains the RAG modules
3. All required dependencies are installed

### Missing Dataset Files
If dataset files are missing:
1. Check that all `.jsonl` files exist in `app/rag_data/`
2. Verify the files contain valid JSON lines
3. Make sure file permissions allow reading

### Empty Results
If queries return no results:
1. Check that FAISS indexes are being built correctly
2. Verify the embedding service is working
3. Try simpler, more direct queries

## 📊 Expected Output Format

### PDF Outline Examples
```
## Example: Task Title

**User Input:** User's request
**Subtask Description:** Specific task breakdown  
**Dependencies Info:** Additional context
**Response:**
```json
{
  "title": "Document Title",
  "sections": [...]
}
```

### PDF Section Examples
```
## EXAMPLE TITLE

(given head:)
<head>...</head>

(output:)
<div>...</div>
```

### PDF Head Examples
```
#### EXAMPLE TITLE
<head>
  <meta charset="UTF-8" />
  ...
</head>
```

### Subtask Refiner Examples
```
**All Subtasks Data:**
```json
{
  "subtasks": [...]
}
```

**Completed Subtask Description:**
"Description of completed subtask"

**Completed Subtask Output:**
"Output from completed subtask"

**Pending Subtask Description:**
"Description of pending subtask"

**Possible Output:**:
```json
{
  "subtasks": [...]
}
```
(Reason: Explanation of why refinement was needed)
```

## ✅ Success Indicators

The test is successful if:
- ✅ All dataset files are found and readable
- ✅ Examples are retrieved for all test queries
- ✅ Formatting matches the original hardcoded examples exactly
- ✅ Most relevant examples are returned for each query type
- ✅ No JSON parsing or import errors occur

Happy testing! 🎉
