# NewSlidesGeneratorAgent Implementation

## Overview

The `NewSlidesGeneratorAgent` is a worker agent that implements the updated system design for PowerPoint presentation generation. It follows a clean three-step architecture: **Outline → Style Template → Python-PPTX Code Generation**.

## Architecture

### 1. **Outline Generation**
- Generates a structured JSON outline defining slide content and layout
- Uses RAG system with existing slides examples
- Validates outline structure and provides fallbacks

### 2. **Style Template Generation**
- Creates a JSON style template with visual styling information
- Defines colors, fonts, sizes for consistent presentation design
- Matches styling to presentation topic and user requirements

### 3. **Python-PPTX Code Generation**
- Generates executable Python code using python-pptx library
- Uses RAG system with curated python-pptx code examples
- Creates complete presentation with proper styling

### 4. **Code Execution**
- Safely executes generated code in subprocess
- Creates actual PPTX file with timeout protection
- Handles errors and provides detailed logging

## Key Features

- **Clean Separation of Concerns**: Content, styling, and implementation are handled separately
- **RAG-Powered**: Uses real examples for better code quality
- **Fallback Systems**: Robust error handling with fallback generation
- **Audit Trail**: Saves all intermediate files for debugging
- **Security**: Safe code execution with subprocess isolation

## File Structure

```
/sessions/{chat_id}/
├── knowledge_base/
│   ├── slides_outline_{subtask_id}_{timestamp}.md
│   ├── slides_style_template_{subtask_id}_{timestamp}.md
│   ├── slides_pptx_code_{subtask_id}_{timestamp}.py
│   └── slides_results_{subtask_id}_{timestamp}.md
└── docs/
    └── pptx_result_{subtask_id}_{timestamp}.pptx
```

## RAG Datasets

### 1. **Existing Slides Examples** (`slides_generator_examples.jsonl`)
- Used for outline generation
- Contains presentation structure examples
- Provides layout and content guidance

### 2. **New Python-PPTX Code Examples** (`pptx_code_examples.jsonl`)
- Used for code generation
- Contains curated python-pptx code snippets
- Demonstrates best practices and common patterns

## Implementation Details

### Class Structure
```python
class NewSlidesGeneratorAgent(BaseAgent):
    async def execute_task(task_details)
    async def determine_slides_outline(...)
    async def generate_style_template(...)
    async def generate_pptx_code(...)
    async def execute_pptx_code(...)
```

### Key Methods

#### Outline Generation
- `determine_slides_outline()`: Main outline generation method
- `_parse_json_response()`: Handles JSON parsing with error recovery
- `_validate_outline()`: Ensures outline structure is valid
- `_create_fallback_outline()`: Provides basic fallback outline

#### Style Template Generation
- `generate_style_template()`: Creates styling information
- `_validate_style_template()`: Validates template structure
- `_create_fallback_style_template()`: Provides basic styling fallback

#### Code Generation & Execution
- `generate_pptx_code()`: Generates python-pptx code using RAG
- `_clean_python_code()`: Cleans up generated code
- `_validate_python_code()`: Basic code validation
- `execute_pptx_code()`: Safe code execution in subprocess

#### File Management
- `save_outline_to_md_file()`: Saves outline for audit
- `save_style_template_to_md_file()`: Saves style template
- `save_pptx_code_to_file()`: Saves generated code
- `save_combined_results_to_md_file()`: Creates final results

## Usage

### Integration
The agent follows the same patterns as existing document generators:
- Inherits from `BaseAgent`
- Uses standard communication patterns
- Follows project coding conventions
- Integrates with R2 storage and presigned URLs

### Task Execution
```python
agent = NewSlidesGeneratorAgent(chat_id)
await agent.execute_task({
    "subtask_id": "slides_001",
    "subtask_description": "Create presentation about AI",
    "user_input": "I need slides about machine learning",
    "deps_info": "Research data on AI trends"
})
```

## Error Handling

### Robust Fallbacks
- JSON parsing errors → Fallback outline/template
- LLM generation failures → Basic templates
- Code execution errors → Detailed error logging
- File system errors → Graceful degradation

### Logging
- Comprehensive logging at all stages
- Token counting for LLM calls
- Performance timing
- Error details with context

## Testing

### Test Scripts
- `test_new_slides_agent.py`: Comprehensive functionality testing
- `build_pptx_rag_index.py`: RAG index building and testing

### Test Coverage
- JSON parsing and validation
- Outline generation with fallbacks
- Style template creation
- Code generation and validation
- Error handling scenarios

## Security Considerations

### Safe Code Execution
- Subprocess isolation
- Timeout protection (60 seconds)
- Working directory restrictions
- Error capture and logging

### Input Validation
- JSON structure validation
- Code content validation
- File path sanitization
- Resource usage limits

## Performance

### Optimizations
- Efficient RAG retrieval (k=3-5 examples)
- Streaming LLM responses where appropriate
- Minimal memory footprint
- Fast subprocess execution

### Monitoring
- Token usage tracking
- Execution time measurement
- File size monitoring
- Error rate tracking

## Future Enhancements

### Planned Features
- Image insertion support
- Chart and graph generation
- Advanced layout options
- Template customization
- Batch processing

### Extensibility
- Modular design for easy feature addition
- RAG system can be expanded with more examples
- Style templates can support more properties
- Code generation can handle complex layouts

## Dependencies

### Required Packages
- `python-pptx`: PowerPoint file generation
- `langchain`: LLM integration
- `faiss`: RAG vector search
- Standard library: `subprocess`, `tempfile`, `json`

### Environment
- Google Gemini API access
- R2 storage configuration
- File system permissions
- Python 3.8+ compatibility

## Maintenance

### Regular Tasks
- Update RAG examples with new patterns
- Monitor generation quality
- Review error logs
- Performance optimization

### Monitoring Points
- Generation success rates
- Code execution failures
- File upload issues
- User satisfaction metrics
