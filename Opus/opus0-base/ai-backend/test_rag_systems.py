#!/usr/bin/env python3
"""
Test script for PDF RAG systems.

This script allows manual inspection of the RAG outputs for:
1. PDF outline examples (get_pdf_examples_text)
2. PDF section examples (get_pdf_section_examples_text) 
3. PDF head examples (get_pdf_head_examples_text)

Usage:
    python test_rag_systems.py
"""

import sys
import os
from pathlib import Path

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "app"))

try:
    from app.utils.example_rag import (
        get_pdf_examples_text,
        get_pdf_section_examples_text,
        get_pdf_head_examples_text,
        get_subtask_refiner_examples_text
    )
    print("✅ Successfully imported RAG functions")
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure you're running this from the ai-backend directory")
    sys.exit(1)


def print_separator(title: str):
    """Print a nice separator with title."""
    print("\n" + "="*80)
    print(f" {title} ".center(80, "="))
    print("="*80)


def print_subsection(title: str):
    """Print a subsection separator."""
    print(f"\n{'-'*60}")
    print(f" {title} ")
    print(f"{'-'*60}")


def test_pdf_outline_examples():
    """Test PDF outline examples retrieval."""
    print_separator("PDF OUTLINE EXAMPLES TEST")
    
    test_queries = [
        "create a wedding invitation",
        "build a resume for software engineer", 
        "make a travel itinerary for Japan",
        "write an article about technology",
        "design a timeline for project milestones"
    ]
    
    for i, query in enumerate(test_queries, 1):
        print_subsection(f"Query {i}: '{query}'")
        try:
            result = get_pdf_examples_text(query, k=2)
            if result:
                print(result)
            else:
                print("❌ No results returned")
        except Exception as e:
            print(f"❌ Error: {e}")


def test_pdf_section_examples():
    """Test PDF section examples retrieval."""
    print_separator("PDF SECTION EXAMPLES TEST")
    
    test_queries = [
        "create a timeline section",
        "wedding invitation design",
        "resume work experience section",
        "travel itinerary with budget",
        "article with two columns"
    ]
    
    for i, query in enumerate(test_queries, 1):
        print_subsection(f"Query {i}: '{query}'")
        try:
            result = get_pdf_section_examples_text(query, k=2)
            if result:
                print(result)
            else:
                print("❌ No results returned")
        except Exception as e:
            print(f"❌ Error: {e}")


def test_pdf_head_examples():
    """Test PDF head examples retrieval."""
    print_separator("PDF HEAD EXAMPLES TEST")

    test_queries = [
        "wedding poster with elegant fonts",
        "professional resume styling",
        "Japan travel itinerary with cultural theme",
        "article with serif typography",
        "modern document design"
    ]

    for i, query in enumerate(test_queries, 1):
        print_subsection(f"Query {i}: '{query}'")
        try:
            result = get_pdf_head_examples_text(query, k=2)
            if result:
                print(result)
            else:
                print("❌ No results returned")
        except Exception as e:
            print(f"❌ Error: {e}")


def test_subtask_refiner_examples():
    """Test subtask refiner examples retrieval."""
    print_separator("SUBTASK REFINER EXAMPLES TEST")

    test_queries = [
        "find AI professors at universities",
        "generate PDFs for multiple resumes",
        "create detailed travel itinerary",
        "analyze video content",
        "answer questions from image",
        "summarize document sections",
        "create PDF report with mixed content"
    ]

    for i, query in enumerate(test_queries, 1):
        print_subsection(f"Query {i}: '{query}'")
        try:
            result = get_subtask_refiner_examples_text(query, k=2)
            if result:
                print(result)
            else:
                print("❌ No results returned")
        except Exception as e:
            print(f"❌ Error: {e}")


def test_all_systems():
    """Run all RAG system tests."""
    print("🚀 Starting RAG Systems Test Suite")
    print("This will test all four RAG systems with various queries.")
    print("Inspect the outputs to verify formatting and relevance.")

    try:
        test_pdf_outline_examples()
        test_pdf_section_examples()
        test_pdf_head_examples()
        test_subtask_refiner_examples()

        print_separator("TEST SUITE COMPLETED")
        print("✅ All tests completed successfully!")
        print("\n📋 What to check:")
        print("1. Are examples properly formatted?")
        print("2. Are the most relevant examples being retrieved?")
        print("3. Do the outputs match the original prompt formats?")
        print("4. Are there any JSON parsing errors?")

    except Exception as e:
        print(f"\n❌ Test suite failed with error: {e}")
        return False

    return True


def interactive_test():
    """Interactive test mode for custom queries."""
    print_separator("INTERACTIVE TEST MODE")
    print("Enter custom queries to test the RAG systems.")
    print("Type 'quit' to exit, 'help' for commands.")
    
    while True:
        print("\nAvailable commands:")
        print("  outline <query>   - Test PDF outline examples")
        print("  section <query>   - Test PDF section examples")
        print("  head <query>      - Test PDF head examples")
        print("  refiner <query>   - Test subtask refiner examples")
        print("  quit             - Exit interactive mode")
        
        try:
            user_input = input("\n> ").strip()
            
            if user_input.lower() == 'quit':
                break
            elif user_input.lower() == 'help':
                continue
            elif user_input.startswith('outline '):
                query = user_input[8:]
                result = get_pdf_examples_text(query, k=2)
                print(f"\n📋 Outline examples for: '{query}'")
                print(result if result else "❌ No results")
            elif user_input.startswith('section '):
                query = user_input[8:]
                result = get_pdf_section_examples_text(query, k=2)
                print(f"\n🎨 Section examples for: '{query}'")
                print(result if result else "❌ No results")
            elif user_input.startswith('head '):
                query = user_input[5:]
                result = get_pdf_head_examples_text(query, k=2)
                print(f"\n📄 Head examples for: '{query}'")
                print(result if result else "❌ No results")
            elif user_input.startswith('refiner '):
                query = user_input[8:]
                result = get_subtask_refiner_examples_text(query, k=2)
                print(f"\n🔧 Subtask refiner examples for: '{query}'")
                print(result if result else "❌ No results")
            else:
                print("❌ Invalid command. Type 'help' for available commands.")
                
        except KeyboardInterrupt:
            print("\n👋 Exiting interactive mode...")
            break
        except Exception as e:
            print(f"❌ Error: {e}")


def check_datasets():
    """Check if all dataset files exist."""
    print_separator("DATASET FILES CHECK")

    datasets = [
        ("PDF Examples", "app/rag_data/pdf_examples.jsonl"),
        ("PDF Section Examples", "app/rag_data/pdf_section_examples.jsonl"),
        ("PDF Head Examples", "app/rag_data/pdf_head_examples.jsonl"),
        ("Subtask Refiner Examples", "app/rag_data/subtask_refiner_examples.jsonl")
    ]

    all_exist = True
    for name, path in datasets:
        file_path = Path(path)
        if file_path.exists():
            size = file_path.stat().st_size
            print(f"✅ {name}: {path} ({size} bytes)")
        else:
            print(f"❌ {name}: {path} (NOT FOUND)")
            all_exist = False

    return all_exist


def count_examples():
    """Count examples in each dataset."""
    print_separator("EXAMPLE COUNTS")

    datasets = [
        ("PDF Examples", "app/rag_data/pdf_examples.jsonl"),
        ("PDF Section Examples", "app/rag_data/pdf_section_examples.jsonl"),
        ("PDF Head Examples", "app/rag_data/pdf_head_examples.jsonl"),
        ("Subtask Refiner Examples", "app/rag_data/subtask_refiner_examples.jsonl")
    ]

    for name, path in datasets:
        file_path = Path(path)
        if file_path.exists():
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    count = sum(1 for line in f if line.strip())
                print(f"📊 {name}: {count} examples")
            except Exception as e:
                print(f"❌ {name}: Error reading file - {e}")
        else:
            print(f"❌ {name}: File not found")


if __name__ == "__main__":
    print("🧪 PDF RAG Systems Test Script")
    print("=" * 50)

    # Check if datasets exist first
    if not check_datasets():
        print("\n❌ Some dataset files are missing. Please create them first.")
        sys.exit(1)

    count_examples()

    if len(sys.argv) > 1 and sys.argv[1] == "--interactive":
        interactive_test()
    else:
        success = test_all_systems()

        print(f"\n🎯 Want to test custom queries? Run:")
        print(f"   python {sys.argv[0]} --interactive")

        sys.exit(0 if success else 1)
