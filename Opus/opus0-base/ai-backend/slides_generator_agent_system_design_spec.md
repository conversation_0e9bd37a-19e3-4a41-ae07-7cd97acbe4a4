# 🎯 System Design Doc – SlidesGeneratorAgent

## 1 · Goal
Build a worker agent that converts LLM-generated structured data into a styled .pptx slideshow file. It must return a presigned download link, mirroring PDFGeneratorAgent, DOCXGeneratorAgent, and XLSXGeneratorAgent behavior.

## 2 · Why Markdown → python-pptx?

| Need | Pure python-pptx | Markdown → python-pptx (chosen) |
|------|------------------|----------------------------------|
| LLM simplicity | Requires verbose code | ✅ LLMs natively emit Markdown |
| Human audit trail | None | ✅ Readable .md stored & reusable |
| PowerPoint styling power | ✅ | ✅ (via mapping) |
| Slide structure clarity | Complex nested objects | ✅ Clear slide-based format |
| Parsing reliability | Manual slide manipulation | ✅ Simple regex parsing |

## 3 · File & Class

**File:** `app/agents/workers/slides_generator_agent.py`  
**Class:** `SlidesGeneratorAgent(BaseAgent)`

Follow the exact coding conventions used by DOCXGeneratorAgent (file header, separators, logging, error handling).

## 4 · Execution Flow

1. Generate **slides outline** via <PERSON><PERSON>hain prompt (title + slide structure as JSON)
2. Generate **slides content** via LangChain prompt (complete slide data as Markdown)
3. Save both outline and content as `.md` in the knowledge-base folder
4. Parse Markdown data:
   - `---` → Slide separators
   - `# Title` → Slide titles
   - `## Subtitle` → Slide subtitles
   - `- Bullet` → Bullet points
   - Plain text → Paragraph content
5. Build `.pptx` with `python-pptx`, mapping parsed elements to PowerPoint features
6. Save to `/sessions/{chat_id}/docs/slides_result_<subtask>_<timestamp>.pptx`
7. Upload to R2 and obtain a 24-h presigned link
8. Report results via `send_message()` including the link

## 5 · MVP Scope

**Supported:**
- Multiple slide layouts (title, content, bullet points)
- Text formatting (titles, subtitles, bullet points, body text)
- Basic styling (fonts, colors, alignment)
- Speaker notes
- Master slide themes

**Deferred:**
- Image insertion and positioning
- Charts and graphs (bar, line, pie)
- Slide transitions and animations
- Two-column layouts with images
- Advanced animations and transitions
- Embedded videos
- Interactive elements
- Custom slide masters
- Advanced chart types
- SmartArt graphics
- Collaboration features

## 6 · Technical Implementation

### 6.1 Dependencies
- **python-pptx**: Primary PowerPoint library
- **datetime**: For timestamps
- **pathlib**: For file path handling

### 6.2 Core Methods

```python
class SlidesGeneratorAgent(BaseAgent):
    async def execute_task(task_details: Dict[str, Any])
    async def determine_slides_outline(subtask_desc, user_input, deps_info) -> Dict[str, Any]
    async def generate_slides_content(outline_plan, subtask_desc, user_input, deps_info) -> str
    def create_pptx_from_markdown(markdown_content: str, pptx_path: Path)
    def _parse_markdown_slides(markdown_content: str) -> List[Dict[str, Any]]
    def _create_slide_from_data(presentation, slide_data: Dict[str, Any])
    def _apply_slide_layout(slide, layout_type: str) -> Any
    def _add_text_to_slide(slide, text_content: str, is_title: bool = False)
    def _apply_theme_styling(presentation, theme_config: Dict[str, Any])
    def save_outline_to_md_file(subtask_id: str, outline_plan: Dict[str, Any])
    def save_content_to_md_file(subtask_id: str, content: str)
    def report_results(results: Dict[str, Any])
```

### 6.3 RAG Integration
Follow the same pattern as PDF, DOCX, and XLSX agents:
- **Dataset:** `slides_generator_examples.jsonl`
- **Index:** `slides_generator_index`
- **Functions:** `get_slides_examples_text()`, `get_slides_content_examples_text()`
- **Formatter:** `slides_example_to_text()`, `slides_content_example_to_text()`

### 6.4 File Structure
```
/sessions/{chat_id}/
├── knowledge_base/
│   ├── slides_outline_{subtask_id}_{timestamp}.md
│   └── slides_content_{subtask_id}_{timestamp}.md
└── docs/
    └── slides_result_{subtask_id}_{timestamp}.pptx
```

## 7 · Prompt Design

### 7.1 Outline Prompt
Generate JSON structure defining:
- Presentation title and metadata
- Slide count and sequence
- Slide layout types for each slide (title, content, bullet_points)
- Content themes and styling preferences

### 7.2 Content Prompt
Generate Markdown with:
- Complete slide content in Markdown format
- `---` separators between slides
- `#` for slide titles, `##` for subtitles
- `-` for bullet points and lists
- Plain text for paragraph content
- Speaker notes in comments

## 8 · Error Handling & Validation

- **Markdown validation**: Ensure valid Markdown structure and required elements
- **Slide parsing**: Handle malformed slide separators gracefully
- **Text validation**: Handle missing or malformed text content gracefully
- **Memory management**: Handle large presentations efficiently
- **Graceful degradation**: Skip unsupported features, log warnings
- **Content validation**: Validate slide content against expected structure

## 9 · Integration Points

### 9.1 R2 Upload
- **Content-Type**: `application/vnd.openxmlformats-presentationml.presentation`
- **File naming**: `slides_result_{subtask_id}_{timestamp}.pptx`
- **Presigned URL**: 24-hour expiration

### 9.2 Directory Structure
- Use `get_public_docx_dir()` pattern → create `get_public_slides_dir()`
- Follow same session-based organization

### 9.3 Constants Update
Add to `app/utils/constants.py`:
```python
def get_public_slides_dir(chat_id: str) -> Path:
    """Return the public_slides folder for ``chat_id``."""
    return get_session_dir(chat_id) / "docs"
```

## 10 · Deliverables

1. **`slides_generator_agent.py`** - Production-ready, logged, error-handled
2. **RAG dataset** - `app/rag_data/slides_generator_examples.jsonl`
3. **RAG integration** - Update `app/utils/example_rag.py`
4. **Constants update** - Add slides directory helper
5. **Upload support** - Update allowed extensions to include "pptx"
6. **Tests** - Unit tests for slide creation and PowerPoint generation

## 11 · Slide Layout Types

### 11.1 Supported Layouts
- **title_slide**: Title and subtitle with optional background
- **title_content**: Title with bullet points or paragraphs
- **bullet_points**: Multi-level bullet point lists
- **section_header**: Section divider with large title

### 11.2 Markdown Structure Example
```markdown
# Slide 1: Title Slide
## My Presentation Title
### Subtitle goes here

---

# Slide 2: Content Slide
## Main Topic
- Bullet point 1
- Bullet point 2
- Bullet point 3

---

# Slide 3: Section Header
## New Section
```

## 12 · Theme and Styling

### 12.1 Theme Configuration
- **Color schemes**: Primary, secondary, accent colors
- **Font families**: Title, body, and accent fonts
- **Background styles**: Solid colors, gradients, images
- **Layout spacing**: Margins, padding, element positioning

### 12.2 Style Inheritance
- **Master slide**: Define global styles and layouts
- **Slide-level**: Override specific styling per slide
- **Element-level**: Fine-grained control over individual elements

## 13 · Success Criteria

- ✅ Generates valid .pptx files that open in PowerPoint/Google Slides
- ✅ Handles multiple slide layouts correctly
- ✅ Preserves text formatting and basic styling
- ✅ Follows exact same patterns as PDF/DOCX/XLSX agents
- ✅ Integrates with existing RAG system
- ✅ Provides reliable R2 upload and presigned URLs
- ✅ Maintains consistent error handling and logging
- ✅ Implements clean text-based layout system for different content types
