#!/usr/bin/env python3
# test_slides_fixes.py
"""
Quick test to verify the NewSlidesGeneratorAgent fixes work correctly.
"""

import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_import():
    """Test that the agent can be imported without errors."""
    try:
        from app.agents.workers.new_slides_generator_agent import NewSlidesGeneratorAgent
        print("✅ Import successful")
        return True
    except Exception as e:
        print(f"❌ Import failed: {e}")
        return False

def test_instantiation():
    """Test that the agent can be instantiated."""
    try:
        from app.agents.workers.new_slides_generator_agent import NewSlidesGeneratorAgent
        agent = NewSlidesGeneratorAgent("test_agent_id", "test_chat_id")
        print("✅ Instantiation successful")
        print(f"   Agent ID: {agent.agent_id}")
        print(f"   Chat ID: {agent.chat_id}")
        return True
    except Exception as e:
        print(f"❌ Instantiation failed: {e}")
        return False

def test_methods_exist():
    """Test that required methods exist."""
    try:
        from app.agents.workers.new_slides_generator_agent import NewSlidesGeneratorAgent
        agent = NewSlidesGeneratorAgent("test_agent_id", "test_chat_id")
        
        # Check if _invoke_chain method exists
        if hasattr(agent, '_invoke_chain'):
            print("✅ _invoke_chain method exists")
        else:
            print("❌ _invoke_chain method missing")
            return False
            
        # Check if execute_task method exists
        if hasattr(agent, 'execute_task'):
            print("✅ execute_task method exists")
        else:
            print("❌ execute_task method missing")
            return False
            
        return True
    except Exception as e:
        print(f"❌ Method check failed: {e}")
        return False

def test_send_message_import():
    """Test that send_message can be imported correctly."""
    try:
        from app.utils.communication import send_message
        print("✅ send_message import successful")
        
        # Check function signature
        import inspect
        sig = inspect.signature(send_message)
        params = list(sig.parameters.keys())
        expected_params = ['chat_id', 'sender', 'receiver', 'message']
        
        if params == expected_params:
            print("✅ send_message signature correct")
            return True
        else:
            print(f"❌ send_message signature incorrect. Expected: {expected_params}, Got: {params}")
            return False
            
    except Exception as e:
        print(f"❌ send_message import failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🧪 Testing NewSlidesGeneratorAgent Fixes\n")
    
    tests = [
        ("Import Test", test_import),
        ("Instantiation Test", test_instantiation),
        ("Methods Exist Test", test_methods_exist),
        ("send_message Import Test", test_send_message_import),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}:")
        if test_func():
            passed += 1
        else:
            print(f"   Test failed!")
    
    print(f"\n📊 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The fixes are working correctly.")
        return True
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
