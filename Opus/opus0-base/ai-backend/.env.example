# ----------------------------------------
# Example .env File
# ----------------------------------------

# OpenAI API Key
# e.g. sk-XXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
OPENAI_API_KEY=your_openai_api_key_here

# Anthropic API Key
# e.g. sk-YYYYYYYYYYYYYYYYYYYYYYYYYYYYYY
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Google API Key
# e.g. sk-ZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZ
GOOGLE_API_KEY=your_google_api_key_here

# DeepSeek API Key
# e.g. sk-ZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZ
DEEPSEEK_API_KEY=your_deepseek_api_key_here

# ----------------------------------------
# Additional Services (Code Interpreter & Internet Search)
# ----------------------------------------

# E2B (Example-to-Bullet) API Key
# e.g. e2b-AAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
E2B_API_KEY=your_e2b_api_key_here

# Tavily API Key
# e.g. tvly-BBBBBBBBBBBBBBBBBBBBBBBBBBBBBB
TAVILY_API_KEY=your_tavily_api_key_here

# (Optional) Google Custom Search API Key
# Uncomment if using Google Search
# GOOGLE_SEARCH_API=your_google_search_api_key_here

# Brave Search API Key
# e.g. BSA-CCCCCCCCCCCCCCCCCCCCCCCCCCCCCC
BRAVE_SEARCH_API=your_brave_search_api_key_here

# Mistral API Key (if using Mistral models)
MISTRAL_API_KEY=your_mistral_api_key_here

# ----------------------------------------
# Scraper & Retriever Configurations
# ----------------------------------------

# SCRAPER: choose between "bs" for BeautifulSoup or "browser" for Selenium-based dynamic scraping
SCRAPER=bs

# RETRIEVER: choose your search engine retriever (e.g., brave, bing, google)
RETRIEVER=brave

# ----------------------------------------
# Frontend & Backend Configuration
# ----------------------------------------

# Frontend URL (where your frontend is served)
# e.g. http://localhost:5173
FRONTEND_URL=http://localhost:5173

# Backend port (what your backend listens on)
# e.g. 8000
PORT=8000

# Enable or disable debug mode (True or False)
DEBUG=True

# ----------------------------------------
# MongoDB Configuration
# ----------------------------------------

# MongoDB Username
MONGODB_USERNAME=your_mongodb_username

# MongoDB Password
# If it contains special characters, ensure proper quoting or URL-encoding when used in connection strings
MONGODB_PASSWORD=your_mongodb_password

# MongoDB Host (Atlas cluster URI or local host)
# e.g. cluster0.abcd123.mongodb.net
MONGODB_HOST=cluster0.example.mongodb.net

# MongoDB Database Name
MONGODB_DATABASE=your_database_name

# ----------------------------------------
# API URL (Backend endpoint for client to call)
# ----------------------------------------

# e.g. http://localhost:8000
API_URL=http://localhost:8000

# ----------------------------------------
# Cloudflare R2 Credentials & Bucket Config
# ----------------------------------------

# R2 Account ID (Cloudflare account identifier)
R2_ACCOUNT_ID=your_r2_account_id

# R2 Endpoint URL
# e.g. https://<account_id>.r2.cloudflarestorage.com
R2_ENDPOINT=https://account_id.r2.cloudflarestorage.com

# R2 Bucket Name
R2_BUCKET_NAME=your_r2_bucket_name

# R2 Access Key ID
R2_ACCESS_KEY_ID=your_r2_access_key_id

# R2 Secret Access Key
R2_SECRET_ACCESS_KEY=your_r2_secret_access_key

# ----------------------------------------
#  - Use this file as a template; rename to “.env” and fill in real values.


# Google OAuth
GOOGLE_OAUTH_CLIENT_ID=google-auth-clientID
GOOGLE_OAUTH_CLIENT_SECRET=google-auth-client-secret
