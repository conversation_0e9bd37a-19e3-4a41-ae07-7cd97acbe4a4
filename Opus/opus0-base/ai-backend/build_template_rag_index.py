#!/usr/bin/env python3
"""
Script: build_template_rag_index.py

Build FAISS index for template selection examples.
This script creates a searchable index of template selection examples
to enable intelligent template recommendation based on user input.
"""

import sys
import logging
from pathlib import Path

# Add the app directory to the Python path
sys.path.insert(0, str(Path(__file__).parent / "app"))

from app.utils.example_rag import build_index
from app.utils.constants import BASE_DIR

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def main():
    """
    Build the template selection RAG index.
    
    This function:
    1. Loads template selection examples from JSONL file
    2. Creates embeddings for each example
    3. Builds and saves a FAISS index for fast similarity search
    """
    try:
        logger.info("Starting template selection RAG index build...")
        
        # Verify template examples file exists
        examples_file = BASE_DIR / "app" / "rag_data" / "template_examples.jsonl"
        
        if not examples_file.exists():
            logger.error(f"Template examples file not found: {examples_file}")
            return False
        
        # Count examples
        example_count = 0
        with open(examples_file, 'r', encoding='utf-8') as f:
            for line in f:
                if line.strip():
                    example_count += 1
        
        logger.info(f"Found {example_count} template selection examples")
        
        if example_count == 0:
            logger.error("No template examples found in file")
            return False
        
        # Build the template selection index
        logger.info("Building FAISS index for template selection...")
        build_index("template_selection")
        logger.info("✅ Template selection index built successfully!")

        # Build the template metadata index for discovery
        logger.info("Building FAISS index for template metadata...")
        build_index("template_metadata")
        logger.info("✅ Template metadata index built successfully!")

        logger.info("🎯 Template system ready!")
        logger.info("   - Template selection: Intelligent template recommendation")
        logger.info("   - Template discovery: RAG-based template finding")
        logger.info("   - Scalable: Supports hundreds of templates")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to build template selection index: {e}")
        import traceback
        traceback.print_exc()
        return False


def verify_index():
    """
    Verify that the template selection index was built correctly.
    
    Returns:
        bool: True if index is valid, False otherwise
    """
    try:
        from app.utils.example_rag import get_template_examples_text
        
        # Test the index with a sample query
        test_query = "Create a business presentation for investors"
        examples = get_template_examples_text(test_query, k=2)
        
        if examples and len(examples) > 0:
            logger.info("✅ Template selection index verification passed")
            logger.info(f"Sample query result: {examples[:100]}...")
            return True
        else:
            logger.error("❌ Template selection index verification failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ Index verification failed: {e}")
        return False


if __name__ == "__main__":
    success = main()
    
    if success:
        logger.info("Verifying the built index...")
        verify_index()
    else:
        logger.error("Index build failed")
        exit(1)
