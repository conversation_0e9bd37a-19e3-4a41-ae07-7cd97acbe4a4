📄 System Design Doc – DOCXGeneratorAgent
1 · Goal
Build a worker agent that converts LLM‑generated Markdown into a styled .docx file (no images in v‑0). It must return a presigned download link, mirroring PDFGeneratorAgent behaviour.

2 · Why Markdown → python-docx?
Need

Pure python-docx

Markdown → python-docx (chosen)

LLM simplicity

Requires verbose JSON / code

✅ LLMs natively emit Markdown

Human audit trail

None

✅ Readable .md stored & reusable

DOCX styling power

✅

✅ (via mapping)

3 · File & Class


app/agents/workers/docx_generator_agent.py
class DOCXGeneratorAgent(BaseAgent)
Follow the exact coding conventions used by PDFGeneratorAgent (file header, separators, logging).

4 · Execution Flow


1. Generate **Markdown outline** via <PERSON><PERSON><PERSON><PERSON> prompt (title + section headings)
2. Generate **Markdown section content** for each outline heading
3. Save both outline and content as `.md` in the knowledge‑base folder
4. Parse Markdown:
     #  → Heading1      ## → Heading2
     - / 1. → ListBullet / ListNumber
     plain text → Paragraph
5. Build `.docx` with `python-docx`, mapping parsed elements to DOCX styles
6. Save to /sessions/{chat_id}/docs/docx_result_<subtask>_<timestamp>.docx
7. Upload to R2 and obtain a 24‑h presigned link
8. Report results via `send_message()` including the link
5 · MVP Scope
Supported: headings, paragraphs, bullet & numbered lists, (optional) tables.

Deferred: images, headers/footers, page numbers, advanced styles.

6 · Deliverables
docx_generator_agent.py (production‑ready, logged, error‑handled)