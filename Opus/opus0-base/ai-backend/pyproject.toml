[tool.poetry]
name = "ai-backend"
version = "0.1.0"
description = ""
authors = ["garv-2501 <<EMAIL>>"]
readme = "README.md"

packages = [{ include = "app" }]
[tool.poetry.dependencies]
python = ">=3.10,<3.14"
python-socketio = {extras = ["asyncio"], version = "^5.11.4"}
fastapi = "^0.115.0"
uvicorn = "^0.30.6"
langchain = "^0.3.1"
langchain-openai = "^0.3.9"
pydantic-settings = "^2.5.2"
python-dotenv = "^1.0.1"
loguru = "^0.7.2"
pymongo = {extras = ["srv"], version = "^4.10.0"}
langchain-community = "^0.3.1"
langchain-anthropic = "^0.2.3"
langchain-google-genai = "^2.0.1"
e2b-code-interpreter = "^1.0.1"
gpt-researcher = "^0.10.2"
openai = "^1.59.3"
httpx = "^0.28.1"
rich = "^13.9.4"
crawl4ai = "^0.5.0.post4"
langchain-deepseek = "^0.1.3"
playwright = "^1.51.0"
google-genai = "^1.13.0"
google-generativeai = "^0.8.5"
yt-dlp = "^2025.4.30"
youtube-transcript-api = "^1.0.3"
tavily-python = "^0.7.2"
langchain-core = "^0.3.60"
boto3 = "^1.38.22"
mistralai = "^1.7.1"
pandas = "^2.2.3"
pymupdf = "^1.26.0"
python-docx = "^1.1.2"
python-pptx = "^1.0.2"
tabulate = "^0.9.0"
faiss-cpu = "^1.7.2"
numpy = "<2.0"
openpyxl = "^3.1.5"


[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
