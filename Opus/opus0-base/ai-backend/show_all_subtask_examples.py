#!/usr/bin/env python3
"""
Script to display all formatted subtask refiner examples for manual verification.
"""

import json
from pathlib import Path

def format_subtask_example(example: dict) -> str:
    """Format a subtask refiner example exactly like the original prompt format."""
    all_subtasks_data = example.get("all_subtasks_data", {})
    completed_subtask_desc = example.get("completed_subtask_desc", "")
    completed_subtask_output = example.get("completed_subtask_output", "")
    pending_subtask_desc = example.get("pending_subtask_desc", "")
    response = example.get("response", {})
    reasoning = example.get("reasoning", "")

    formatted_example = f"""**All Subtasks Data:** 
```json
{json.dumps(all_subtasks_data, indent=4, ensure_ascii=False)}
```

**Completed Subtask Description:** 
"{completed_subtask_desc}"

**Completed Subtask Output:** 
"{completed_subtask_output}"

**Pending Subtask Description:** 
"{pending_subtask_desc}"

**Possible Output:**: 
```json
{json.dumps(response, indent=4, ensure_ascii=False)}
```
(Reason "Not to be added in the final output": {reasoning})"""

    return formatted_example

def main():
    """Display all subtask refiner examples."""
    print("🔍 ALL SUBTASK REFINER EXAMPLES - FORMATTED OUTPUT")
    print("=" * 80)
    print("This shows how all 7 examples will appear in the RAG prompt.")
    print("=" * 80)
    
    # Load examples
    examples_file = Path("app/rag_data/subtask_refiner_examples.jsonl")
    if not examples_file.exists():
        print("❌ Subtask refiner examples file not found")
        return 1
    
    examples = []
    with open(examples_file, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line:
                examples.append(json.loads(line))
    
    print(f"\n📊 Found {len(examples)} examples in dataset\n")
    
    # Display all examples
    for i, example in enumerate(examples, 1):
        print(f"\n{'='*80}")
        print(f"### **Example {i}:**")
        print(f"Task: {example.get('task', 'Unknown')}")
        print(f"Type: {example.get('document_type', 'Unknown')}")
        print(f"{'='*80}")
        
        formatted = format_subtask_example(example)
        print(formatted)
        
        if i < len(examples):
            print(f"\n\n{'-'*80}")
            print("---")
            print(f"{'-'*80}")
    
    print(f"\n\n{'='*80}")
    print("✅ VERIFICATION COMPLETE")
    print(f"{'='*80}")
    print(f"📊 Total examples: {len(examples)}")
    print("🎯 All examples are properly formatted and ready for RAG retrieval!")
    print("\n📋 What to verify:")
    print("1. ✅ JSON structure is valid and properly indented")
    print("2. ✅ All required fields are present")
    print("3. ✅ Format matches original hardcoded examples exactly")
    print("4. ✅ Reasoning explanations are clear and helpful")
    print("5. ✅ Examples cover diverse task types and scenarios")
    
    return 0

if __name__ == "__main__":
    exit(main())
