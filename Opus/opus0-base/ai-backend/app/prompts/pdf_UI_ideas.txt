#----------------------------------------------------------------------
TABLE EXAMPLES
#----------------------------------------------------------------------

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8"/>
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Tables Example</title>
  <!-- Tailwind CSS CDN -->
  <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="font-sans text-sm text-gray-700">

  <!-- Table 1 -->
  <div class="relative overflow-x-auto sm:rounded-lg p-10">
    <table class="w-full text-sm text-left text-gray-500">
      <thead class="text-xs text-gray-700 uppercase bg-gray-50">
        <tr>
          <th scope="col" class="px-6 py-3">Product name</th>
          <th scope="col" class="px-6 py-3">Color</th>
          <th scope="col" class="px-6 py-3 bg-gray-50">Category</th>
          <th scope="col" class="px-6 py-3">Price</th>
        </tr>
      </thead>
      <tbody>
        <tr class="border-b border-gray-200">
          <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap bg-gray-50">
            Apple MacBook Pro 17"
          </th>
          <td class="px-6 py-4">Silver</td>
          <td class="px-6 py-4 bg-gray-50">Laptop</td>
          <td class="px-6 py-4">$2999</td>
        </tr>
        <tr class="border-b border-gray-200">
          <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap bg-gray-50">
            Microsoft Surface Pro
          </th>
          <td class="px-6 py-4">White</td>
          <td class="px-6 py-4 bg-gray-50">Laptop PC</td>
          <td class="px-6 py-4">$1999</td>
        </tr>
        <tr class="border-b border-gray-200">
          <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap bg-gray-50">
            Magic Mouse 2
          </th>
          <td class="px-6 py-4">Black</td>
          <td class="px-6 py-4 bg-gray-50">Accessories</td>
          <td class="px-6 py-4">$99</td>
        </tr>
        <tr class="border-b border-gray-200">
          <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap bg-gray-50">
            Google Pixel Phone
          </th>
          <td class="px-6 py-4">Gray</td>
          <td class="px-6 py-4 bg-gray-50">Phone</td>
          <td class="px-6 py-4">$799</td>
        </tr>
        <tr>
          <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap bg-gray-50">
            Apple Watch 5
          </th>
          <td class="px-6 py-4">Red</td>
          <td class="px-6 py-4 bg-gray-50">Wearables</td>
          <td class="px-6 py-4">$999</td>
        </tr>
      </tbody>
    </table>
  </div>

  <!-- Table 2 -->
  <div class="relative overflow-x-auto mt-8 p-10">
    <table class="w-full text-sm text-left text-gray-500">
      <thead class="text-xs text-gray-700 uppercase bg-gray-100">
        <tr>
          <th scope="col" class="px-6 py-3 rounded-s-lg">Product name</th>
          <th scope="col" class="px-6 py-3">Qty</th>
          <th scope="col" class="px-6 py-3 rounded-e-lg">Price</th>
        </tr>
      </thead>
      <tbody>
        <tr class="bg-white">
          <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap">
            Apple MacBook Pro 17"
          </th>
          <td class="px-6 py-4">1</td>
          <td class="px-6 py-4">$2999</td>
        </tr>
        <tr class="bg-white">
          <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap">
            Microsoft Surface Pro
          </th>
          <td class="px-6 py-4">1</td>
          <td class="px-6 py-4">$1999</td>
        </tr>
        <tr class="bg-white">
          <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap">
            Magic Mouse 2
          </th>
          <td class="px-6 py-4">1</td>
          <td class="px-6 py-4">$99</td>
        </tr>
      </tbody>
      <tfoot>
        <tr class="font-semibold text-gray-900">
          <th scope="row" class="px-6 py-3 text-base">Total</th>
          <td class="px-6 py-3">3</td>
          <td class="px-6 py-3">21,000</td>
        </tr>
      </tfoot>
    </table>
  </div>

  <!-- Table 3 -->
  <div class="relative overflow-x-auto mt-8 mb-8 p-10">
    <table class="w-full text-sm text-left text-gray-500">
      <thead class="text-xs text-gray-900 uppercase">
        <tr>
          <th scope="col" class="px-6 py-3">Product name</th>
          <th scope="col" class="px-6 py-3">Color</th>
          <th scope="col" class="px-6 py-3">Category</th>
          <th scope="col" class="px-6 py-3">Price</th>
        </tr>
      </thead>
      <tbody>
        <tr class="bg-white">
          <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap">
            Apple MacBook Pro 17"
          </th>
          <td class="px-6 py-4">Silver</td>
          <td class="px-6 py-4">Laptop</td>
          <td class="px-6 py-4">$2999</td>
        </tr>
        <tr class="bg-white">
          <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap">
            Microsoft Surface Pro
          </th>
          <td class="px-6 py-4">White</td>
          <td class="px-6 py-4">Laptop PC</td>
          <td class="px-6 py-4">$1999</td>
        </tr>
        <tr class="bg-white">
          <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap">
            Magic Mouse 2
          </th>
          <td class="px-6 py-4">Black</td>
          <td class="px-6 py-4">Accessories</td>
          <td class="px-6 py-4">$99</td>
        </tr>
      </tbody>
    </table>
  </div>

</body>
</html>


#----------------------------------------------------------------------
# HORIZONTAL TIMELINE EXAMPLE
#----------------------------------------------------------------------

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Timeline Diagram</title>

  <!-- Tailwind CSS CDN -->
  <script src="https://cdn.tailwindcss.com"></script>

  <!-- Google Fonts -->
  <link
    href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500&family=Montserrat:wght@600;700&display=swap"
    rel="stylesheet"
  />

  <style>
    .font-heading { font-family: 'Montserrat', sans-serif; }
  </style>
</head>
<body class="bg-gray-50 font-sans">
  <section aria-labelledby="timeline-title" class="mx-20 my-12">
    <!-- Header -->
    <header class="bg-[#2B356F] text-white text-center py-8 px-6 font-heading">
      <h2 id="timeline-title" class="text-3xl font-bold tracking-tight">Timeline Diagram</h2>
      <p class="mt-2 text-sm uppercase tracking-widest">
        Identify what tasks are important to your larger business goals
      </p>
    </header>

    <!-- Timeline Container -->
    <div class="bg-white p-8">
      <ol class="items-center sm:flex">
        <!-- 2010 -->
        <li class="relative mb-6 sm:mb-0">
          <div class="flex items-center">
            <div class="z-10 flex items-center justify-center w-6 h-6 bg-[#2B356F] rounded-full ring-4 ring-white shrink-0">
              <svg class="w-2.5 h-2.5 text-white" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                <path d="M20 4a2 2 0 0 0-2-2h-2V1a1 1 0 0 0-2 0v1h-3V1a1 1 0 0 0-2 0v1H6V1a1 1 0 0 0-2 0v1H2a2 2 0 0 0-2 2v2h20V4ZM0 18a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V8H0v10Zm5-8h10a1 1 0 0 1 0 2H5a1 1 0 0 1 0-2Z"/>
              </svg>
            </div>
            <div class="hidden sm:flex w-full bg-gray-200 h-0.5"></div>
          </div>
          <div class="mt-3 sm:pr-8">
            <h3 class="text-lg font-heading text-gray-900">2010</h3>
            <p class=" text-lg text-gray-500">Objectives clear, details precise, consistent results.</p>
          </div>
        </li>

        <!-- 2011 -->
        <li class="relative mb-6 sm:mb-0">
          <div class="flex items-center">
            <div class="z-10 flex items-center justify-center w-6 h-6 bg-[#2B356F] rounded-full ring-4 ring-white shrink-0">
              <svg class="w-2.5 h-2.5 text-white" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                <path d="M20 4a2 2 0 0 0-2-2h-2V1a1 1 0 0 0-2 0v1h-3V1a1 1 0 0 0-2 0v1H6V1a1 1 0 0 0-2 0v1H2a2 2 0 0 0-2 2v2h20V4ZM0 18a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V8H0v10Zm5-8h10a1 1 0 0 1 0 2H5a1 1 0 0 1 0-2Z"/>
              </svg>
            </div>
            <div class="hidden sm:flex w-full bg-gray-200 h-0.5"></div>
          </div>
          <div class="mt-3 sm:pr-8">
            <h3 class="text-lg font-heading text-gray-900">2011</h3>
            <p class=" text-lg text-gray-500">Objectives clear, details precise, consistent results.</p>
          </div>
        </li>

        <!-- 2012 -->
        <li class="relative mb-6 sm:mb-0">
          <div class="flex items-center">
            <div class="z-10 flex items-center justify-center w-6 h-6 bg-[#2B356F] rounded-full ring-4 ring-white shrink-0">
              <svg class="w-2.5 h-2.5 text-white" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                <path d="M20 4a2 2 0 0 0-2-2h-2V1a1 1 0 0 0-2 0v1h-3V1a1 1 0 0 0-2 0v1H6V1a1 1 0 0 0-2 0v1H2a2 2 0 0 0-2 2v2h20V4ZM0 18a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V8H0v10Zm5-8h10a1 1 0 0 1 0 2H5a1 1 0 0 1 0-2Z"/>
              </svg>
            </div>
            <div class="hidden sm:flex w-full bg-gray-200 h-0.5"></div>
          </div>
          <div class="mt-3 sm:pr-8">
            <h3 class="text-lg font-heading text-gray-900">2012</h3>
            <p class=" text-lg text-gray-500">Objectives clear, details precise, consistent results.</p>
          </div>
        </li>

        <!-- 2013 -->
        <li class="relative mb-6 sm:mb-0">
          <div class="flex items-center">
            <div class="z-10 flex items-center justify-center w-6 h-6 bg-[#2B356F] rounded-full ring-4 ring-white shrink-0">
              <svg class="w-2.5 h-2.5 text-white" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                <path d="M20 4a2 2 0 0 0-2-2h-2V1a1 1 0 0 0-2 0v1h-3V1a1 1 0 0 0-2 0v1H6V1a1 1 0 0 0-2 0v1H2a2 2 0 0 0-2 2v2h20V4ZM0 18a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V8H0v10Zm5-8h10a1 1 0 0 1 0 2H5a1 1 0 0 1 0-2Z"/>
              </svg>
            </div>
            <div class="hidden sm:flex w-full bg-gray-200 h-0.5"></div>
          </div>
          <div class="mt-3 sm:pr-8">
            <h3 class="text-lg font-heading text-gray-900">2013</h3>
            <p class=" text-lg text-gray-500">Objectives clear, details precise, consistent results.</p>
          </div>
        </li>

        <!-- 2014 -->
        <li class="relative mb-6 sm:mb-0">
          <div class="flex items-center">
            <div class="z-10 flex items-center justify-center w-6 h-6 bg-[#2B356F] rounded-full ring-4 ring-white shrink-0">
              <svg class="w-2.5 h-2.5 text-white" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                <path d="M20 4a2 2 0 0 0-2-2h-2V1a1 1 0 0 0-2 0v1h-3V1a1 1 0 0 0-2 0v1H6V1a1 1 0 0 0-2 0v1H2a2 2 0 0 0-2 2v2h20V4ZM0 18a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V8H0v10Zm5-8h10a1 1 0 0 1 0 2H5a1 1 0 0 1 0-2Z"/>
              </svg>
            </div>
          </div>
          <div class="mt-3 sm:pr-8">
            <h3 class="text-lg font-heading text-gray-900">2014</h3>
            <p class=" text-lg text-gray-500">Objectives clear, details precise, consistent results.</p>
          </div>
        </li>
      </ol>
    </div>
  </section>
</body>
</html>


# VERTICAL TIMELINE EXAMPLE
#----------------------------------------------------------------------
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8"/>
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Vertical Timeline</title>
  <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-white font-sans text-gray-800">
  <section class="mx-20 my-12">
    <ol class="relative border-l-2 border-gray-800">
      <!-- Item 1 -->
      <li class="mb-10 ml-6">
        <span class="absolute w-3 h-3 bg-gray-800 rounded-full -left-1.5 mt-1.5"></span>
        <time class="block mb-1 text-sm font-normal leading-none text-gray-600">February 2022</time>
        <h3 class="text-lg font-semibold text-gray-900">Application UI code in Tailwind CSS</h3>
        <p class="mb-4 text-base font-normal text-gray-700">
          Get access to over 20+ pages including a dashboard layout, charts, kanban board, calendar, and pre-order E-commerce & Marketing pages.
        </p>
      </li>
      <!-- Item 2 -->
      <li class="mb-10 ml-6">
        <span class="absolute w-3 h-3 bg-gray-800 rounded-full -left-1.5 mt-1.5"></span>
        <time class="block mb-1 text-sm font-normal leading-none text-gray-600">March 2022</time>
        <h3 class="text-lg font-semibold text-gray-900">Marketing UI design in Figma</h3>
        <p class="text-base font-normal text-gray-700">
          All of the pages and components are first designed in Figma and we keep a parity between the two versions even as we update the project.
        </p>
      </li>
      <!-- Item 3 -->
      <li class="ml-6">
        <span class="absolute w-3 h-3 bg-gray-800 rounded-full -left-1.5 mt-1.5"></span>
        <time class="block mb-1 text-sm font-normal leading-none text-gray-600">April 2022</time>
        <h3 class="text-lg font-semibold text-gray-900">E-Commerce UI code in Tailwind CSS</h3>
        <p class="text-base font-normal text-gray-700">
          Get started with dozens of web components and interactive elements built on top of Tailwind CSS.
        </p>
      </li>
    </ol>
  </section>
</body>
</html>





























#----------------------------------------------------------------------
# WEDDING INVITATION POSTER WITH IMAGE EXAMPLE
#----------------------------------------------------------------------
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Wedding Poster</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <!-- Fonts: script for cursive, serif for titles -->
  <link
    href="https://fonts.googleapis.com/css2?family=Great+Vibes&family=Playfair+Display:wght@400;700&display=swap"
    rel="stylesheet"
  />
  <style>
    .font-script { font-family: 'Great Vibes', cursive; }
    .font-serif  { font-family: 'Playfair Display', serif; }
  </style>
</head>
<body class="bg-[#f3efe8] flex items-center justify-center min-h-screen">
  <div class="bg-[#f3efe8] border border-[#e1d6cd]">
    <!-- Photo with fade -->
    <div class="relative overflow-hidden">
      <img
        src="https://images.unsplash.com/photo-1519741497674-611481863552?q=80&w=1170&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
        alt="Wedding couple"
        class="w-full h-80 object-cover"
      />
      <div class="absolute inset-x-0 bottom-0 h-32 bg-gradient-to-t from-[#f3efe8] to-transparent"></div>
    </div>

    <!-- Text content -->
    <div class="text-center px-6 py-12 mt-12">
      <h1 class="font-script text-8xl text-[#6e5a4f]">Welcome</h1>
      <p class="font-serif text-lg text-[#7a6e67] mt-4 tracking-widest">TO THE CELEBRATION OF</p>
      <h2 class="font-serif text-5xl text-[#3e352f] uppercase mt-8">Olivia</h2>
      <p class="font-script text-3xl text-[#6e5a4f] mt-2">and</p>
      <h2 class="font-serif text-5xl text-[#3e352f] uppercase">Richard</h2>
      <p class="font-serif text-lg text-[#7a6e67] tracking-widest mt-8">WEDDING</p>
    </div>

    <!-- Date with lines -->
    <div class="flex items-center justify-center px-6 pb-8 mb-4">
      <div class="flex-grow border-t border-[#e1d6cd]"></div>
      <span class="font-serif text-base text-[#7a6e67] px-4">May 14, 2030</span>
      <div class="flex-grow border-t border-[#e1d6cd]"></div>
    </div>
  </div>
</body>
</html>


#----------------------------------------------------------------------
# WEDDING INVITATION POSTER 2 MINIMAL WITHOUT IMAGE EXAMPLE
#----------------------------------------------------------------------
<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Wedding Poster</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Great+Vibes&family=Playfair+Display:wght@400;700&display=swap" rel="stylesheet" />
    <style>
      .font-script { font-family: 'Great Vibes', cursive; }
      .font-serif  { font-family: 'Playfair Display', serif; }
    </style>
  </head>
  <body class="font-body flex min-h-screen items-center justify-center bg-gray-100 text-xl text-gray-800">
    <div class="my-10 w-full border-2 border-dashed border-gray-300 bg-white px-10 py-16 leading-relaxed">
      <!-- Monogram -->
      <div class="mt-10 mb-12 text-center">
        <span class="font-serif text-8xl tracking-wide text-gray-800">H</span>
        <span class="mx-4 font-serif text-8xl text-gray-800">|</span>
        <span class="font-serif text-8xl tracking-wide text-gray-800">R</span>
      </div>

      <!-- Welcome -->
      <h1 class="font-script mb-8 text-center text-7xl text-gray-800">Welcome</h1>
      <p class="mb-12 text-center font-serif tracking-widest text-gray-600 uppercase">To the wedding of</p>

      <!-- Names -->
      <div class="mb-12 text-center">
        <h2 class="mb-4 font-serif text-5xl text-gray-800 uppercase">Henrietta Mitchell</h2>
        <p class="font-script mb-4 text-3xl text-gray-800">and</p>
        <h2 class="font-serif text-5xl text-gray-800 uppercase">Richard Sanchez</h2>
      </div>

      <!-- Date -->
      <p class="mb-12 text-center font-serif text-gray-600 uppercase">March 14th, 2027</p>

      <!-- Thank You -->
      <p class="font-script mb-10 text-center text-2xl text-gray-800">Thank you for celebrating with us!</p>
    </div>
  </body>
</html>


#----------------------------------------------------------------------
# RANDOM POSTER EXAMPLE
#----------------------------------------------------------------------

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8"/>
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Marine Life Study Poster</title>

  <!-- Tailwind CSS CDN -->
  <script src="https://cdn.tailwindcss.com"></script>

  <!-- Google Fonts -->
  <link
    href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500&family=Playfair+Display:ital,wght@1,400;700&family=Montserrat:wght@600;700&display=swap"
    rel="stylesheet"
  />

  <style>
    .font-sans    { font-family: 'Inter', sans-serif; }
    .font-serif   { font-family: 'Playfair Display', serif; }
    .font-heading { font-family: 'Montserrat', sans-serif; }
  </style>
</head>
<body class="bg-gray-100 font-sans text-xl">
  <section class="bg-white shadow-lg rounded-lg overflow-hidden">
    <!-- Header -->
    <header class="bg-[#4A8FA1] text-white text-center py-10 px-6 font-serif italic text-lg">
      <div>Blue Valley University</div>
      <h1 class="font-heading text-5xl font-bold uppercase mt-2">Marine Life Study</h1>
      <div class="uppercase mt-1">By Geeta Maheswari</div>
    </header>

    <!-- Content Grid -->
    <div class="grid grid-cols-3 gap-6 p-8">
      <!-- Left Column -->
      <div class="col-span-2 space-y-8">
        <!-- Introduction -->
        <div>
          <h2 class="font-heading text-2xl text-[#1A202C] uppercase">Introduction</h2>
          <p class="mt-2 text-gray-700 leading-relaxed">
            According to Wikipedia, in academic publishing, a scientific journal is a periodical publication intended to further the progress of science, usually by reporting new research. There are thousands of scientific journals in publication, and many more have been published at various points in the past. Most journals are highly specialized, although some of the oldest journals such as Nature publish articles and scientific papers across a wide range.
          </p>
        </div>

        <!-- Main Image -->
        <div>
          <img
            src="https://via.placeholder.com/800x200"
            alt="Ocean scene placeholder"
            class="w-full h-48 object-cover rounded-lg"
          />
        </div>

        <!-- Species A & B -->
        <div class="grid grid-cols-2 gap-6">
          <div>
            <h3 class="font-heading text-xl text-[#1A202C] uppercase">Species A</h3>
            <p class="mt-1 text-gray-700 leading-snug">
              According to Wikipedia, in academic publishing, a scientific journal is a periodical publication intended to further the progress of science...
            </p>
          </div>
          <div>
            <h3 class="font-heading text-xl text-[#1A202C] uppercase">Species B</h3>
            <p class="mt-1 text-gray-700 leading-snug">
              According to Wikipedia, in academic publishing, a scientific journal is a periodical publication intended to further the progress of science...
            </p>
          </div>
        </div>
      </div>

      <!-- Right Sidebar -->
      <aside class="bg-[#C2D8D8] p-6 rounded-lg font-serif">
        <h2 class="font-heading text-xl text-[#1A202C] uppercase">Results &amp; Discussion</h2>
        <p class="mt-2 text-gray-700 leading-relaxed">
          According to Wikipedia, in academic publishing, a scientific journal is a periodical publication intended to further the progress of science...
        </p>
        <div class="mt-4">
          <img
            src="https://via.placeholder.com/400x200"
            alt="Results image placeholder"
            class="w-full h-32 object-cover rounded-lg"
          />
        </div>
      </aside>
    </div>
  </section>
</body>
</html>


#----------------------------------------------------------------------
# RESEARCH POSTER EXAMPLE
#----------------------------------------------------------------------

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Research Poster</title>

  <!-- Tailwind CSS CDN -->
  <script src="https://cdn.tailwindcss.com"></script>

  <!-- Google Fonts -->
  <link
    href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500&family=Poppins:wght@400;700&family=Montserrat:wght@600;700&display=swap"
    rel="stylesheet"
  />

  <!-- Ensure heading font is applied -->
  <style>
    .font-heading { font-family: 'Montserrat', sans-serif; }
  </style>
</head>
<body class="bg-gray-100 font-sans text-xl">
  <section class="bg-white max-w-screen-md mx-auto p-8 shadow-lg">
    <!-- Top labels -->
    <div class="grid grid-cols-2 text-xs font-semibold text-gray-600 uppercase">
      <div>How to Make a Research Poster</div>
      <div class="text-right">A Guide for Students</div>
    </div>

    <!-- Title -->
    <div class="grid grid-cols-2 mt-4">
      <h1 class="font-heading text-6xl font-medium text-primary uppercase tracking-wide">Research</h1>
      <h1 class="font-heading text-6xl font-medium text-primary uppercase tracking-wide text-right">Poster</h1>
    </div>

    <!-- Divider -->
    <hr class="border-t-2 border-gray-300 my-6"/>

    <!-- Subtitle -->
    <p class="text-sm text-gray-700 leading-relaxed">
      Many technologies and breakthroughs would not be possible without research. It is important to keep members of the community informed about the latest updates. One way to do that is through research posters.
    </p>

    <!-- Authors & Affiliations -->
    <div class="grid grid-cols-2 gap-6 mt-8 text-sm text-gray-800">
      <div>
        <h2 class="font-semibold uppercase text-gray-600 mb-2">Authors</h2>
        <p>
          Be proud of your work! Add the names of the people involved in this study. Don’t forget to include titles and credentials. We are proud of these too.
        </p>
      </div>
      <div>
        <h2 class="font-semibold uppercase text-gray-600 mb-2">Affiliations</h2>
        <p>
          List your institutions, departments, and locations here. Acknowledge funding sources or grants if applicable.
        </p>
      </div>
    </div>

    <!-- Introduction & Image -->
    <div class="grid grid-cols-3 gap-6 mt-8">
      <div class="col-span-2 border border-gray-200 p-4">
        <h2 class="font-semibold uppercase text-gray-600 mb-2 text-sm">Introduction</h2>
        <p class="text-sm text-gray-800 leading-snug">
          Posters are a popular method of presenting research findings in a concise and visually pleasing manner. They are commonly used in conferences and meetings. Start by introducing the subject of your research and/or your hypothesis. What are the questions you want to answer? What new things can it contribute to the existing literature?
        </p>
      </div>
      <div class="border border-gray-200 p-4 flex flex-col items-center justify-center">
        <div class="w-full h-32 bg-gray-200"></div>
        <p class="text-xs text-gray-500 mt-2">Image Placeholder</p>
      </div>
    </div>

    <!-- Data Visualizations -->
    <div class="grid grid-cols-3 gap-6 mt-8 text-center">
      <div class="border border-gray-200 p-4">
        <div class="w-full h-32 bg-gray-200 mb-2"></div>
        <p class="text-xs text-gray-500">Image Placeholder</p>
      </div>
      <div class="border border-gray-200 p-4">
        <h3 class="font-semibold uppercase text-gray-600 mb-2 text-sm">Results</h3>
        <div class="w-full h-32 bg-gray-200 mb-2"></div>
        <p class="text-xs text-gray-500">Bar Chart Placeholder</p>
      </div>
      <div class="border border-gray-200 p-4">
        <h3 class="font-semibold uppercase text-gray-600 mb-2 text-sm">Results</h3>
        <div class="w-full h-32 bg-gray-200 rounded-full mb-2"></div>
        <p class="text-xs text-gray-500">Pie Chart Placeholder</p>
      </div>
    </div>

    <!-- Analysis & Conclusion -->
    <div class="grid grid-cols-2 gap-6 mt-8 text-gray-800">
      <div>
        <h3 class="font-semibold uppercase text-gray-600 mb-2">Analysis</h3>
        <p class="leading-snug">
          Expand on your findings by discussing what methods were used to analyze your data, the key takeaways, and any limitations or future directions.
        </p>
      </div>
      <div>
        <h3 class="font-semibold uppercase text-gray-600 mb-2">Conclusion</h3>
        <p class="leading-snug">
          Summarize your study’s impact and suggest practical applications or recommendations based on your results.
        </p>
      </div>
    </div>

    <!-- Footer -->
    <hr class="border-t border-gray-200 mt-8"/>
    <p class="text-xs text-gray-500 text-right mt-2">
      References: List all sources in APA or your chosen format.
    </p>
  </section>
</body>
</html>


#----------------------------------------------------------------------
# MUSIC EVENT POSTER EXAMPLE
#----------------------------------------------------------------------
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8"/>
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Music Event Poster</title>

  <!-- Tailwind CSS CDN -->
  <script src="https://cdn.tailwindcss.com"></script>

  <!-- Google Fonts -->
  <link
    href="https://fonts.googleapis.com/css2?family=Metal+Mania&family=Inter:wght@400;500&display=swap"
    rel="stylesheet"
  />

  <style>
    /* typography */
    .font-metal { font-family: 'Metal Mania', cursive; }
    .font-body  { font-family: 'Inter', sans-serif; }
    body { margin: 0; padding: 0; background: #fff; }
  </style>
</head>
<body class="relative font-body text-gray-900">

  <!-- Crumpled paper background -->
  <div class="fixed inset-0 bg-cover bg-center"
       style="background-image:url('https://www.transparenttextures.com/patterns/crumpled-paper.png');">
  </div>

  <div class="relative mx-20 my-12 z-10 space-y-8">
    <!-- Top names -->
    <div class="flex justify-between uppercase text-sm tracking-wide">
      <span>Greta Mae Evans</span>
      <span>Harper Russo</span>
      <span>Donna Stroupe</span>
    </div>

    <!-- Subtitle -->
    <div class="text-center">
      <span class="italic text-xs text-gray-600 tracking-wider">Borcelle Community Present</span>
    </div>

    <!-- Main title with Metal Mania -->
    <h1 class="font-metal text-[12rem] leading-none text-center drop-shadow-[6px_6px_0_rgba(0,0,0,0.4)]">
      MUSIC<br/>EVENT
    </h1>

    <!-- Enhanced decorative slogan block -->
    <div class="flex items-center justify-center space-x-4 text-gray-600 uppercase text-sm tracking-widest">
      <div class="h-px w-20 bg-gray-800"></div>
      <svg class="w-6 h-6 text-gray-800" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
        <path d="M9 17V5l12-2v12"/>
        <circle cx="9" cy="17" r="3"/>
        <circle cx="21" cy="15" r="3"/>
      </svg>
      <span class="italic">LET’S ROCK!</span>
      <svg class="w-6 h-6 text-gray-800 rotate-180" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
        <path d="M9 17V5l12-2v12"/>
        <circle cx="9" cy="17" r="3"/>
        <circle cx="21" cy="15" r="3"/>
      </svg>
      <div class="h-px w-20 bg-gray-800"></div>
    </div>

    <!-- Venue & date -->
    <div class="flex justify-center items-baseline space-x-4">
      <h2 class="font-metal text-4xl uppercase">Shodwe Arena</h2>
      <span class="font-metal text-2xl">//</span>
      <span class="text-xl">1708 2024</span>
    </div>

    <!-- Icon grid -->
    <div class="flex justify-center space-x-3">
      <div class="w-8 h-8 border-2 border-gray-800"></div>
      <div class="w-8 h-8 border-2 border-gray-800"></div>
      <div class="w-8 h-8 border-2 border-gray-800"></div>
      <div class="w-8 h-8 border-2 border-gray-800"></div>
      <div class="w-8 h-8 border-2 border-gray-800"></div>
    </div>

    <!-- Wave graph -->
    <div class="relative w-full h-20 border-t-2 border-b-2 border-gray-800 overflow-hidden">
      <svg class="absolute inset-0 w-full h-full" viewBox="0 0 200 50" fill="none" stroke="currentColor" stroke-width="2">
        <polyline points="0,25 20,10 40,30 60,15 80,35 100,20 120,40 140,18 160,28 180,12 200,25" />
      </svg>
    </div>

    <!-- Address -->
    <div class="text-center">
      <span class="bg-gray-900 text-white uppercase text-xs px-4 py-1 tracking-wide rounded">
        123 Anywhere St., Any City, ST 12345
      </span>
    </div>

    <!-- Footer link -->
    <div class="text-center text-xs uppercase tracking-wider text-gray-600">
      For further information:
      <a href="https://www.synagi.com" class="underline hover:text-gray-900">
        WWW.SYNAGI.COM
      </a>
    </div>
  </div>
</body>
</html>


#----------------------------------------------------------------------
# SINGLE COLUMN WELL STYLED CV
#----------------------------------------------------------------------

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8"/>
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Jacqueline Thompson — Resume</title>

  <!-- Tailwind CSS CDN -->
  <script src="https://cdn.tailwindcss.com"></script>

  <!-- Google Fonts -->
  <link
    href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500&family=Poppins:wght@600;700&display=swap"
    rel="stylesheet"
  />

  <style>
    body { font-family: 'Inter', sans-serif; }
    .font-heading { font-family: 'Poppins', sans-serif; }
    .text-purple { --tw-text-opacity: 1; color: rgba(118, 72, 178, var(--tw-text-opacity)); }
    .border-purple { --tw-border-opacity: 1; border-color: rgba(118, 72, 178, var(--tw-border-opacity)); }
  </style>
</head>
<body class="bg-white text-gray-800 text-sm leading-relaxed">
  <div class="py-10 px-10">
    <!-- Name & Contact -->
    <header class="text-center mb-8">
      <h1 class="font-heading text-3xl text-purple uppercase">Jacqueline Thompson</h1>
      <p class="mt-1 text-gray-600">
        123 Anywhere St., Any City · ************ · <EMAIL><br/>
        www.synagi.com
      </p>
    </header>

    <!-- Summary -->
    <section class="mb-8">
      <div class="flex items-center mb-4">
        <h2 class="font-heading text-purple uppercase text-sm">Summary</h2>
        <div class="flex-grow ml-4 border-t-2 border-purple"></div>
      </div>
      <p>
        Results-oriented Engineering Executive with a proven track record of optimizing project outcomes. Skilled in strategic project management and team leadership. Seeking a challenging executive role to leverage technical expertise and drive engineering excellence.
      </p>
    </section>

    <!-- Work Experience -->
    <section class="mb-8">
      <div class="flex items-center mb-4">
        <h2 class="font-heading text-purple uppercase text-sm">Work Experience</h2>
        <div class="flex-grow ml-4 border-t-2 border-purple"></div>
      </div>
      <div class="space-y-6">
        <!-- Item 1 -->
        <div class="flex justify-between">
          <div class="w-3/4">
            <h3 class="font-heading text-base">Engineering Executive, Borcelle Technologies</h3>
            <ul class="list-disc list-inside mt-2 space-y-1">
              <li>Implemented cost-effective solutions, resulting in a 20% reduction in project expenses.</li>
              <li>Streamlined project workflows, enhancing overall efficiency by 25%.</li>
              <li>Led a team in successfully delivering a complex engineering project on time and within allocated budget.</li>
            </ul>
          </div>
          <div class="text-right text-gray-600">Jan 2023 – Present</div>
        </div>

        <!-- Item 2 -->
        <div class="flex justify-between">
          <div class="w-3/4">
            <h3 class="font-heading text-base">Project Engineer, Salford & Co</h3>
            <ul class="list-disc list-inside mt-2 space-y-1">
              <li>Managed project timelines, reducing delivery times by 30%.</li>
              <li>Spearheaded the adoption of cutting-edge engineering software, improving project accuracy by 15%.</li>
              <li>Collaborated with cross-functional teams, enhancing project success rates by 10%.</li>
            </ul>
          </div>
          <div class="text-right text-gray-600">Mar 2021 – Dec 2022</div>
        </div>

        <!-- Item 3 -->
        <div class="flex justify-between">
          <div class="w-3/4">
            <h3 class="font-heading text-base">Graduate Engineer, Aerowai Industries</h3>
            <ul class="list-disc list-inside mt-2 space-y-1">
              <li>Coordinated project tasks, ensuring adherence to engineering standards and regulations.</li>
              <li>Conducted comprehensive project analyses, identifying and rectifying discrepancies in engineering designs.</li>
            </ul>
          </div>
          <div class="text-right text-gray-600">Feb 2020 – Jan 2021</div>
        </div>
      </div>
    </section>

    <!-- Education -->
    <section class="mb-8">
      <div class="flex items-center mb-4">
        <h2 class="font-heading text-purple uppercase text-sm">Education</h2>
        <div class="flex-grow ml-4 border-t-2 border-purple"></div>
      </div>
      <div class="space-y-6">
        <!-- Degree 1 -->
        <div class="flex justify-between">
          <div class="w-3/4">
            <h3 class="font-heading text-base">Master of Science in Mechanical Engineering</h3>
            <p class="mt-1">University of Engineering and Technology</p>
            <ul class="list-disc list-inside mt-1 space-y-1 text-sm text-gray-700">
              <li>Specialization in Advanced Manufacturing.</li>
              <li>Thesis on “Innovations in Sustainable Engineering Practices”.</li>
            </ul>
          </div>
          <div class="text-right text-gray-600">Sep 2019 – Oct 2020</div>
        </div>

        <!-- Degree 2 -->
        <div class="flex justify-between">
          <div class="w-3/4">
            <h3 class="font-heading text-base">Bachelor of Science in Civil Engineering</h3>
            <p class="mt-1">City College of Engineering</p>
            <ul class="list-disc list-inside mt-1 space-y-1 text-sm text-gray-700">
              <li>Relevant coursework in Structural Design and Project Management.</li>
            </ul>
          </div>
          <div class="text-right text-gray-600">Aug 2015 – Aug 2019</div>
        </div>
      </div>
    </section>

    <!-- Additional Information -->
    <section>
      <div class="flex items-center mb-4">
        <h2 class="font-heading text-purple uppercase text-sm">Additional Information</h2>
        <div class="flex-grow ml-4 border-t-2 border-purple"></div>
      </div>
      <ul class="list-none space-y-2 text-sm text-gray-700">
        <li><strong>Technical Skills:</strong> Project Management, Structural Analysis, Robotics and Automation, CAD</li>
        <li><strong>Languages:</strong> English, Malay, German</li>
        <li><strong>Certifications:</strong> Professional Engineer (PE) License, Project Management Professional (PMP)</li>
        <li><strong>Awards & Activities:</strong> Received the “Engineering Excellence” Award for outstanding contributions to project innovation, Borcelle Technologies</li>
      </ul>
    </section>
  </div>
</body>
</html>


#----------------------------------------------------------------------
# DOUBLE COLUMN WELL STYLED MODERN CV
#----------------------------------------------------------------------

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8"/>
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Cia Rodriguez — Resume</title>
  <!-- Tailwind CSS CDN -->
  <script src="https://cdn.tailwindcss.com"></script>
  <!-- Google Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500&family=Poppins:wght@600;700&display=swap" rel="stylesheet"/>
  <style>
    body { margin:0; font-family: 'Inter', sans-serif; }
    .font-heading { font-family: 'Poppins', sans-serif; }
    .text-accent { color: #4A5568; } /* charcoal accent */
  </style>
</head>
<body class="bg-white">
  <div class="flex">
    <!-- Left Column -->
    <aside class="w-1/3 bg-[#f7fafc] p-8 text-gray-700">
      <h1 class="font-heading text-3xl text-gray-800 uppercase">Cia Rodriguez</h1>
      <p class="mt-1 text-base text-accent uppercase tracking-wide">College Instructor</p>

      <section class="mt-10">
        <h2 class="font-heading text-xl text-gray-800 uppercase border-b-2 border-gray-300 pb-1">Career Objectives</h2>
        <p class="mt-3 text-base leading-relaxed text-gray-600">
          I aim to pursue a career in the academe which makes use of my skills as a sociology graduate. I would like to teach gender relations and social stratification.
        </p>
      </section>

      <section class="mt-8">
        <h2 class="font-heading text-xl text-gray-800 uppercase border-b-2 border-gray-300 pb-1">Career Highlights</h2>
        <ul class="mt-3 list-disc list-inside space-y-1 text-gray-600">
          <li>Instructor of the Year (2020)</li>
          <li>Best Instructor (College Division, 2020)</li>
          <li>Faculty Member of the Year (2019)</li>
          <li>Most Innovative Research Paper (2019)</li>
        </ul>
      </section>

      <section class="mt-8">
        <h2 class="font-heading text-xl text-gray-800 uppercase border-b-2 border-gray-300 pb-1">Contact</h2>
        <p class="mt-3 text-base leading-relaxed text-gray-600">
          Home: ************<br/>
          Cell: ************<br/>
          <EMAIL><br/>
          www.synagi.com<br/>
          123 Anywhere St., Any City, State<br/>Country 12345
        </p>
      </section>
    </aside>

    <!-- Right Column -->
    <main class="w-2/3 bg-white p-8 text-gray-800">
      <!-- Work Experience -->
      <section>
        <h2 class="font-heading text-2xl text-gray-800 uppercase border-b-2 border-gray-300 pb-1">Work Experience</h2>
        <div class="mt-6 space-y-8">
          <div>
            <div class="flex justify-between items-baseline">
              <h3 class="font-heading text-lg text-accent">College Instructor</h3>
              <span class="text-sm text-gray-500 italic">Jan 2018 – 2020</span>
            </div>
            <p class="text-sm text-gray-600 italic">University of El Dorado</p>
            <ul class="list-disc list-inside mt-2 space-y-1 text-gray-600">
              <li>Created the syllabus for Introduction to Sociology</li>
              <li>Conducted regular consultations with students weekly</li>
              <li>Provided curated reading materials</li>
            </ul>
          </div>
          <div>
            <div class="flex justify-between items-baseline">
              <h3 class="font-heading text-lg text-accent">Teacher’s Assistant</h3>
              <span class="text-sm text-gray-500 italic">2017 – 2018</span>
            </div>
            <p class="text-sm text-gray-600 italic">University of El Dorado</p>
            <ul class="list-disc list-inside mt-2 space-y-1 text-gray-600">
              <li>Assisted professor with visual aids, handouts, and paperwork</li>
              <li>Scheduled consultations for professors and students</li>
            </ul>
          </div>
        </div>
      </section>

      <!-- Education Highlights -->
      <section class="mt-12">
        <h2 class="font-heading text-2xl text-gray-800 uppercase border-b-2 border-gray-300 pb-1">Education Highlights</h2>
        <div class="mt-6 space-y-8">
          <div>
            <div class="flex justify-between items-baseline">
              <h3 class="font-heading text-lg text-accent">El Dorado University</h3>
              <span class="text-sm text-gray-500 italic">2013 – 2017</span>
            </div>
            <p class="text-sm text-gray-600 italic">Bachelor of Arts in Sociology</p>
            <ul class="list-disc list-inside mt-2 space-y-1 text-gray-600">
              <li>Completed 30+ units of sociology</li>
              <li>Batch Representative, Sociology Class of 2017</li>
              <li>President, The Educators Circle</li>
              <li>Member, The Debate Club</li>
            </ul>
          </div>
          <div>
            <div class="flex justify-between items-baseline">
              <h3 class="font-heading text-lg text-accent">ZimCore High School</h3>
              <span class="text-sm text-gray-500 italic">Batch 2013</span>
            </div>
            <p class="text-sm text-gray-600 italic">School Council President</p>
            <ul class="list-disc list-inside mt-2 space-y-1 text-gray-600">
              <li>Graduated with honors</li>
              <li>President, The Debate Society</li>
              <li>Editor-in-Chief, School Paper</li>
              <li>Member, The History Club</li>
            </ul>
          </div>
        </div>
      </section>

      <!-- Main Interests -->
      <section class="mt-12">
        <h2 class="font-heading text-2xl text-gray-800 uppercase border-b-2 border-gray-300 pb-1">Main Interests</h2>
        <ul class="mt-6 list-disc list-inside text-gray-600 space-y-1">
          <li>Sociology</li>
          <li>Literature</li>
          <li>History</li>
          <li>Art &amp; Design</li>
          <li>Lecturing</li>
          <li>Debating</li>
          <li>Public Speaking</li>
          <li>Psychology</li>
          <li>Statistics</li>
          <li>Communication Research</li>
        </ul>
      </section>
    </main>
  </div>
</body>
</html>


#----------------------------------------------------------------------
# ITINERARY EXAMPLE MODERN
#----------------------------------------------------------------------

<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Japan 7-Day Itinerary</title>

    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Niconne&family=Noto+Sans+JP:wght@400;500&display=swap" rel="stylesheet" />
  </head>
  <body class="  bg-white font-sans">
    <!-- Hero with Tokyo image and overlay -->
    <header class="relative h-64 w-full overflow-hidden">
      <img src="https://images.unsplash.com/photo-1545569341-9eb8b30979d9?w=1200&auto=format&fit=crop&q=80" alt="Tokyo Skyline" class="h-full w-full object-cover" />
      <div class="absolute inset-0 bg-black/30"></div>
      <!-- Sakura icon + title with solid backdrop -->
      <div class="absolute bottom-6 left-6 flex items-center space-x-2 rounded bg-white/80 p-2">
        <svg width="40" height="40" viewBox="0 0 64 64" fill="none">
          <circle cx="32" cy="32" r="6" fill="#F8BBD0" />
          <circle cx="32" cy="12" r="8" fill="#F8BBD0" />
          <circle cx="52" cy="32" r="8" fill="#F8BBD0" />
          <circle cx="32" cy="52" r="8" fill="#F8BBD0" />
          <circle cx="12" cy="32" r="8" fill="#F8BBD0" />
        </svg>
        <h1 class="font-heading text-5xl">Japan Adventure</h1>
      </div>
    </header>

    <!-- Vertical timeline -->
    <main class="mx-20 my-12">
      <ol class="relative border-l-2 border-gray-300">
        <!-- Day 1 -->
        <li class="mb-12 ml-6">
          <span class="bg-japanRed absolute top-1 -left-3 h-6 w-6 rounded-full border-2 border-white"></span>
          <h2 class="font-heading mb-2 text-2xl">Day 1: Tokyo Arrival</h2>
          <p class="text-sm text-gray-700">Arrive at Narita/Haneda Airport, transfer to your hotel in Shinjuku, and explore the neon streets.</p>
          <p class="mt-1 text-sm font-medium">Budget: ¥20,000 (~$150)</p>
          <ul class="list-inside list-disc text-xs text-gray-600">
            <li>Transport: ¥8,000</li>
            <li>Meals: ¥6,000</li>
            <li>Accommodation: ¥6,000</li>
          </ul>
          <img src="https://images.unsplash.com/photo-1674814343675-23c631bfd3ab?w=400&auto=format&fit=crop&q=60" alt="Tokyo Airport" class="mt-4 h-40 w-full rounded object-cover" />
        </li>

        <!-- Day 2 -->
        <li class="mb-12 ml-6">
          <span class="bg-japanRed absolute top-1 -left-3 h-6 w-6 rounded-full border-2 border-white"></span>
          <h2 class="font-heading mb-2 text-2xl">Day 2: Asakusa & Ueno</h2>
          <p class="text-sm text-gray-700">Morning at Senso-ji Temple in Asakusa, afternoon strolling Ueno Park’s museums and cherry blossoms.</p>
          <p class="mt-1 text-sm font-medium">Budget: ¥15,000 (~$110)</p>
          <ul class="list-inside list-disc text-xs text-gray-600">
            <li>Temple Entry & Gifts: ¥3,000</li>
            <li>Park & Museums: ¥4,000</li>
            <li>Meals & Snacks: ¥8,000</li>
          </ul>
          <img src="https://images.unsplash.com/photo-1478436127897-769e1b3f0f36?w=400&auto=format&fit=crop&q=60" alt="Senso-ji Temple" class="mt-4 h-40 w-full rounded object-cover" />
        </li>

        <!-- Day 3 -->
        <li class="mb-12 ml-6">
          <span class="bg-japanRed absolute top-1 -left-3 h-6 w-6 rounded-full border-2 border-white"></span>
          <h2 class="font-heading mb-2 text-2xl">Day 3: Mount Fuji Excursion</h2>
          <p class="text-sm text-gray-700">Day-trip to the Fuji Five Lakes region for iconic views of Mt. Fuji and local cuisine.</p>
          <p class="mt-1 text-sm font-medium">Budget: ¥18,000 (~$130)</p>
          <ul class="list-inside list-disc text-xs text-gray-600">
            <li>Train Pass: ¥5,000</li>
            <li>Guided Tour: ¥7,000</li>
            <li>Lunch & Snacks: ¥6,000</li>
          </ul>
          <img src="https://plus.unsplash.com/premium_photo-1673698463059-c022fb3a32ab?w=400&auto=format&fit=crop&q=60" alt="Mount Fuji" class="mt-4 h-40 w-full rounded object-cover" />
        </li>

        <!-- Day 4 -->
        <li class="mb-12 ml-6">
          <span class="bg-japanRed absolute top-1 -left-3 h-6 w-6 rounded-full border-2 border-white"></span>
          <h2 class="font-heading mb-2 text-2xl">Day 4: Kyoto by Shinkansen</h2>
          <p class="text-sm text-gray-700">Bullet train to Kyoto, then evening wander through Gion’s lantern-lit streets.</p>
          <p class="mt-1 text-sm font-medium">Budget: ¥25,000 (~$185)</p>
          <ul class="list-inside list-disc text-xs text-gray-600">
            <li>Shinkansen Fare: ¥14,000</li>
            <li>Dinner in Gion: ¥6,000</li>
            <li>Accommodation: ¥5,000</li>
          </ul>
          <img src="https://images.unsplash.com/photo-1545569341-9eb8b30979d9?w=400&auto=format&fit=crop&q=60" alt="Kyoto Skyline" class="mt-4 h-40 w-full rounded object-cover" />
        </li>

        <!-- Day 5 -->
        <li class="mb-12 ml-6">
          <span class="bg-japanRed absolute top-1 -left-3 h-6 w-6 rounded-full border-2 border-white"></span>
          <h2 class="font-heading mb-2 text-2xl">Day 5: Arashiyama Bamboo Grove</h2>
          <p class="text-sm text-gray-700">Wander the serene bamboo forest and cross the iconic Togetsukyo Bridge.</p>
          <p class="mt-1 text-sm font-medium">Budget: ¥12,000 (~$90)</p>
          <ul class="list-inside list-disc text-xs text-gray-600">
            <li>Train & Bus: ¥3,000</li>
            <li>Entrance Fees: ¥2,000</li>
            <li>Lunch: ¥7,000</li>
          </ul>
          <img src="https://plus.unsplash.com/premium_photo-1673698463059-c022fb3a32ab?w=400&auto=format&fit=crop&q=60" alt="Arashiyama Bamboo" class="mt-4 h-40 w-full rounded object-cover" />
        </li>

        <!-- Day 6 -->
        <li class="mb-12 ml-6">
          <span class="bg-japanRed absolute top-1 -left-3 h-6 w-6 rounded-full border-2 border-white"></span>
          <h2 class="font-heading mb-2 text-2xl">Day 6: Osaka Street Food</h2>
          <p class="text-sm text-gray-700">Dive into Dotonbori’s takoyaki, okonomiyaki, and neon nightlife.</p>
          <p class="mt-1 text-sm font-medium">Budget: ¥20,000 (~$150)</p>
          <ul class="list-inside list-disc text-xs text-gray-600">
            <li>Food Tour: ¥8,000</li>
            <li>River Cruise: ¥4,000</li>
            <li>Snacks & Drinks: ¥8,000</li>
          </ul>
          <img src="https://source.unsplash.com/400x200/?osaka,dotonbori" alt="Dotonbori" class="mt-4 h-40 w-full rounded object-cover" />
        </li>

        <!-- Day 7 -->
        <li class="mb-12 ml-6">
          <span class="bg-japanRed absolute top-1 -left-3 h-6 w-6 rounded-full border-2 border-white"></span>
          <h2 class="font-heading mb-2 text-2xl">Day 7: Return via Shibuya</h2>
          <p class="text-sm text-gray-700">Last-minute shopping in Shibuya, then transfer to the airport for departure.</p>
          <p class="mt-1 text-sm font-medium">Budget: ¥10,000 (~$75)</p>
          <ul class="list-inside list-disc text-xs text-gray-600">
            <li>Shopping & Souvenirs: ¥6,000</li>
            <li>Taxi to Airport: ¥4,000</li>
          </ul>
          <img src="https://source.unsplash.com/400x200/?shibuya,station" alt="Shibuya Crossing" class="mt-4 h-40 w-full rounded object-cover" />
        </li>
      </ol>
    </main>
    <section class="mx-20 my-12">
      <hr class="mb-8 border-t border-gray-300" />
      <h2 class="font-heading mt-12 mb-4 text-center text-3xl">Budget Summary</h2>
      <ol class="relative border-l-2 border-gray-300 pl-8">
        <li class="relative mb-6">
          <span class="bg-sakura absolute top-1 -left-4 h-4 w-4 rounded-full border-2 border-white"></span>
          <div class="flex justify-between">
            <span class="font-heading">Day 1</span>
            <span class="  font-medium">¥20,000 (~$150)</span>
          </div>
        </li>
        <li class="relative mb-6">
          <span class="bg-sakura absolute top-1 -left-4 h-4 w-4 rounded-full border-2 border-white"></span>
          <div class="flex justify-between">
            <span class="font-heading">Day 2</span>
            <span class="  font-medium">¥15,000 (~$110)</span>
          </div>
        </li>
        <li class="relative mb-6">
          <span class="bg-sakura absolute top-1 -left-4 h-4 w-4 rounded-full border-2 border-white"></span>
          <div class="flex justify-between">
            <span class="font-heading">Day 3</span>
            <span class="  font-medium">¥18,000 (~$130)</span>
          </div>
        </li>
        <li class="relative mb-6">
          <span class="bg-sakura absolute top-1 -left-4 h-4 w-4 rounded-full border-2 border-white"></span>
          <div class="flex justify-between">
            <span class="font-heading">Day 4</span>
            <span class="  font-medium">¥25,000 (~$185)</span>
          </div>
        </li>
        <li class="relative mb-6">
          <span class="bg-sakura absolute top-1 -left-4 h-4 w-4 rounded-full border-2 border-white"></span>
          <div class="flex justify-between">
            <span class="font-heading">Day 5</span>
            <span class="  font-medium">¥12,000 (~$90)</span>
          </div>
        </li>
        <li class="relative mb-6">
          <span class="bg-sakura absolute top-1 -left-4 h-4 w-4 rounded-full border-2 border-white"></span>
          <div class="flex justify-between">
            <span class="font-heading">Day 6</span>
            <span class="  font-medium">¥20,000 (~$150)</span>
          </div>
        </li>
        <li class="relative mb-6">
          <span class="bg-sakura absolute top-1 -left-4 h-4 w-4 rounded-full border-2 border-white"></span>
          <div class="flex justify-between">
            <span class="font-heading">Day 7</span>
            <span class="  font-medium">¥10,000 (~$75)</span>
          </div>
        </li>
      </ol>
      <div class="mt-10 flex items-center justify-center space-x-4">
        <span class="bg-japanRed inline-block h-6 w-6 rounded-full border-2 border-white"></span>
        <span class="font-heading text-japanRed text-2xl">Total: ¥120,000 (~$890)</span>
      </div>
    </section>

    <section class="mx-20 my-12">
      <hr class="mb-8 border-t border-gray-300" />
      <!-- Section Header with decorative underline -->
      <div class="relative mt-12 mb-4 text-center">
        <h2 class="font-heading text-3xl">Travel Tips &amp; Useful Phrases</h2>
        <div class="bg-sakura absolute top-full left-1/2 mt-2 h-1 w-24 -translate-x-1/2 transform rounded"></div>
      </div>

      <!-- Container with subtle background -->
      <div class="bg-indigo/10 rounded-lg p-8">
        <div class="grid grid-cols-1 gap-12 md:grid-cols-2">
          <!-- Travel Tips -->
          <div>
            <h3 class="  mb-4 text-2xl font-semibold">Travel Tips</h3>
            <ul class="space-y-6">
              <li class="flex items-start">
                <svg class="text-japanRed mt-1 h-6 w-6 flex-shrink-0" fill="currentColor" viewBox="0 0 24 24"><path d="M12 2a1 1 0 011 1v4h4a1 1 0 110 2h-4v4a1 1 0 11-2 0v-4H7a1 1 0 110-2h4V3a1 1 0 011-1z" /></svg>
                <span class="ml-3 text-sm text-gray-800"> Carry cash – many local shops, temples &amp; eateries are cash-only. </span>
              </li>
              <li class="flex items-start">
                <svg class="text-japanRed mt-1 h-6 w-6 flex-shrink-0" fill="currentColor" viewBox="0 0 24 24"><path d="M12 2a1 1 0 011 1v4h4a1 1 0 110 2h-4v4a1 1 0 11-2 0v-4H7a1 1 0 110-2h4V3a1 1 0 011-1z" /></svg>
                <span class="ml-3 text-sm text-gray-800"> Get an IC card (Suica/Pasmo) for seamless trains, buses &amp; vending machines. </span>
              </li>
              <li class="flex items-start">
                <svg class="text-japanRed mt-1 h-6 w-6 flex-shrink-0" fill="currentColor" viewBox="0 0 24 24"><path d="M12 2a1 1 0 011 1v4h4a1 1 0 110 2h-4v4a1 1 0 11-2 0v-4H7a1 1 0 110-2h4V3a1 1 0 011-1z" /></svg>
                <span class="ml-3 text-sm text-gray-800"> Respect etiquette: bow lightly, remove shoes at entrances, speak softly on trains. </span>
              </li>
              <li class="flex items-start">
                <svg class="text-japanRed mt-1 h-6 w-6 flex-shrink-0" fill="currentColor" viewBox="0 0 24 24"><path d="M12 2a1 1 0 011 1v4h4a1 1 0 110 2h-4v4a1 1 0 11-2 0v-4H7a1 1 0 110-2h4V3a1 1 0 011-1z" /></svg>
                <span class="ml-3 text-sm text-gray-800"> Rent a pocket Wi-Fi or local SIM in advance to stay connected everywhere. </span>
              </li>
              <li class="flex items-start">
                <svg class="text-japanRed mt-1 h-6 w-6 flex-shrink-0" fill="currentColor" viewBox="0 0 24 24"><path d="M12 2a1 1 0 011 1v4h4a1 1 0 110 2h-4v4a1 1 0 11-2 0v-4H7a1 1 0 110-2h4V3a1 1 0 011-1z" /></svg>
                <span class="ml-3 text-sm text-gray-800"> Download Google Maps offline for navigation in rural areas. </span>
              </li>
            </ul>
          </div>

          <!-- Useful Phrases -->
          <div>
            <h3 class="  mb-4 text-2xl font-semibold">Useful Japanese Phrases</h3>
            <ul class="space-y-6">
              <li>
                <span class="font-heading text-japanRed text-lg">こんにちは</span>
                <span class="ml-2 text-sm text-gray-700 italic">(Konnichiwa)</span>
                <span class="ml-2 text-sm text-gray-800">– Hello</span>
              </li>
              <li>
                <span class="font-heading text-japanRed text-lg">お願いします</span>
                <span class="ml-2 text-sm text-gray-700 italic">(Onegaishimasu)</span>
                <span class="ml-2 text-sm text-gray-800">– Please</span>
              </li>
              <li>
                <span class="font-heading text-japanRed text-lg">ありがとう</span>
                <span class="ml-2 text-sm text-gray-700 italic">(Arigatō)</span>
                <span class="ml-2 text-sm text-gray-800">– Thank you</span>
              </li>
              <li>
                <span class="font-heading text-japanRed text-lg">すみません</span>
                <span class="ml-2 text-sm text-gray-700 italic">(Sumimasen)</span>
                <span class="ml-2 text-sm text-gray-800">– Excuse me / Sorry</span>
              </li>
              <li>
                <span class="font-heading text-japanRed text-lg">いくらですか？</span>
                <span class="ml-2 text-sm text-gray-700 italic">(Ikura desu ka?)</span>
                <span class="ml-2 text-sm text-gray-800">– How much is it?</span>
              </li>
              <li>
                <span class="font-heading text-japanRed text-lg">トイレはどこですか？</span>
                <span class="ml-2 text-sm text-gray-700 italic">(Toire wa doko desu ka?)</span>
                <span class="ml-2 text-sm text-gray-800">– Where is the restroom?</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </section>
  </body>
</html>


#----------------------------------------------------------------------
# ITINERARY EXAMPLE CULTURAL
#----------------------------------------------------------------------

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8"/>
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Incredible India 7-Day Itinerary</title>

  <!-- Tailwind CSS CDN -->
  <script src="https://cdn.tailwindcss.com"></script>

  <!-- Google Fonts -->
  <link
    href="https://fonts.googleapis.com/css2?family=Mukta:wght@400;500&family=Ranga&display=swap"
    rel="stylesheet"
  />

  <style>
    .font-heading { font-family: 'Ranga', cursive; }
    .font-body    { font-family: 'Mukta', sans-serif; }
  </style>
</head>
<body class="bg-gray-50 font-body text-gray-800">

  <!-- Decorative Header -->
  <header class="mx-20 my-12 text-center">
    <svg class="mx-auto mb-4 text-[#FF9933]" width="64" height="64" viewBox="0 0 64 64" fill="currentColor">
      <circle cx="32" cy="20" r="6"/>
      <circle cx="20" cy="32" r="6"/>
      <circle cx="44" cy="32" r="6"/>
      <circle cx="32" cy="44" r="6"/>
      <circle cx="32" cy="32" r="6"/>
    </svg>
    <h1 class="font-heading text-5xl text-[#0B3D91] mb-2">Incredible India</h1>
    <p class="text-[#FF9933] font-semibold">7-Day Cultural Odyssey</p>
    <div class="mt-2 h-1 w-24 mx-auto bg-[#FF9933] rounded"></div>
  </header>

  <main class="mx-20 space-y-12">

    <!-- Day 1 -->
    <section class="bg-[#FFF3E0] p-8 rounded-lg">
      <div class="flex items-center mb-4 space-x-4">
        <div class="w-10 h-10 bg-[#138808] text-white flex items-center justify-center font-heading text-lg rounded-full">1</div>
        <h2 class="font-heading text-2xl text-[#0B3D91]">Day 1 · Delhi Arrival</h2>
      </div>
      <p class="mb-2">
        Land at IGI Airport, transfer to Connaught Place. Explore Janpath market & savor local <em>chaat</em>.
      </p>
      <p class="font-medium text-[#FF9933] mb-2">Budget: ₹8,000 (~$100)</p>
      <ul class="list-disc list-inside text-xs text-gray-700 mb-4">
        <li>Airport transfer: ₹2,000</li>
        <li>Meals & snacks: ₹3,000</li>
        <li>Hotel (CP area): ₹3,000</li>
      </ul>
      <img
        src="https://images.unsplash.com/photo-1519955266818-0231b63402bc?w=400&auto=format&fit=crop&q=60"
        alt="Delhi Architecture"
        class="w-full h-48 object-cover rounded-lg"
      />
    </section>

    <!-- Day 2 -->
    <section class="bg-[#E8F5E9] p-8 rounded-lg">
      <div class="flex items-center mb-4 space-x-4">
        <div class="w-10 h-10 bg-[#138808] text-white flex items-center justify-center font-heading text-lg rounded-full">2</div>
        <h2 class="font-heading text-2xl text-[#0B3D91]">Day 2 · Old & New Delhi</h2>
      </div>
      <p class="mb-2">
        Rickshaw through Chandni Chowk at dawn, visit Jama Masjid. Afternoon at Qutub Minar & Humayun’s Tomb.
      </p>
      <p class="font-medium text-[#FF9933] mb-2">Budget: ₹6,000 (~$75)</p>
      <ul class="list-disc list-inside text-xs text-gray-700 mb-4">
        <li>Rickshaw tour: ₹1,000</li>
        <li>Monument entries: ₹1,500</li>
        <li>Meals & transport: ₹3,500</li>
      </ul>
      <img
        src="https://plus.unsplash.com/premium_photo-1661919589683-f11880119fb7?w=400&auto=format&fit=crop&q=60"
        alt="Historic Monument"
        class="w-full h-48 object-cover rounded-lg"
      />
    </section>

    <!-- Day 3 -->
    <section class="bg-[#FFF3E0] p-8 rounded-lg">
      <div class="flex items-center mb-4 space-x-4">
        <div class="w-10 h-10 bg-[#138808] text-white flex items-center justify-center font-heading text-lg rounded-full">3</div>
        <h2 class="font-heading text-2xl text-[#0B3D91]">Day 3 · Agra & Fatehpur Sikri</h2>
      </div>
      <p class="mb-2">
        Sunrise at the Taj Mahal, then explore the red-sandstone palaces of Fatehpur Sikri.
      </p>
      <p class="font-medium text-[#FF9933] mb-2">Budget: ₹7,000 (~$90)</p>
      <ul class="list-disc list-inside text-xs text-gray-700 mb-4">
        <li>Train fare: ₹1,200</li>
        <li>Entry tickets: ₹1,100</li>
        <li>Guide & meals: ₹4,700</li>
      </ul>
      <img
        src="https://images.unsplash.com/photo-1524492412937-b28074a5d7da?w=400&auto=format&fit=crop&q=60"
        alt="Taj Mahal"
        class="w-full h-48 object-cover rounded-lg"
      />
    </section>

    <!-- Day 4 -->
    <section class="bg-[#E8F5E9] p-8 rounded-lg">
      <div class="flex items-center mb-4 space-x-4">
        <div class="w-10 h-10 bg-[#138808] text-white flex items-center justify-center font-heading text-lg rounded-full">4</div>
        <h2 class="font-heading text-2xl text-[#0B3D91]">Day 4 · Jaipur (Pink City)</h2>
      </div>
      <p class="mb-2">
        Drive to Jaipur. Visit Hawa Mahal, City Palace & browse vibrant Johari bazaars.
      </p>
      <p class="font-medium text-[#FF9933] mb-2">Budget: ₹9,000 (~$115)</p>
      <ul class="list-disc list-inside text-xs text-gray-700 mb-4">
        <li>Car transfer: ₹3,000</li>
        <li>Entry fees: ₹1,500</li>
        <li>Shopping & meals: ₹4,500</li>
      </ul>
      <img
        src="https://images.unsplash.com/photo-1545126178-862cdb469409?w=400&auto=format&fit=crop&q=60"
        alt="Hawa Mahal"
        class="w-full h-48 object-cover rounded-lg"
      />
    </section>

    <!-- Day 5 -->
    <section class="bg-[#FFF3E0] p-8 rounded-lg">
      <div class="flex items-center mb-4 space-x-4">
        <div class="w-10 h-10 bg-[#138808] text-white flex items-center justify-center font-heading text-lg rounded-full">5</div>
        <h2 class="font-heading text-2xl text-[#0B3D91]">Day 5 · Varanasi Spiritual</h2>
      </div>
      <p class="mb-2">
        Ganga Aarti at Dashashwamedh Ghat, dawn boat ride & wander the ancient lanes.
      </p>
      <p class="font-medium text-[#FF9933] mb-2">Budget: ₹8,500 (~$110)</p>
      <ul class="list-disc list-inside text-xs text-gray-700 mb-4">
        <li>Flight: ₹4,000</li>
        <li>Boat & Aarti: ₹1,500</li>
        <li>Meals & stay: ₹3,000</li>
      </ul>
      <img
        src="https://images.unsplash.com/photo-1519955266818-0231b63402bc?w=400&auto=format&fit=crop&q=60"
        alt="Varanasi Backwaters"
        class="w-full h-48 object-cover rounded-lg"
      />
    </section>

    <!-- Day 6 -->
    <section class="bg-[#E8F5E9] p-8 rounded-lg">
      <div class="flex items-center mb-4 space-x-4">
        <div class="w-10 h-10 bg-[#138808] text-white flex items-center justify-center font-heading text-lg rounded-full">6</div>
        <h2 class="font-heading text-2xl text-[#0B3D91]">Day 6 · Kerala Backwaters</h2>
      </div>
      <p class="mb-2">
        Cruise Alleppey backwaters on a houseboat & savor authentic Kerala cuisine.
      </p>
      <p class="font-medium text-[#FF9933] mb-2">Budget: ₹10,000 (~$130)</p>
      <ul class="list-disc list-inside text-xs text-gray-700 mb-4">
        <li>Flight: ₹3,500</li>
        <li>Houseboat & meals: ₹6,000</li>
        <li>Transfers: ₹500</li>
      </ul>
      <img
        src="https://images.unsplash.com/photo-1519955266818-0231b63402bc?w=400&auto=format&fit=crop&q=60"
        alt="Kerala Backwaters"
        class="w-full h-48 object-cover rounded-lg"
      />
    </section>

    <!-- Day 7 -->
    <section class="bg-[#FFF3E0] p-8 rounded-lg">
      <div class="flex items-center mb-4 space-x-4">
        <div class="w-10 h-10 bg-[#138808] text-white flex items-center justify-center font-heading text-lg rounded-full">7</div>
        <h2 class="font-heading text-2xl text-[#0B3D91]">Day 7 · Mumbai Finale</h2>
      </div>
      <p class="mb-2">
        Sunset at Marine Drive, street food at Chowpatty & depart from Chhatrapati Shivaji Airport.
      </p>
      <p class="font-medium text-[#FF9933] mb-2">Budget: ₹9,000 (~$115)</p>
      <ul class="list-disc list-inside text-xs text-gray-700 mb-4">
        <li>Flight: ₹4,000</li>
        <li>Meals & transfers: ₹3,000</li>
        <li>Souvenirs: ₹2,000</li>
      </ul>
      <img
        src="https://images.unsplash.com/photo-1519955266818-0231b63402bc?w=400&auto=format&fit=crop&q=60"
        alt="Marine Drive"
        class="w-full h-48 object-cover rounded-lg"
      />
    </section>

  </main>
</body>
</html>

#----------------------------------------------------------------------
# ITINERARY EXAMPLE CULTURAL 2
#----------------------------------------------------------------------

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Japan 7-Day Itinerary</title>

  <!-- Tailwind CSS CDN -->
  <script src="https://cdn.tailwindcss.com"></script>
  <script>
    // Japan-themed fonts and colors
    tailwind.config = {
      theme: {
        extend: {
          fontFamily: {
            body: ['Noto Sans JP', 'sans-serif'],
            heading: ['Niconne', 'cursive'],
          },
          colors: {
            japanRed: '#D32F2F',
            sakura:   '#F8BBD0',
            indigo:   '#264653',
            midnight: '#212121',
          },
        },
      },
    }
  </script>

  <!-- Google Fonts -->
  <link
    href="https://fonts.googleapis.com/css2?family=Niconne&family=Noto+Sans+JP:wght@400;500&display=swap"
    rel="stylesheet"
  />

  <!-- Ensure custom classes display correctly -->
  <style>
    .font-heading { font-family: 'Niconne', cursive; }
    .font-body    { font-family: 'Noto Sans JP', sans-serif; }
    .text-japanRed { color: #D32F2F !important; }
    .bg-japanRed   { background-color: #D32F2F !important; }
    .text-indigo   { color: #264653 !important; }
    .text-midnight { color: #212121 !important; }
  </style>
</head>
<body class="bg-white font-body text-midnight">

  <!-- Hero with Tokyo image and overlay -->
  <header class="relative w-full h-64 overflow-hidden">
    <img
      src="https://images.unsplash.com/photo-1545569341-9eb8b30979d9?w=1200&auto=format&fit=crop&q=80"
      alt="Tokyo Skyline"
      class="w-full h-full object-cover"
    />
    <div class="absolute inset-0 bg-black/30"></div>
    <!-- Sakura icon + title with solid backdrop -->
    <div class="absolute bottom-6 left-6 flex items-center space-x-2 bg-white/80 p-2 rounded">
      <svg width="40" height="40" viewBox="0 0 64 64" fill="none">
        <circle cx="32" cy="32" r="6" fill="#F8BBD0"/>
        <circle cx="32" cy="12" r="8" fill="#F8BBD0"/>
        <circle cx="52" cy="32" r="8" fill="#F8BBD0"/>
        <circle cx="32" cy="52" r="8" fill="#F8BBD0"/>
        <circle cx="12" cy="32" r="8" fill="#F8BBD0"/>
      </svg>
      <h1 class="text-5xl font-heading text-indigo">Japan Adventure</h1>
    </div>
  </header>

  <!-- Vertical timeline -->
  <main class="mx-20 my-12">
    <ol class="relative border-l-2 border-gray-300">

      <!-- Day 1 -->
      <li class="mb-12 ml-6">
        <span class="absolute -left-3 top-1 w-6 h-6 bg-japanRed rounded-full border-2 border-white"></span>
        <h2 class="text-2xl font-heading text-indigo mb-2">Day 1: Tokyo Arrival</h2>
        <p class="text-sm text-gray-700">
          Arrive at Narita/Haneda Airport, transfer to your hotel in Shinjuku, and explore the neon streets.
        </p>
        <p class="mt-1 text-sm font-medium text-japanRed">Budget: ¥20,000 (~$150)</p>
        <ul class="list-disc list-inside text-xs text-gray-600">
          <li>Transport: ¥8,000</li>
          <li>Meals: ¥6,000</li>
          <li>Accommodation: ¥6,000</li>
        </ul>
        <img
          src="https://images.unsplash.com/photo-1674814343675-23c631bfd3ab?w=400&auto=format&fit=crop&q=60"
          alt="Tokyo Airport"
          class="mt-4 w-full h-40 object-cover rounded"
        />
      </li>

      <!-- Day 2 -->
      <li class="mb-12 ml-6">
        <span class="absolute -left-3 top-1 w-6 h-6 bg-japanRed rounded-full border-2 border-white"></span>
        <h2 class="text-2xl font-heading text-indigo mb-2">Day 2: Asakusa & Ueno</h2>
        <p class="text-sm text-gray-700">
          Morning at Senso-ji Temple in Asakusa, afternoon strolling Ueno Park’s museums and cherry blossoms.
        </p>
        <p class="mt-1 text-sm font-medium text-japanRed">Budget: ¥15,000 (~$110)</p>
        <ul class="list-disc list-inside text-xs text-gray-600">
          <li>Temple Entry & Gifts: ¥3,000</li>
          <li>Park & Museums: ¥4,000</li>
          <li>Meals & Snacks: ¥8,000</li>
        </ul>
        <img
          src="https://images.unsplash.com/photo-1478436127897-769e1b3f0f36?w=400&auto=format&fit=crop&q=60"
          alt="Senso-ji Temple"
          class="mt-4 w-full h-40 object-cover rounded"
        />
      </li>

      <!-- Day 3 -->
      <li class="mb-12 ml-6">
        <span class="absolute -left-3 top-1 w-6 h-6 bg-japanRed rounded-full border-2 border-white"></span>
        <h2 class="text-2xl font-heading text-indigo mb-2">Day 3: Mount Fuji Excursion</h2>
        <p class="text-sm text-gray-700">
          Day-trip to the Fuji Five Lakes region for iconic views of Mt. Fuji and local cuisine.
        </p>
        <p class="mt-1 text-sm font-medium text-japanRed">Budget: ¥18,000 (~$130)</p>
        <ul class="list-disc list-inside text-xs text-gray-600">
          <li>Train Pass: ¥5,000</li>
          <li>Guided Tour: ¥7,000</li>
          <li>Lunch & Snacks: ¥6,000</li>
        </ul>
        <img
          src="https://plus.unsplash.com/premium_photo-1673698463059-c022fb3a32ab?w=400&auto=format&fit=crop&q=60"
          alt="Mount Fuji"
          class="mt-4 w-full h-40 object-cover rounded"
        />
      </li>

      <!-- Day 4 -->
      <li class="mb-12 ml-6">
        <span class="absolute -left-3 top-1 w-6 h-6 bg-japanRed rounded-full border-2 border-white"></span>
        <h2 class="text-2xl font-heading text-indigo mb-2">Day 4: Kyoto by Shinkansen</h2>
        <p class="text-sm text-gray-700">
          Bullet train to Kyoto, then evening wander through Gion’s lantern-lit streets.
        </p>
        <p class="mt-1 text-sm font-medium text-japanRed">Budget: ¥25,000 (~$185)</p>
        <ul class="list-disc list-inside text-xs text-gray-600">
          <li>Shinkansen Fare: ¥14,000</li>
          <li>Dinner in Gion: ¥6,000</li>
          <li>Accommodation: ¥5,000</li>
        </ul>
        <img
          src="https://images.unsplash.com/photo-1545569341-9eb8b30979d9?w=400&auto=format&fit=crop&q=60"
          alt="Kyoto Skyline"
          class="mt-4 w-full h-40 object-cover rounded"
        />
      </li>

      <!-- Day 5 -->
      <li class="mb-12 ml-6">
        <span class="absolute -left-3 top-1 w-6 h-6 bg-japanRed rounded-full border-2 border-white"></span>
        <h2 class="text-2xl font-heading text-indigo mb-2">Day 5: Arashiyama Bamboo Grove</h2>
        <p class="text-sm text-gray-700">
          Wander the serene bamboo forest and cross the iconic Togetsukyo Bridge.
        </p>
        <p class="mt-1 text-sm font-medium text-japanRed">Budget: ¥12,000 (~$90)</p>
        <ul class="list-disc list-inside text-xs text-gray-600">
          <li>Train & Bus: ¥3,000</li>
          <li>Entrance Fees: ¥2,000</li>
          <li>Lunch: ¥7,000</li>
        </ul>
        <img
          src="https://plus.unsplash.com/premium_photo-1673698463059-c022fb3a32ab?w=400&auto=format&fit=crop&q=60"
          alt="Arashiyama Bamboo"
          class="mt-4 w-full h-40 object-cover rounded"
        />
      </li>

      <!-- Day 6 -->
      <li class="mb-12 ml-6">
        <span class="absolute -left-3 top-1 w-6 h-6 bg-japanRed rounded-full border-2 border-white"></span>
        <h2 class="text-2xl font-heading text-indigo mb-2">Day 6: Osaka Street Food</h2>
        <p class="text-sm text-gray-700">
          Dive into Dotonbori’s takoyaki, okonomiyaki, and neon nightlife.
        </p>
        <p class="mt-1 text-sm font-medium text-japanRed">Budget: ¥20,000 (~$150)</p>
        <ul class="list-disc list-inside text-xs text-gray-600">
          <li>Food Tour: ¥8,000</li>
          <li>River Cruise: ¥4,000</li>
          <li>Snacks & Drinks: ¥8,000</li>
        </ul>
        <img
          src="https://source.unsplash.com/400x200/?osaka,dotonbori"
          alt="Dotonbori"
          class="mt-4 w-full h-40 object-cover rounded"
        />
      </li>

      <!-- Day 7 -->
      <li class="mb-12 ml-6">
        <span class="absolute -left-3 top-1 w-6 h-6 bg-japanRed rounded-full border-2 border-white"></span>
        <h2 class="text-2xl font-heading text-indigo mb-2">Day 7: Return via Shibuya</h2>
        <p class="text-sm text-gray-700">
          Last-minute shopping in Shibuya, then transfer to the airport for departure.
        </p>
        <p class="mt-1 text-sm font-medium text-japanRed">Budget: ¥10,000 (~$75)</p>
        <ul class="list-disc list-inside text-xs text-gray-600">
          <li>Shopping & Souvenirs: ¥6,000</li>
          <li>Taxi to Airport: ¥4,000</li>
        </ul>
        <img
          src="https://source.unsplash.com/400x200/?shibuya,station"
          alt="Shibuya Crossing"
          class="mt-4 w-full h-40 object-cover rounded"
        />
      </li>

    </ol>
  </main>

  <section class="mx-20 my-12">
  <hr class="border-t border-gray-300 mb-8"/>
  <h2 class="text-3xl font-heading text-indigo text-center mt-12 mb-4">Budget Summary</h2>
  <ol class="relative border-l-2 border-gray-300 pl-8">
    <li class="mb-6 relative">
      <span class="absolute -left-4 top-1 w-4 h-4 bg-sakura rounded-full border-2 border-white"></span>
      <div class="flex justify-between">
        <span class="font-heading text-indigo">Day 1</span>
        <span class="font-medium text-midnight">¥20,000 (~$150)</span>
      </div>
    </li>
    <li class="mb-6 relative">
      <span class="absolute -left-4 top-1 w-4 h-4 bg-sakura rounded-full border-2 border-white"></span>
      <div class="flex justify-between">
        <span class="font-heading text-indigo">Day 2</span>
        <span class="font-medium text-midnight">¥15,000 (~$110)</span>
      </div>
    </li>
    <li class="mb-6 relative">
      <span class="absolute -left-4 top-1 w-4 h-4 bg-sakura rounded-full border-2 border-white"></span>
      <div class="flex justify-between">
        <span class="font-heading text-indigo">Day 3</span>
        <span class="font-medium text-midnight">¥18,000 (~$130)</span>
      </div>
    </li>
    <li class="mb-6 relative">
      <span class="absolute -left-4 top-1 w-4 h-4 bg-sakura rounded-full border-2 border-white"></span>
      <div class="flex justify-between">
        <span class="font-heading text-indigo">Day 4</span>
        <span class="font-medium text-midnight">¥25,000 (~$185)</span>
      </div>
    </li>
    <li class="mb-6 relative">
      <span class="absolute -left-4 top-1 w-4 h-4 bg-sakura rounded-full border-2 border-white"></span>
      <div class="flex justify-between">
        <span class="font-heading text-indigo">Day 5</span>
        <span class="font-medium text-midnight">¥12,000 (~$90)</span>
      </div>
    </li>
    <li class="mb-6 relative">
      <span class="absolute -left-4 top-1 w-4 h-4 bg-sakura rounded-full border-2 border-white"></span>
      <div class="flex justify-between">
        <span class="font-heading text-indigo">Day 6</span>
        <span class="font-medium text-midnight">¥20,000 (~$150)</span>
      </div>
    </li>
    <li class="mb-6 relative">
      <span class="absolute -left-4 top-1 w-4 h-4 bg-sakura rounded-full border-2 border-white"></span>
      <div class="flex justify-between">
        <span class="font-heading text-indigo">Day 7</span>
        <span class="font-medium text-midnight">¥10,000 (~$75)</span>
      </div>
    </li>
  </ol>
  <div class="mt-10 flex justify-center items-center space-x-4">
    <span class="inline-block w-6 h-6 bg-japanRed rounded-full border-2 border-white"></span>
    <span class="text-2xl font-heading text-japanRed">Total: ¥120,000 (~$890)</span>
  </div>
</section>

<section class="mx-20 my-12">
  <hr class="border-t border-gray-300 mb-8"/>
  <div class="relative text-center mt-12 mb-4">
    <h2 class="text-3xl font-heading text-indigo">Travel Tips &amp; Useful Phrases</h2>
    <div class="absolute left-1/2 transform -translate-x-1/2 top-full mt-2 w-24 h-1 bg-sakura rounded"></div>
  </div>

  <div class="bg-indigo/10 p-8 rounded-lg">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-12">
      <!-- Travel Tips -->
      <div>
        <h3 class="text-2xl font-semibold text-midnight mb-4">Travel Tips</h3>
        <ul class="space-y-6">
          <li class="flex items-start">
            <svg class="w-6 h-6 flex-shrink-0 text-japanRed mt-1" fill="currentColor" viewBox="0 0 24 24"><path d="M12 2a1 1 0 011 1v4h4a1 1 0 110 2h-4v4a1 1 0 11-2 0v-4H7a1 1 0 110-2h4V3a1 1 0 011-1z"/></svg>
            <span class="ml-3 text-sm text-gray-800">
              Carry cash – many local shops, temples &amp; eateries are cash-only.
            </span>
          </li>
          <li class="flex items-start">
            <svg class="w-6 h-6 flex-shrink-0 text-japanRed mt-1" fill="currentColor" viewBox="0 0 24 24"><path d="M12 2a1 1 0 011 1v4h4a1 1 0 110 2h-4v4a1 1 0 11-2 0v-4H7a1 1 0 110-2h4V3a1 1 0 011-1z"/></svg>
            <span class="ml-3 text-sm text-gray-800">
              Get an IC card (Suica/Pasmo) for seamless trains, buses &amp; vending machines.
            </span>
          </li>
          <li class="flex items-start">
            <svg class="w-6 h-6 flex-shrink-0 text-japanRed mt-1" fill="currentColor" viewBox="0 0 24 24"><path d="M12 2a1 1 0 011 1v4h4a1 1 0 110 2h-4v4a1 1 0 11-2 0v-4H7a1 1 0 110-2h4V3a1 1 0 011-1z"/></svg>
            <span class="ml-3 text-sm text-gray-800">
              Respect etiquette: bow lightly, remove shoes at entrances, speak softly on trains.
            </span>
          </li>
          <li class="flex items-start">
            <svg class="w-6 h-6 flex-shrink-0 text-japanRed mt-1" fill="currentColor" viewBox="0 0 24 24"><path d="M12 2a1 1 0 011 1v4h4a1 1 0 110 2h-4v4a1 1 0 11-2 0v-4H7a1 1 0 110-2h4V3a1 1 0 011-1z"/></svg>
            <span class="ml-3 text-sm text-gray-800">
              Rent a pocket Wi-Fi or local SIM in advance to stay connected everywhere.
            </span>
          </li>
          <li class="flex items-start">
            <svg class="w-6 h-6 flex-shrink-0 text-japanRed mt-1" fill="currentColor" viewBox="0 0 24 24"><path d="M12 2a1 1 0 011 1v4h4a1 1 0 110 2h-4v4a1 1 0 11-2 0v-4H7a1 1 0 110-2h4V3a1 1 0 011-1z"/></svg>
            <span class="ml-3 text-sm text-gray-800">
              Download Google Maps offline for navigation in rural areas.
            </span>
          </li>
        </ul>
      </div>

      <!-- Useful Phrases -->
      <div>
        <h3 class="text-2xl font-semibold text-midnight mb-4">Useful Japanese Phrases</h3>
        <ul class="space-y-6">
          <li>
            <span class="font-heading text-japanRed text-lg">こんにちは</span>
            <span class="ml-2 italic text-sm text-gray-700">(Konnichiwa)</span>
            <span class="ml-2 text-sm text-gray-800">– Hello</span>
          </li>
          <li>
            <span class="font-heading text-japanRed text-lg">お願いします</span>
            <span class="ml-2 italic text-sm text-gray-700">(Onegaishimasu)</span>
            <span class="ml-2 text-sm text-gray-800">– Please</span>
          </li>
          <li>
            <span class="font-heading text-japanRed text-lg">ありがとう</span>
            <span class="ml-2 italic text-sm text-gray-700">(Arigatō)</span>
            <span class="ml-2 text-sm text-gray-800">– Thank you</span>
          </li>
          <li>
            <span class="font-heading text-japanRed text-lg">すみません</span>
            <span class="ml-2 italic text-sm text-gray-700">(Sumimasen)</span>
            <span class="ml-2 text-sm text-gray-800">– Excuse me / Sorry</span>
          </li>
          <li>
            <span class="font-heading text-japanRed text-lg">いくらですか？</span>
            <span class="ml-2 italic text-sm text-gray-700">(Ikura desu ka?)</span>
            <span class="ml-2 text-sm text-gray-800">– How much is it?</span>
          </li>
          <li>
            <span class="font-heading text-japanRed text-lg">トイレはどこですか？</span>
            <span class="ml-2 italic text-sm text-gray-700">(Toire wa doko desu ka?)</span>
            <span class="ml-2 text-sm text-gray-800">– Where is the restroom?</span>
          </li>
        </ul>
      </div>
    </div>
  </div>
</section>

</body>
</html>

#----------------------------------------------------------------------
# NEWSPAPER DESIGN OLD
#----------------------------------------------------------------------
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8"/>
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Breaking News – Especial Edition</title>
  <!-- Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>
  <!-- Google Fonts -->
  <link 
    href="https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400;0,700;1,400&family=Roboto+Slab:wght@400;700&display=swap" 
    rel="stylesheet"
  />
  <style>
    body {
      font-family: 'Roboto Slab', serif;
      background: #f7f5f0 url('https://www.transparenttextures.com/patterns/paper-fibers.png');
      color: #111;
      margin: 0; padding: 0;
    }
    .font-headline { font-family: 'Playfair Display', serif; }
  </style>
</head>
<body>
  <div class="p-8">
    <!-- Header -->
    <div class="text-center mb-2">
      <span class="uppercase text-xs tracking-widest bg-white px-2">Especial Edition</span>
    </div>
    <h1 class="font-headline text-6xl text-center leading-tight uppercase tracking-tight">
      Breaking News
    </h1>
    <hr class="border-t-2 border-gray-800 my-2"/>
    <h2 class="font-headline text-2xl text-center uppercase tracking-wide mb-6">
      Olivia's 1st Birthday Party
    </h2>

    <!-- Feature Article -->
    <div class="grid grid-cols-2 gap-6 mb-12">
      <div>
        <img src="https://picsum.photos/seed/baby/600/400" alt="Feature" class="w-full h-auto rounded shadow"/>
      </div>
      <div class="text-base leading-relaxed">
        <p>
          Lorem ipsum dolor sit amet, consectetur adipiscing elit. Vivamus orci velit, porttitor nec justo eget, fermentum facilisis urna. Nullam ut tincidunt quam. Curabitur in tempus est. Suspendisse sed urna efficitur, eleifend sapien quis, consectetur libero.
        </p>
      </div>
    </div>

    <hr class="border-t border-gray-800 mb-8"/>

    <!-- Newspaper-style multi-column section -->
    <section class="columns-2 gap-8">
      <!-- Snippet 1 -->
      <div class="break-inside-avoid mb-8">
        <h3 class="font-headline text-xl uppercase mb-2">Community Celebrations</h3>
        <img src="https://picsum.photos/seed/party/300/200" alt="Block party" class="w-full mb-2 rounded"/>
        <p class="text-sm leading-snug">
          Neighbors threw a surprise block party complete with balloons and treats. Children enjoyed games while adults caught up over coffee in front yards transformed into mini-festivals.
        </p>
      </div>

      <!-- Snippet 2 -->
      <div class="break-inside-avoid mb-8">
        <h3 class="font-headline text-xl uppercase mb-2">Bakery Boom</h3>
        <img src="https://picsum.photos/seed/bakery/300/200" alt="Bakery" class="w-full mb-2 rounded"/>
        <p class="text-sm leading-snug">
          The town bakery reports record cake sales this month. Their limited-edition rainbow cake sold out within hours of opening, prompting early morning lines around the block.
        </p>
      </div>

      <!-- Snippet 3 -->
      <div class="break-inside-avoid mb-8">
        <h3 class="font-headline text-xl uppercase mb-2">Charity Fun Run</h3>
        <p class="text-sm leading-snug">
          Hundreds laced up their sneakers on June 10 for the annual charity fun run. Funds raised will support the local children’s hospital and community sports programs.
        </p>
      </div>

      <!-- Snippet 4 -->
      <div class="break-inside-avoid mb-8">
        <h3 class="font-headline text-xl uppercase mb-2">Market Opening</h3>
        <p class="text-sm leading-snug">
          The seasonal farmers’ market opens August 1 with fresh produce, artisan crafts, and live music. Local growers and crafters will showcase their best goods.
        </p>
      </div>
    </section>

    <hr class="border-t border-gray-800 mt-12"/>

    <!-- Footer -->
    <p class="text-center text-sm uppercase tracking-wide mt-4">
      For more news, visit <a href="https://www.synagi.com" class="underline">www.synagi.com</a>
    </p>
  </div>
</body>
</html>


#----------------------------------------------------------------------
# NEWSPAPER DESIGN VINTAGE
#----------------------------------------------------------------------

<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Breaking News – Vintage Edition</title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Libre+Baskerville:wght@400;700&family=Old+Standard+TT:400,700&display=swap" rel="stylesheet" />
    <style>
      body {
        margin: 0; padding: 0;
        background: #f0e6d2 url('https://www.transparenttextures.com/patterns/paper-fibers.png');
        color: #222;
        font-family: 'Libre Baskerville', serif;
      }
      .font-title { font-family: 'Old Standard TT', serif; }
      .rule { border-top-width: 2px; border-color: #222; }
      .news-col { column-gap: 2rem; }
    </style>
  </head>
  <body>
    <div class="p-6">
      <!-- Top label and thin rule -->
      <div class="mb-1 text-center text-sm tracking-wide uppercase">Especial Edition</div>
      <div class="rule mb-2"></div>

      <!-- Main title with double rule -->
      <h1 class="font-title text-center text-6xl leading-tight uppercase">Breaking News</h1>
      <div class="rule my-2"></div>

      <!-- Feature section -->
      <div class="mb-6 grid grid-cols-3 gap-4">
        <div class="col-span-2">
          <img src="https://picsum.photos/seed/concert/800/500" alt="Juliet's Birthday Night" class="mb-2 h-auto w-full rounded-sm shadow" />
          <h2 class="font-title mb-2 text-2xl uppercase">Juliet's Birthday Night</h2>
          <p class="mb-4 text-sm leading-relaxed">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Vivamus orci velit, porttitor nec justo eget, fermentum facilisis enim. Nullam ut tincidunt quam. Curabitur in tempus est.</p>
        </div>
        <aside class="col-span-1 space-y-6 text-sm">
          <div>
            <h3 class="font-title mb-1 text-base uppercase">Juliet tells us how she prepared for her party.</h3>
            <img src="https://picsum.photos/seed/juliet/300/200" alt="Juliet portrait" class="mb-1 h-auto w-full rounded-sm" />
            <p class="leading-snug">Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Integer nec odio. Praesent libero.</p>
          </div>
          <div class="rule"></div>
          <div>
            <h3 class="font-title mb-1 text-base uppercase">Mom and Dad, the most elegant of the night.</h3>
            <img src="https://picsum.photos/seed/parents/300/200" alt="Parents" class="mb-1 h-auto w-full rounded-sm" />
            <p class="leading-snug">Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Integer nec odio. Praesent libero.</p>
          </div>
        </aside>
      </div>

      <div class="rule mb-6"></div>

      <!-- Extended newspaper-style multi-column section -->
      <section class="news-col mb-6" style="columns:3;">
        <article class="mb-4 break-inside-avoid">
          <h3 class="font-title mb-1 text-lg uppercase">Town Parade Draws Crowd</h3>
          <p class="text-sm leading-snug">Hundreds lined Main Street to watch the historic town parade. Floats adorned with flowers rolled by, while marching bands played anthems to cheering onlookers. The mayor delivered a brief speech celebrating the community's unity.</p>
        </article>
        <article class="mb-4 break-inside-avoid">
          <h3 class="font-title mb-1 text-lg uppercase">New Library Wings Open</h3>
          <p class="text-sm leading-snug">The public library unveiled two new reading wings featuring rare local manuscripts and a dedicated children’s corner. Librarians reported a 30% increase in membership registrations on opening day.</p>
        </article>
        <article class="mb-4 break-inside-avoid">
          <h3 class="font-title mb-1 text-lg uppercase">Vintage Car Show</h3>
          <p class="text-sm leading-snug">Classic automobile enthusiasts gathered at Riverside Park for the annual Vintage Car Show. Over 50 models from the 1920s to ’60s were on display, including restored Model Ts and ’57 Chevrolets.</p>
        </article>
        <article class="mb-4 break-inside-avoid">
          <h3 class="font-title mb-1 text-lg uppercase">Farmers’ Market Success</h3>
          <p class="text-sm leading-snug">This season’s Farmers’ Market saw record attendance, with stalls selling fresh produce, handmade crafts, and artisanal baked goods. Organizers plan to extend market hours due to popular demand.</p>
        </article>
        <article class="mb-4 break-inside-avoid">
          <h3 class="font-title mb-1 text-lg uppercase">Community Theatre Revival</h3>
          <p class="text-sm leading-snug">After a two-year hiatus, the community theatre troupe returns with a production of Shakespeare’s “A Midsummer Night’s Dream.” Tickets sold out within hours of release.</p>
        </article>
      </section>

      <div class="rule mb-4"></div>
      <p class="text-center text-sm tracking-wide uppercase">More information at <a href="https://www.synagi.com" class="underline">www.synagi.com</a></p>
    </div>
  </body>
</html>


#----------------------------------------------------------------------
# ARTICLE
#----------------------------------------------------------------------
<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Entwined Rivals: Apple & Samsung—and Beyond</title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Merriweather:ital,wght@0,400;0,700;1,400;1,700&family=Montserrat:wght@400;600;700&display=swap" rel="stylesheet" />
    <style>
      body { font-family: 'Merriweather', serif; color: #333; background: #fff; }
      h1,h2,h3 { font-family: 'Montserrat', sans-serif; }
      blockquote { font-style: italic; color: #555; border-left: 4px solid #ccc; padding-left: 1rem; }
    </style>
  </head>
  <body>
    <article class="mx-auto max-w-4xl p-8">
      <!-- Header -->
      <header class="mb-12">
        <h1 class="mb-2 text-4xl font-bold">Entwined Rivals: Apple &amp; Samsung—and Beyond</h1>
        <p class="text-sm text-gray-600">By <strong>Synagi</strong> | May 1, 2025</p>
        <div class="mt-6">
          <img src="https://picsum.photos/1200/600?tech" alt="Apple & Samsung" class="h-auto w-full rounded-lg shadow-lg" />
        </div>
      </header>

      <!-- Pull-quote -->
      <blockquote class="my-10 text-xl leading-snug">“Samsung’s OLED screens power billions of iPhones—proof that public rivalry often bows to economic reality.”</blockquote>

      <!-- Two-column body -->
      <div class="grid grid-cols-1 gap-8 text-lg leading-relaxed lg:grid-cols-2">
        <!-- Section 1 -->
        <section>
          <h2 class="mb-4 text-2xl font-semibold">Samsung’s Screens Inside Your iPhone</h2>
          <p>Apple and Samsung feud fiercely, yet Samsung supplies the high-end OLED displays for iPhones. While Apple touts its prototypes, most of those brilliant, color-perfect screens come from Samsung’s factories.</p>
          <p class="mt-4">Imagine Apple demanding 100 million new OLED panels for an “iPhone Air.” If Samsung refused, Apple’s supply chain would collapse—and consumers would likely flock to Samsung’s own handsets. But Samsung earns far more selling displays to Apple than it could ever gain by boycotting its rival.</p>
        </section>

        <!-- Section 2 -->
        <section>
          <h2 class="mb-4 text-2xl font-semibold">The Billion-Dollar Default: Google &amp; Apple</h2>
          <p>Beyond hardware lies software. Google pays Apple roughly $20 billion annually to remain Safari’s default search engine on iPhones. Each untapped click means millions of searches funnel into Google’s index—cementing its dominance.</p>
          <p class="mt-4">When the U.S. Justice Department sued Google for antitrust, this deal came under fire as “anti-competitive.” Yet for Apple it’s easy revenue, and for Google it’s an unassailable default. Change this arrangement, and both giants would feel the impact immediately.</p>
        </section>

        <!-- Full-width visual -->
        <div class="my-12 lg:col-span-2">
          <img src="https://picsum.photos/1200/400?economics" alt="Supply chain" class="h-auto w-full rounded-lg shadow" />
          <figcaption class="mt-2 text-center text-sm text-gray-500">Figure: When rivalry meets cooperation, big tech’s true power emerges.</figcaption>
        </div>

        <!-- Section 3 -->
        <section>
          <h2 class="mb-4 text-2xl font-semibold">Google &amp; Samsung: A Symbiotic Showcase</h2>
          <p>Google demos often feature Samsung Galaxy devices—despite Google’s own Pixel lineup. From Circle to Search to Gemini AI, new Android features debut on Galaxy handsets first, underscoring how Google and Samsung rely on each other to reach billions of users.</p>
          <p class="mt-4">Samsung needs Google’s Android backbone and Play services; Google needs Samsung’s market leadership. Recent court filings reveal Google even pays Samsung “enormous sums” to pre-install its AI assistant—another reminder that in tech, economics override rivalry.</p>
        </section>
      </div>

      <!-- Conclusion -->
      <section class="prose prose-lg mx-auto mt-16 text-gray-800">
        <h2 class="mb-4 text-2xl font-semibold">The Economics of Rivalry</h2>
        <p>In tech’s grand chessboard, public competition often masks behind-the-scenes cooperation. From OLED supply chains to default search deals to mutual product showcases, Apple, Samsung, and Google remain locked in a dance where mutual benefit outpaces marketing bravado.</p>
        <p>As consumers, we see the headlines; as participants in the market, we benefit from innovation fueled by these unlikely partnerships. And as observers, we witness how money and scale dictate the rules of engagement—even among the fiercest rivals.</p>
      </section>
    </article>
  </body>
</html>






