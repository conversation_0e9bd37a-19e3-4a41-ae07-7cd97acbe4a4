"""
Module: subtask_refiner.py

This module provides the function `refine_dependent_subtasks`, which checks the current task status for any pending subtasks that depend on a recently completed subtask. It uses an LLM chain to determine if the pending subtask description needs to be updated (or split) based on the new information provided by the completed subtask. If revisions are required, the function deletes the original pending subtask and adds the revised subtasks using utility functions.
"""

import json
import os
import re
import logging
import asyncio
from typing import Dict, Any, <PERSON><PERSON>, List
from collections import OrderedDict

from langchain_openai import ChatOpenAI
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import Str<PERSON>utputParser
from app.core.config import settings
from app.utils.task_status import (
    delete_subtask,
    add_subtask,
    replace_dependency,
    get_subtask_details,
)

from app.utils.token_counter import count_tokens

# reuse the same analysis logger configured in BaseAgent
ANALYSIS_LOGGER = logging.getLogger("agent_analysis")


# ──────────────────────────────────────────────────────────────
#                SHARED LLM CLIENT & PROMPT SETUP
# ──────────────────────────────────────────────────────────────

# instantiate one shared ChatGoogleGenerativeAI client
LLM_CLIENT = ChatGoogleGenerativeAI(
    api_key=settings.GOOGLE_API_KEY,
    model="gemini-2.0-flash",
    temperature=0.0,
)

# build the prompt template once
PROMPT_TEMPLATE = ChatPromptTemplate.from_template(
    """
                # **Role:**
                You are part of a multi ai agent system that operates on and completes subtasks created by a task planner. You are called the **Subtask Refiner**. You will be given data and your job is to check if a pending subtask must be updated and split into multiple subtasks based on the completed subtask’s new information. It is not a compulsion to create new subtasks. Only create subtasks when you find it is necessary or will add value.


                ---

                ## **Data You Will Be Provided:**


                **All Subtasks Data:** Contains a json of all the current subtasks in the task status including all the completed and pending subtasks.
                **Completed Subtask Details:** Description of the Completed Subtask which the pending subtask is dependent on and the output generated by the complted subtask for more information that will help you create the new subtasks
                **Pending Subtask Description:** Description of the Pending Subtask that will inherit from the completed subtask and is dependent on it.
                **Dependencies Details:** Information from other dependencies of the pending subtask description for better context of the problem.

                ---


                ## **Objectives:**
                1. Decide on Edit vs. No Change:
                2. If the completed subtask’s output does not add relevant or specific info that could be used to change the subtask description, respond with no change.
                3. If it does add information that should refine or split the pending subtask, respond with new or revised subtasks.
                4. Keep dependencies logical. This is very important.
                5. Do not create a subtask that creates a final output. This is already taken care of by an aggregator agent that, in the end, takes all the information and gives the user a final output.
                6. Output strictly the specified JSON format.
                7. Given there are multiple dependencies, don't be selfish for your subtasks. There might be additional things that the other subtasks might want.


                ---
                
                **Agents Available:**

                - **You have the following agents available to complete these tasks. You don't have to specify what agent you want to use but keep this information in the back of your mind to create better tasks that can be properly handled:**
                - IMPORTANT: All models are multi model and can be given images, documents in uploads if needed.
                  - Basic LLM: This can complete normal tasks
                  - Reasoning LLM: This is a reasoning model best for complex tasks.
                  - Web Search Agent: This can search the web to find brand new information
                  - PDF Generator Agent: This agent takes research provided, plans the document like a report, literary review, research paper, article, biography, etc and then using this generated plan, writes the docuemnt and then produces the pdf. This means, we don't need to add a subtask to write the document. PDFGenerator can write the document and generate a pdf out of it. This means, just use pdf generator when creating the document. Never use for research. And give it all the research you can
                  
                ---


                ## **Output Format:**


                Your response must consist either No Change response or One or More Revised Subtasks Response


                1. A JSON object in exactly one of these forms:
                - **No Change:** 
                    ```json
                    {{
                        "subtasks": []
                    }}
                    ```
                - **One or More Revised Subtasks:** 
                    ```json
                    {{
                        "subtasks": [
                            {{
                                "id": "1",
                                "desc": "New subtask description 1",
                                "deps": ["depID1", "depID2"],
                                "uploads": []

                            }},
                            {{
                                "id": "2",
                                "desc": "New subtask description 2",
                                "deps": ["depID1"],
                                "uploads": ["upload_1.md", "upload_image_1.png"]
                            }}
                        ]
                    }}
                    ```
                Each object inside "subtasks" must have three keys:
                - `"subtaskID"` (string): The new subtask identifier.
                - `"description"` (string): The subtask description.
                - `"dependencies"` (array of strings): The list of dependency subtask IDs.
                - `"uploads"` (array of strings): Filenames of any user‐uploads (MD or image) that this subtask needs.

                No other text or fields should appear.


                ---


                ## **Examples:**


                ### **Example 1:**


                **All Subtasks Data:** 
                ```json
                {{
                    "subtasks": [
                        "criteria": "Think of 3 famous universities in london and find about their ai professors. ",                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   
                        {{
                        "id": "1",
                        "desc": "Identify 3 famous universities in London that have AI programs or departments.",
                        "deps": [],
                        "uploads": [],
                        "status": "completed",
                        "output_file": "./app/knowledge_base/1_20250309001612.md"
                        }},
                        {{
                        "id": "2",
                        "desc": "Collect information on AI professors at the indentified universities.",
                        "deps": ["1"],
                        "uploads": [],
                        "status": "pending",
                        }}
                    ]
                }}
                ```


                **Completed Subtask Description:** 
                "Identify 3 famous universities in London that have AI programs or departments."


                **Completed Subtask Output:** 
                "Imperial, UCL, and KCL have AI departments."


                **Pending Subtask Description:** 
                "Find AI researchers in those universities."


                **Possible Output:**: 
                ```json
                {{
                    "subtasks": [
                        {{
                            "id": "2",
                            "desc": "Find AI researchers at Imperial College",
                            "deps": ["1"],
                            "uploads": [],
                        }},
                        {{
                            "id": "2.1",
                            "desc": "Find AI researchers at UCL",
                            "deps": ["1"],
                            "uploads": [],
                        }},
                        {{
                            "id": "2.2",
                            "desc": "Find AI researchers at KCL",
                            "deps": ["1"],
                            "uploads": [],
                        }}
                    ]
                }}
                ```
                (Reason "Not to be added in the final output": Specific university names were provided in the completed subtask output. This is information that could be used ot create new subtasks, so the pending subtask is split accordingly.)


                ---


                ### **Example 2:**


                **All Subtasks Data:** 
                ```json
                {{
                    "subtasks": [
                        "criteria": "Generate 3 random tech resumes and then generate pdfs for all of their resumes. ",
                        {{
                        "id": "1",
                        "desc": "Generate a resume for a person in tech doing software engineering.",
                        "deps": [],
                        "uploads": [],
                        "status": "completed",
                        "output_file": "./app/knowledge_base/1_20250309002000.md"
                        }},
                        {{
                        "id": "2",
                        "desc": "Generate a resume for the second person in tech doing software engineering.",
                        "deps": [],
                        "uploads": [],
                        "status": "in_progress",
                        }},
                        {{
                        "id": "3",
                        "desc": "Generate a resume for the third person in tech doing software engineering.",
                        "deps": [],
                        "uploads": [],
                        "status": "in_progress",
                        }},
                        {{
                        "id": "4",
                        "desc": "Generate a pdf for each resume",
                        "deps": [1,2,3],
                        "uploads": [],
                        "status": "pending",
                        }}
                    ]
                }}
                ```


                **Completed Subtask Description:** 
                "Generate a resume for a person in tech doing software engineering."


                **Completed Subtask Output:** 
                "John Doe Fabrication Engineer | [Phone Number] | [Email Address] | [LinkedIn Profile]


                Professional Summary Highly motivated and detail-oriented Software Engineer with extensive experience in engineering design and process optimization. Skilled in creating innovative solutions to complex problems and delivering high-quality results.


                Professional Experience
                Lead Software Engineer, Lockheed Martin, Bethesda, MD


                Led the design,..."


                **Pending Subtask Description:** 
                "Generate a pdf for each resume."


                **Possible Output:**: 
                ```json
                {{
                    "subtasks": [
                        {{
                            "id": "4",
                            "desc": "Generate the pdf for the first resume",
                            "deps": ["1"],
                            "uploads": [],
                        }},
                        {{
                            "id": "4.1",
                            "desc": "Generate the pdf for the second resume",
                            "deps": ["2"],
                            "uploads": [],
                        }},
                        {{
                            "id": "4.2",
                            "desc": "Generate the pdf for the third resume",
                            "deps": ["3"],
                            "uploads": [],
                        }}
                    ]
                }}
                ```
                (Reason "Not to be added in the final output": As you can see, we need seperate pdf for each person. For that, we need to create seperate subtasks.)

                ---
                
                ### **Example 3:**


                **All Subtasks Data:** 
                ```json
                {{
                    "subtasks": [
                        "criteria": "Travelling to India for 1 month. I want to experience the food, culture of different places and nature. Give me an itinerary in a pdf for this trip.",
                        {{
                        "id": "1",
                        "desc": "Find famous food spots in India.",
                        "deps": [],
                        "uploads": [],
                        "status": "completed",
                        "output_file": "./app/knowledge_base/1_20250309002000.md"
                        }},
                        {{
                        "id": "2",
                        "desc": "Find cultural places in different states in India.",
                        "deps": [],
                        "uploads": [],
                        "status": "in_progress",
                        }},
                        {{
                        "id": "3",
                        "desc": "Find best nature site seeing spots in India.",
                        "deps": [],
                        "uploads": [],
                        "status": "completed",
                        }},
                        {{
                        "id": "4",
                        "desc": "Generate a 30 day itinerary with food spots, cultural places and best nature site seeing spots.",
                        "deps": [1,2,3],
                        "uploads": [],
                        "status": "pending",
                        }}
                    ]
                }}
                ```


                **Completed Subtask Description:** 
                "Find best nature site seeing spots in India."


                **Completed Subtask Output:** 
                "Day 1-5 we will be in kanto, then we will shift to tohoku till day 10. We will go to hokkaido till 15 and chubu till 19th day. Then, shikoku for 2-3 days till day 22 and kansai till 28. Then finally kyushu to okinawa on the final days."


                **Pending Subtask Description:** 
                "Generate a 30 day itinerary for the trip."


                **Possible Output:**: 
                ```json
                {{
                    "subtasks": [
                        {{
                            "id": "4",
                            "desc": "create a high-level 30-day itinerary plan, grouping researched hidden gems, rural areas, and tech locations by region and planning the overall flow and travel time between regions.",
                            "deps": ["1"],
                            "uploads": [],
                        }},
                        {{
                            "id": "4.1",
                            "desc": "Generate a detailed plan for day 1-5 (Kanto) of the trip beginning the trip",
                            "deps": ["4, 1, 2, 3"],
                            "uploads": [],
                        }},
                        {{
                            "id": "4.2",
                            "desc": "Generate a detailed plan for the 6-10 (Tohoku) of the trip",
                            "deps": ["4, 1, 2, 3"],
                            "uploads": [],
                        }},
                        {{
                            "id": "4.3",
                            "desc": "Generate a detailed plan for the 11-15 (Hokkaido) of the trip",
                            "deps": ["4, 1, 2, 3"],
                            "uploads": [],
                        }},
                        {{
                            "id": "4.4",
                            "desc": "Generate a detailed plan for the 16-19 (Chubu) of the trip",
                            "deps": ["4, 1, 2, 3"],
                            "uploads": [],
                        }},
                        {{
                            "id": "4.5",
                            "desc": "Generate a detailed plan for the 20-22 (Shikoku) of the trip",
                            "deps": ["4, 1, 2, 3"],
                            "uploads": [],
                        }},
                        {{
                            "id": "4.6",
                            "desc": "Generate a detailed plan for the 23-28 (Kansai) of the trip",
                            "deps": ["4, 1, 2, 3"],
                            "uploads": [],
                        }},
                        {{
                            "id": "4.7",
                            "desc": "Generate a detailed plan for the 29-30 (Kyushu -> Okinawa) of the trip ending the trip",
                            "deps": ["4, 1, 2, 3"],
                            "uploads": [],
                        }},
                    ]
                }}
                ```
                (Reason "The output for subtask 4 needs to be detailed but the current description is not detailed enough. We need a overview plan of the trip based on regions of exploration. Then, given how many days we are going to spend in a region, we can generate initerary based on that making it much better quality" )
                
                ---
                
                ### **Example 4:**


                **All Subtasks Data:** 
                ```json
                {{
                "subtasks": [
                    "criteria": "find the latest mkbhd video and give a full summary of everything talked about in it.",
                    {{
                    "id": "1",
                    "desc": "Find the link to the latest MKBHD video on YouTube.",
                    "deps": [],
                    "uploads": [],
                    "status": "completed",
                    "output_file": "./app/knowledge_base/1_20250309001612.md"
                    }},
                    {{
                    "id": "2",
                    "desc": "Use the provided link to find information about the video",
                    "deps": ["1"],
                    "uploads": [],
                    "status": "pending",
                    }}
                ]
                }}
                ```


                **Completed Subtask Description:** 
                "Find the link to the latest MKBHD video on YouTube."


                **Completed Subtask Output:** 
                "
                ### Latest MKBHD Video:
                    **Title:** CMF Phone 2 Pro: Budget Phone of the Year!
                    **Link:** [https://www.youtube.com/watch?v=hIzjHBwjGCk](https://www.youtube.com/watch?v=hIzjHBwjGCk)
                    **Upload Date:** 7 days ago (from the crawl date)
                    **Views:** 1,524,298 views
                    **Description Snippet:** Modular. Cheap. And Fun.

                ### Other Recent Videos:
                    **I Have A Problem with the Light Phone III:** [https://www.youtube.com/watch?v=HdTCDxX-UnQ](https://www.youtube.com/watch?v=HdTCDxX-UnQ) (2 weeks ago, 2.5M views)
                    **Reviewing a Bionic Hand!:** [https://www.youtube.com/watch?v=VTs8wnMsh0k](https://www.youtube.com/watch?v=VTs8wnMsh0k) (3 weeks ago, 2.3M views)
                    **Oppo Find X8 Ultra: The Bar's Been Raised!:** [https://www.youtube.com/watch?v=p24qhUFIe68](https://www.youtube.com/watch?v=p24qhUFIe68) (4 weeks ago, 2.4M views)
                "


                **Pending Subtask Description:** 
                "Use the provided link: [https://www.youtube.com/watch?v=hIzjHBwjGCk] to find information about the video"


                **Possible Output:**: 
                ```json
                {{
                    "subtasks": [
                        {{
                            "id": "2",
                            "desc": "Find AI researchers at Imperial College",
                            "deps": ["1"],
                            "uploads": [],
                        }},
                        {{
                            "id": "2.1",
                            "desc": "Find AI researchers at UCL",
                            "deps": ["1"],
                            "uploads": [],
                        }},
                        {{
                            "id": "2.2",
                            "desc": "Find AI researchers at KCL",
                            "deps": ["1"],
                            "uploads": [],
                        }}
                    ]
                }}
                ```
                (Reason "Not to be added in the final output": Specific university names were provided in the completed subtask output. This is information that could be used ot create new subtasks, so the pending subtask is split accordingly.)
                
                ---
                
                Below are three new “Example” blocks (labelled Example 5, 6, and 7) that you can insert into the **Examples** section of your prompt. Each follows the same structure as the existing examples and demonstrates how to use the new `"uploads"` field.

                ---
                ### **Example 5: Image‐Based Questions Extraction**

                **All Subtasks Data:** 
                {
                "subtasks": [
                    "criteria": "Find all the questions shown in the provided image and then answer each question.",
                    {
                    "id": "1",
                    "desc": "Find all the questions in the given image.",
                    "deps": [],
                    "uploads": ["upload_image_1.png"],
                    "status": "completed",
                    "output_file": "./app/knowledge_base/1_20250501120000.md"
                    },
                    {
                    "id": "2",
                    "desc": "For each question, generate an answer.",
                    "deps": ["1"],
                    "uploads": [],
                    "status": "pending"
                    }
                ]
                }

                **Added Documents/Images:**

                * `upload_image_1.png`

                **Completed Subtask Description:**
                `"Find all the questions in the given image."`

                **Completed Subtask Output:**

                List of questions found in upload_image_1.png:
                1. What is the capital of France?
                2. Who wrote 'To Kill a Mockingbird'?
                3. Solve 12 × 8.
                4. Name the largest planet in our solar system.
                5. What year did the Berlin Wall fall?

                **Pending Subtask Description:**
                `"For each question, generate an answer."`

                **Possible Output:**

                {
                "subtasks": [
                    {
                    "id": "2",
                    "desc": "Provide the answer to 'What is the capital of France?'",
                    "deps": ["1"],
                    "uploads": ["upload_image_1.png"]
                    },
                    {
                    "id": "2.1",
                    "desc": "Provide the answer to 'Who wrote To Kill a Mockingbird?'",
                    "deps": ["1"],
                    "uploads": ["upload_image_1.png"]
                    },
                    {
                    "id": "2.2",
                    "desc": "Provide the answer to 'Solve 12 × 8.'",
                    "deps": ["1"],
                    "uploads": ["upload_image_1.png"]
                    },
                    {
                    "id": "2.3",
                    "desc": "Provide the answer to 'Name the largest planet in our solar system.'",
                    "deps": ["1"],
                    "uploads": ["upload_image_1.png"]
                    },
                    {
                    "id": "2.4",
                    "desc": "Provide the answer to 'What year did the Berlin Wall fall?'",
                    "deps": ["1"],
                    "uploads": ["upload_image_1.png"]
                    }
                ]
                }

                (Reason: Five distinct questions were extracted from the image, so each one becomes its own pending‐answer subtask, all requiring access to the original image.)

                ---

                ### **Example 6: Document Sections Breakdown**

                **All Subtasks Data:**

                {
                "subtasks": [
                    "criteria": "Summarize each major section of the uploaded specification document.",
                    {
                    "id": "1",
                    "desc": "Extract all section headings from upload_1.md.",
                    "deps": [],
                    "uploads": ["upload_1.md"],
                    "status": "completed",
                    "output_file": "./app/knowledge_base/1_20250501123000.md"
                    },
                    {
                    "id": "2",
                    "desc": "Generate detailed notes for each extracted section.",
                    "deps": ["1"],
                    "uploads": [],
                    "status": "pending"
                    }
                ]
                }

                **Added Documents/Images:**

                * `upload_1.md`

                **Completed Subtask Description:**
                `"Extract all section headings from upload_1.md."`

                **Completed Subtask Output:**

                Sections found in upload_1.md:
                - Introduction
                - System Architecture
                - API Endpoints
                - Data Models
                - Security Considerations
                - Deployment Strategy

                **Pending Subtask Description:**
                `"Generate detailed notes for each extracted section."`

                **Possible Output:**

                {
                "subtasks": [
                    {
                    "id": "2",
                    "desc": "Write detailed notes for the 'Introduction' section of upload_1.md.",
                    "deps": ["1"],
                    "uploads": ["upload_1.md"]
                    },
                    {
                    "id": "2.1",
                    "desc": "Write detailed notes for the 'System Architecture' section of upload_1.md.",
                    "deps": ["1"],
                    "uploads": ["upload_1.md"]
                    },
                    {
                    "id": "2.2",
                    "desc": "Write detailed notes for the 'API Endpoints' section of upload_1.md.",
                    "deps": ["1"],
                    "uploads": ["upload_1.md"]
                    },
                    {
                    "id": "2.3",
                    "desc": "Write detailed notes for the 'Data Models' section of upload_1.md.",
                    "deps": ["1"],
                    "uploads": ["upload_1.md"]
                    },
                    {
                    "id": "2.4",
                    "desc": "Write detailed notes for the 'Security Considerations' section of upload_1.md.",
                    "deps": ["1"],
                    "uploads": ["upload_1.md"]
                    },
                    {
                    "id": "2.5",
                    "desc": "Write detailed notes for the 'Deployment Strategy' section of upload_1.md.",
                    "deps": ["1"],
                    "uploads": ["upload_1.md"]
                    }
                ]
                }

                (Reason: Six section headings were identified, so each becomes its own subtask. All require the original document file.)

                ---

                ### **Example 7: Mixed Document + Image PDF Preparation**

                **All Subtasks Data:**

                {
                "subtasks": [
                    "criteria": "Create a PDF report that combines text from the spec document and annotated screenshots.",
                    {
                    "id": "1",
                    "desc": "Extract relevant text from upload_1.md and upload_image_2.png.",
                    "deps": [],
                    "uploads": ["upload_1.md", "upload_image_2.png"],
                    "status": "completed",
                    "output_file": "./app/knowledge_base/1_20250501124500.md"
                    },
                    {
                    "id": "2",
                    "desc": "Create a PDF combining extracted text and annotated images.",
                    "deps": ["1"],
                    "uploads": [],
                    "status": "pending"
                    }
                ]
                }

                **Added Documents/Images:**

                * `upload_1.md`
                * `upload_image_2.png`

                **Completed Subtask Description:**
                `"Extract relevant text from upload_1.md and upload_image_2.png."`

                **Completed Subtask Output:**

                • From upload_1.md: 
                - Overview of module A, B, and C.
                - Detailed API usage examples for module A.
                • From upload_image_2.png: 
                - Screenshot annotations for UI flow: Login → Dashboard → Settings.
                - Highlighted data‐entry form fields.

                **Pending Subtask Description:**
                `"Create a PDF combining extracted text and annotated images."`

                **Possible Output:**

                {
                "subtasks": [
                    {
                    "id": "2",
                    "desc": "Design PDF layout: arrange module overviews and include UI flow screenshots from upload_image_2.png.",
                    "deps": ["1"],
                    "uploads": ["upload_1.md", "upload_image_2.png"]
                    },
                    {
                    "id": "2.1",
                    "desc": "Generate the PDF: merge extracted text sections from upload_1.md with annotated screenshots from upload_image_2.png.",
                    "deps": ["2"],
                    "uploads": ["upload_1.md", "upload_image_2.png"]
                    }
                ]
                }

                (Reason: Two logical steps are needed—first design the layout, then produce the actual PDF file. Both subtasks require access to the original document and screenshot.)

                ---


                You can drop these three blocks immediately after **Example 4** in your prompt. Each follows the exact same pattern (showing `"uploads"`) and makes clear when and how to attach either an image file, a document file, or both.


                ---


                **Final Instructions:**
                - No other text or explanation or Reasoning beyond the json subtask output is allowed.
                - Don't be too aggresive give subtasks only if you think it is necessary and will improve the experience a lot, or is needed.
                - If we only need to generate one pdf, don't split it into multiple pdfs. Meaning, For a pdf task that requires one pdf, never split new subtasks!!!
                - IMPORTANT!: Keep things reasonable. Only create the new smaller subtasks while keeping in mind the flow of all subtask data, what has been done and what will be done. Keep the new subtask list logical and coherent with the complete list of subtasks.
                - Also, don't generate absurd amounts of subtasks. generate normally 1-5 subtasks at max. If there is a need more more than 5 subtasks, group descriptions of multiple subtasks to fit in 5 subtasks.
                - Don't mention specific subtasks in your descriptions. If you really have to mention them, just say incorporate additional information provided.
                - When writing a web or youtube link in the subtask, use the actual case sensitive text in strings. Don't write a short version of the link otherwise it can become a problem as you are providing false information. 
                - IMPORTANT: DON'T mention subtask IDs specifically in your new subtask descriptions.
                - IMPORTANT!: Give all links in case-sensitive format.
                - IMPORTANT!: When the subtask asks for one pdf to be generated, even if you want to break down the subtask, only keep one new subtask to generate a pdf. We don't want multiple document generation when the user wants only one.


                # Now, generate the output for the given data:

                **All Subtasks Data (the complete task status file with all subtasks):** {all_subtasks_str} \n\n

                **Completed Subtask Details (desc + output):**{completed_details} \n\n
                
                **Pending Subtask Description:** {pending_desc}

                **Dependencies Details:** {dep_details} \n\n
                
                # OUTPUT (Only create subtasks if it is too much for the llm or you think it will produce significant improvement):

                """
)

# single parser instance
PARSER = StrOutputParser()


# ──────────────────────────────────────────────────────────────
#                          UTILITIES
# ──────────────────────────────────────────────────────────────
def summarize(data, max_length=100):
    s = str(data)
    return s if len(s) <= max_length else s[:max_length] + "..."

def clean_dependencies(deps: List[str], self_id: str) -> List[str]:
    """
    - Removes any occurrence of self_id
    - Deduplicates while preserving original order
    """
    unique = list(OrderedDict.fromkeys(deps))
    return [d for d in unique if d != self_id]


# ──────────────────────────────────────────────────────────────
#                   SINGLE-DEPENDENT REFINEMENT
# ──────────────────────────────────────────────────────────────
async def _refine_one_pending_dependent(
    completed_details: str,
    all_subtasks_str: str,
    pending_sub: Dict[str, Any],
    logger: logging.Logger
) -> Tuple[str, List[Dict[str, Any]]]:
    """
    Process a single pending dependent via LLM and return:
      (old_subtask_id, list_of_new_subtasks)
    If no revisions, returns an empty list for new subtasks.
    """
    old_id = pending_sub["id"]
    pending_desc = pending_sub.get("desc", "")

    # gather full context of all its dependencies
    dep_details = "\n\n".join(
        get_subtask_details(dep_id) for dep_id in pending_sub.get("deps", [])
    )
    
    # render the exact prompt for token counting
    filled_prompt = PROMPT_TEMPLATE.format_prompt(
        all_subtasks_str=all_subtasks_str,
        completed_details=completed_details,
        pending_desc=pending_desc,
        dep_details=dep_details,
    ).to_string()
    in_tokens = count_tokens(filled_prompt)
    ANALYSIS_LOGGER.info(
        "",
        extra={
            "agent_id": "subtask_refiner",
            "model": LLM_CLIENT.model,
            "label": "input_tokens",
            "duration": float(in_tokens),
            "unit": ""
        }
    )
    logger.info(f"[refiner] input_tokens={in_tokens}")

    logger.info(f"Dependent subtask {old_id} desc: {pending_desc}")
    logger.info("Initiating LLM to check if subtask description needs editing...")

    # reuse shared prompt & client
    chain = PROMPT_TEMPLATE | LLM_CLIENT | PARSER

    response_chunks: List[str] = []
    async for chunk in chain.astream({
        "all_subtasks_str": all_subtasks_str,
        "completed_details": completed_details,
        "pending_desc": pending_desc,
        "dep_details": dep_details,
    }):
        response_chunks.append(chunk)

    final = ''.join(response_chunks).strip().lower()
    logger.info(f"LLM final response for {old_id}: '{final}'")
    
    out_tokens = count_tokens(final)
    ANALYSIS_LOGGER.info(
        "",
        extra={
            "agent_id": "subtask_refiner",
            "model": LLM_CLIENT.model,
            "label": "output_tokens",
            "duration": float(out_tokens),
            "unit": ""
        }
    )
    logger.info(f"[refiner] output_tokens={out_tokens}")


    # clean and parse JSON
    cleaned = re.sub(r"^```json\s*", "", final)
    cleaned = re.sub(r"\s*```$", "", cleaned).strip()
    idx = cleaned.rfind('}')
    if idx != -1:
        cleaned = cleaned[:idx+1]

    try:
        revision = json.loads(cleaned)
    except Exception as e:
        logger.error(f"Failed to parse LLM output for {old_id}: {e}")
        return old_id, []

    new_subs = revision.get("subtasks", [])
    return old_id, new_subs


# ──────────────────────────────────────────────────────────────
#                MAIN PARALLEL-THEN-SEQUENTIAL REFINER
# ──────────────────────────────────────────────────────────────
async def refine_dependent_subtasks(
    task_status: Dict[str, Any],
    completed_subtask_id: str,
    logger: logging.Logger
) -> None:
    """
    Parallelize LLM checks for all dependents, then apply deletions/additions sequentially.
    """
    logger.info(f"Starting search of dependent subtasks for subtask {completed_subtask_id} before completion.")

    subtasks = task_status.get("subtasks", {}).get("subtasks", [])
    all_subtasks_str = json.dumps(task_status, indent=2)

    # get completed details
    completed = next((st for st in subtasks if st["id"] == completed_subtask_id), None)
    if not completed:
        logger.error(f"Completed subtask {completed_subtask_id} not found in task status.")
        return

    completed_details = get_subtask_details(completed_subtask_id)
    logger.info(f"Completed subtask context:\n{completed_details}")

    # 1) discover all pending dependents
    dependents = [
        st for st in subtasks
        if st.get("status") in ("pending", "in_progress")
           and completed_subtask_id in st.get("deps", [])
    ]
    if not dependents:
        logger.info("No pending dependents found; skipping refiner.")
        return

    # 2) run LLM checks in parallel, but throttle concurrency
    semaphore = asyncio.Semaphore(5)
    async def limited(sub): 
        async with semaphore:
            return await _refine_one_pending_dependent(completed_details, all_subtasks_str, sub, logger)

    tasks = [limited(st) for st in dependents]
    results = await asyncio.gather(*tasks)

    # 3) collect all revisions
    replacement_mapping: Dict[str, List[Dict[str, Any]]] = {
        old: new_list for old, new_list in results if new_list
    }
    if not replacement_mapping:
        logger.info(f"LLM indicates no revisions needed for any dependents of {completed_subtask_id}.")
        return

    # 4) sequentially apply deletes, adds, and dep replacements
    for old_id, new_subs in replacement_mapping.items():
        logger.info(f"Applying {len(new_subs)} revisions for dependent {old_id}")
        delete_subtask(old_id)
        for new in new_subs:
            add_subtask(
                location_id=old_id,
                new_subtask_id=new.get("id", old_id),
                desc=new.get("desc", ""),
                deps=clean_dependencies(new.get("deps", []), new.get("id", old_id)),
                uploads=new.get("uploads", []),
                status="pending"
            )
            logger.info(f"Added revised subtask {new.get('id')} for {old_id}")
        replace_dependency(old_id, [s.get("id") for s in new_subs])
        logger.info(f"Replaced dependency '{old_id}' with {[s.get('id') for s in new_subs]} in all subtasks.")

    # 5) final sweep log
    logger.info(f"Replacement mapping: {json.dumps(replacement_mapping, indent=2)}")
    for st in subtasks:
        if st.get("status") in ("pending", "in_progress"):
            for old_id in replacement_mapping:
                if old_id in st.get("deps", []):
                    logger.info(f"Pending subtask {st['id']} still lists original dependency '{old_id}'.")


# ──────────────────────────────────────────────────────────────
#             OPTIONAL DEPENDENCY-USAGE LOGGER (unchanged)
# ──────────────────────────────────────────────────────────────
def log_subtask_dependency_usage(
    task_status: Dict[str, Any],
    target_subtask_id: str,
    logger: logging.Logger
):
    """
    Logs any subtasks that currently depend on the given target_subtask_id.
    """
    subtasks = task_status.get("subtasks", {}).get("subtasks", [])
    logger.info(f"Checking dependency usage for subtask '{target_subtask_id}'...")
    for st in subtasks:
        if target_subtask_id in st.get("deps", []):
            logger.info(f"Subtask '{st['id']}' depends on '{target_subtask_id}'")
