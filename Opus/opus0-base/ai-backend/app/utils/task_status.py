# app/utils/task_status.py:

import logging
import os
import json
from typing import Dict, Any, List, Optional, Set
from collections import OrderedDict
from app.utils.constants import get_knowledge_base_dir


def _task_status_file(chat_id: str) -> str:
    """Return the task_status.json path for the given chat."""
    kb_dir = get_knowledge_base_dir(chat_id)
    return os.path.join(kb_dir, "task_status.json")


def initialize_task_status(chat_id: str, task_id: str, subtasks: Dict) -> None:
    """
    Overwrites task_status.json with the new task data.

    :param task_id: Unique identifier for the task.
    :param subtasks: Dictionary containing task details (criteria and subtasks).
    """
    kb_dir = get_knowledge_base_dir(chat_id)
    os.makedirs(kb_dir, exist_ok=True)

    # Prepare the new task status structure
    task_status = {
        "task_id": task_id,
        "status": "initialized",
        "subtasks": subtasks
    }

    # Overwrite the task_status.json file with the new task data
    ts_file = _task_status_file(chat_id)
    with open(ts_file, "w") as file:
        json.dump(task_status, file, indent=4)
    print(f"Task status updated for task ID: {task_id}")


def read_task_status(chat_id: str) -> Dict:
    """
    Reads and returns the content of the task_status.json file.
    """
    ts_file = _task_status_file(chat_id)
    with open(ts_file, "r") as file:
        return json.load(file)


def update_subtask_status(chat_id: str, subtask_id: str, new_status: str) -> None:
    task_status = read_task_status(chat_id)
    for subtask in task_status["subtasks"]["subtasks"]:
        if subtask["id"] == subtask_id:
            subtask["status"] = new_status
            break

    with open(_task_status_file(chat_id), "w") as file:
        json.dump(task_status, file, indent=4)
    print(f"Subtask {subtask_id} status updated to {new_status}")


def update_task_status(chat_id: str, subtasks_value: Any) -> None:
    """
    Persist the `"subtasks"` section in task_status.json.
    """
    ts = read_task_status(chat_id)
    ts["subtasks"] = subtasks_value
    with open(_task_status_file(chat_id), "w") as file:
        json.dump(ts, file, indent=4)
    print(f"Subtasks section overwritten with new data.")

def get_subtask_uploads(chat_id: str, subtask_id: str) -> List[str]:
    """
    Return the `uploads` list for the given subtask ID (or [] if none).
    """
    logger = logging.getLogger(__name__)
    ts = read_task_status(chat_id)
    logger.info(f"[TSUTIL] read task_status.json → {ts['subtasks']['subtasks']!r}")
    for st in ts.get("subtasks", {}).get("subtasks", []):
        logger.info(f"[TSUTIL] checking subtask {st.get('id')!r}, uploads={st.get('uploads')!r}")
        if st.get("id") == subtask_id:
            matched = st.get("uploads", []) or []
            logger.info(f"[TSUTIL] matched uploads for {subtask_id}: {matched!r}")
            return matched
    logger.info(f"[TSUTIL] no uploads found for {subtask_id}, returning []")
    return []

# ---------------------------------------------------------------------------
# New Helper Functions: delete_subtask, add_subtask, and add_multiple_subtasks
# ---------------------------------------------------------------------------

def delete_subtask(chat_id: str, subtask_id: str) -> None:
    """
    Deletes the subtask with the specified ID from the task_status.json file.

    :param subtask_id: The ID of the subtask to remove.
    """
    try:
        task_status = read_task_status(chat_id)
    except Exception as e:
        print(f"Cannot delete subtask; failed to read task status: {e}")
        return

    # Validate structure
    if "subtasks" not in task_status or "subtasks" not in task_status["subtasks"]:
        print("Error: Task status structure is invalid; missing 'subtasks' key.")
        return

    subtask_list = task_status["subtasks"]["subtasks"]
    new_subtask_list = [st for st in subtask_list if st.get("id") != subtask_id]

    if len(new_subtask_list) == len(subtask_list):
        print(f"Warning: Subtask {subtask_id} not found; nothing was deleted.")
        return

    task_status["subtasks"]["subtasks"] = new_subtask_list

    try:
        with open(_task_status_file(chat_id), "w") as f:
            json.dump(task_status, f, indent=4)
        print(f"Deleted subtask {subtask_id} from task status.")
    except Exception as e:
        print(f"Error writing updated task status after deleting subtask {subtask_id}: {e}")


def add_subtask(chat_id: str,
                location_id: str,
                new_subtask_id: str,
                desc: str,
                deps: List[str],
                uploads: Optional[List[str]] = None,
                status: str = "pending",
                output_file: str = None) -> None:

    """
    Inserts a new subtask into the task_status.json file immediately after the subtask with ID == location_id.
    If a subtask with new_subtask_id already exists, it is removed first to avoid duplicates.
    If the location_id is not found, the new subtask is appended to the end.
    """
    try:
        task_status = read_task_status(chat_id)
    except Exception as e:
        print(f"Cannot add subtask; failed to read task status: {e}")
        return

    # Validate structure
    if "subtasks" not in task_status or "subtasks" not in task_status["subtasks"]:
        print("Error: Task status structure is invalid; missing 'subtasks' key.")
        return

    subtask_list = task_status["subtasks"]["subtasks"]

    # ────────────────────────────────────────────────────
    # Remove any existing subtask with the same ID first
    subtask_list = [st for st in subtask_list if st.get("id") != new_subtask_id]
    # ────────────────────────────────────────────────────

    # Create the new subtask object
    if uploads is None:
        uploads = []
    new_subtask = {
        "id": new_subtask_id,
        "desc": desc,
        "deps": deps,
        "uploads": uploads,
        "status": status
    }
    if output_file is not None:
        new_subtask["output_file"] = output_file

    # Find the index of the subtask matching location_id
    idx = next((i for i, st in enumerate(subtask_list) if st.get("id") == location_id), None)

    # Insert or append
    if idx is not None:
        subtask_list.insert(idx + 1, new_subtask)
        print(f"Inserted new subtask {new_subtask_id} after subtask {location_id}.")
    else:
        subtask_list.append(new_subtask)
        print(f"Location {location_id} not found. Appended new subtask {new_subtask_id} at the end.")

    # Write back
    task_status["subtasks"]["subtasks"] = subtask_list
    try:
        with open(_task_status_file(chat_id), "w") as f:
            json.dump(task_status, f, indent=4)
        print(f"Added subtask {new_subtask_id} successfully.")
    except Exception as e:
        print(f"Error writing updated task status after adding subtask {new_subtask_id}: {e}")
        
        
def replace_dependency(
    chat_id: str,
    old_id: str,
    new_ids: List[str],
    skip_ids: Optional[Set[str]] = None
) -> None:
    """
    Reads the current task_status.json and replaces any occurrences of `old_id`
    in a subtask's `deps` list with the list of new IDs in `new_ids`.

    - Any subtask whose own ID is in `skip_ids` will not be modified.
    - After insertion, deps are deduplicated (order preserved) and any self-reference removed.

    :param old_id: The subtask ID to replace.
    :param new_ids: List of IDs to insert in place of `old_id`.
    :param skip_ids: Set of subtask IDs to skip entirely (defaults to set(new_ids)).
    """
    if skip_ids is None:
        skip_ids = set(new_ids)

    try:
        with open(_task_status_file(chat_id), "r") as f:
            task_status = json.load(f)
    except Exception as e:
        print(f"Cannot replace dependency; failed to read task status: {e}")
        return

    subtasks = task_status.get("subtasks", {}).get("subtasks", [])
    updated = False

    for st in subtasks:
        sid = st.get("id")
        if sid in skip_ids:
            continue

        original_deps = st.get("deps", [])
        new_deps: List[str] = []
        for dep in original_deps:
            if dep == old_id:
                new_deps.extend(new_ids)
            else:
                new_deps.append(dep)

        # --- functional dedupe + remove self-references ---
        cleaned = [
            d
            for d in OrderedDict.fromkeys(new_deps)   # preserve order, drop dupes
            if d != sid                               # drop any self-reference
        ]

        if cleaned != original_deps:
            st["deps"] = cleaned
            updated = True

    if updated:
        try:
            with open(_task_status_file(chat_id), "w") as f:
                json.dump(task_status, f, indent=4)
            print(f"Replaced dependency '{old_id}' with {new_ids}, skipped {skip_ids}.")
        except Exception as e:
            print(f"Error writing updated task status after replacing dependency: {e}")
    else:
        print(f"No occurrences of '{old_id}' found (excluding skip_ids).")
        

# ──────────────────────────────────────────────────────────────
# Helper: fetch a subtask’s description + (optional) output text
# ──────────────────────────────────────────────────────────────
def get_subtask_details(chat_id: str, subtask_id: str) -> str:
    """
    Return a Markdown-formatted string containing the subtask’s description
    and, if present, the full contents of its output file.

    Used by the Subtask Refiner so it can feed the LLM with all dependency
    context.
    """
    try:
        ts = read_task_status(chat_id)
    except Exception as exc:
        return f"### {subtask_id}\n[error reading task_status: {exc}]"

    sub = next((st for st in ts["subtasks"]["subtasks"] if st["id"] == subtask_id), None)
    if not sub:
        return f"### {subtask_id}\n[not found]"

    desc = sub.get("desc", "")
    output_file = sub.get("output_file")

    output_text = ""
    if output_file:
        # normalise path
        if not os.path.isabs(output_file):
            output_file = os.path.join(
                get_knowledge_base_dir(chat_id), os.path.basename(output_file)
            )
        if os.path.isfile(output_file):
            with open(output_file, "r", encoding="utf-8") as fh:
                output_text = fh.read()

    return (
        f"### {subtask_id}\n"
        f"**Description:** {desc}\n\n"
        f"**Output:**\n{output_text or '[no output yet]'}\n"
    )
