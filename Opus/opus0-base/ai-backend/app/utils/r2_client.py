# app/utils/r2_client.py
import boto3
from pathlib import Path
from app.core.config import settings


def upload_file_to_r2(
    local_path: Path, content_type: str, expires_in: int = 60 * 60 * 24
) -> str:
    """
    Upload local_path to Cloudflare R2 and return a presigned GET URL valid for expires_in seconds.
    """
    client = boto3.client(
        "s3",
        endpoint_url=str(settings.R2_ENDPOINT),
        region_name="auto",
        aws_access_key_id=settings.R2_ACCESS_KEY_ID,
        aws_secret_access_key=settings.R2_SECRET_ACCESS_KEY,
    )

    key = local_path.name
    # upload
    with local_path.open("rb") as f:
        client.put_object(
            Bucket=settings.R2_BUCKET_NAME,
            Key=key,
            Body=f,
            ContentType=content_type,
        )

    # presign GET
    get_url = client.generate_presigned_url(
        ClientMethod="get_object",
        Params={"Bucket": settings.R2_BUCKET_NAME, "Key": key},
        ExpiresIn=expires_in,
    )

    return get_url


def presign_get_url(key: str, expires_in: int = 60 * 60 * 24) -> str:
    """Generate a presigned GET URL for an object already in R2."""
    client = boto3.client(
        "s3",
        endpoint_url=str(settings.R2_ENDPOINT),
        region_name="auto",
        aws_access_key_id=settings.R2_ACCESS_KEY_ID,
        aws_secret_access_key=settings.R2_SECRET_ACCESS_KEY,
    )

    return client.generate_presigned_url(
        ClientMethod="get_object",
        Params={"Bucket": settings.R2_BUCKET_NAME, "Key": key},
        ExpiresIn=expires_in,
    )
