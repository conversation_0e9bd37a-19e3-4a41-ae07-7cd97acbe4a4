"""
Module: subtask_refiner.py

This module provides the function `refine_dependent_subtasks`, which checks the current task status for any pending subtasks that depend on a recently completed subtask. It uses an LLM chain to determine if the pending subtask description needs to be updated (or split) based on the new information provided by the completed subtask. If revisions are required, the function deletes the original pending subtask and adds the revised subtasks using utility functions.
"""

import json
import os
import re
import logging
import asyncio
from typing import Dict, Any, Tuple, List
from collections import OrderedDict

from langchain_openai import ChatOpenAI
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser
from app.core.config import settings
from app.utils.task_status import (
    delete_subtask,
    add_subtask,
    replace_dependency,
    get_subtask_details,
)

from app.utils.token_counter import count_tokens
from app.utils.example_rag import get_subtask_refiner_examples_text

# reuse the same analysis logger configured in BaseAgent
ANALYSIS_LOGGER = logging.getLogger("agent_analysis")


def log_input_tokens(model: str, input_tokens: int) -> None:
    """Record input token usage to ``agent_analysis.log``."""
    ANALYSIS_LOGGER.info(
        "",
        extra={
            "agent_id": "subtask_refiner",
            "model": model,
            "label": "input_tokens",
            "duration": float(input_tokens),
            "unit": "",
        },
    )


def log_output_tokens(model: str, output_tokens: int) -> None:
    """Record output token usage to ``agent_analysis.log``."""
    ANALYSIS_LOGGER.info(
        "",
        extra={
            "agent_id": "subtask_refiner",
            "model": model,
            "label": "output_tokens",
            "duration": float(output_tokens),
            "unit": "",
        },
    )

# ──────────────────────────────────────────────────────────────
#                SHARED LLM CLIENT & PROMPT SETUP
# ──────────────────────────────────────────────────────────────

# instantiate one shared ChatGoogleGenerativeAI client
LLM_CLIENT = ChatGoogleGenerativeAI(
    api_key=settings.GOOGLE_API_KEY,
    model="gemini-2.0-flash",
    temperature=0.0,
)

# build the prompt template once
PROMPT_TEMPLATE = ChatPromptTemplate.from_template(
    """
                # **Role:**
                You are part of a multi ai agent system that operates on and completes subtasks created by a task planner. You are called the **Subtask Refiner**. You will be given data and your job is to check if a pending subtask must be updated and split into multiple subtasks based on the completed subtask’s new information. It is not a compulsion to create new subtasks. Only create subtasks when you find it is necessary or will add value.


                ---

                ## **Data You Will Be Provided:**


                **All Subtasks Data:** Contains a json of all the current subtasks in the task status including all the completed and pending subtasks.
                **Completed Subtask Details:** Description of the Completed Subtask which the pending subtask is dependent on and the output generated by the complted subtask for more information that will help you create the new subtasks
                **Pending Subtask Description:** Description of the Pending Subtask that will inherit from the completed subtask and is dependent on it.
                **Dependencies Details:** Information from other dependencies of the pending subtask description for better context of the problem.

                ---


                ## **Objectives:**
                1. Decide on Edit vs. No Change:
                2. If the completed subtask’s output does not add relevant or specific info that could be used to change the subtask description, respond with no change.
                3. If it does add information that should refine or split the pending subtask, respond with new or revised subtasks.
                4. Keep dependencies logical. This is very important.
                5. Do not create a subtask that creates a final output. This is already taken care of by an aggregator agent that, in the end, takes all the information and gives the user a final output.
                6. Output strictly the specified JSON format.
                7. Given there are multiple dependencies, don't be selfish for your subtasks. There might be additional things that the other subtasks might want.


                ---
                
                **Agents Available:**

                - **You have the following agents available to complete these tasks. You don't have to specify what agent you want to use but keep this information in the back of your mind to create better tasks that can be properly handled:**
                - IMPORTANT: All models are multi model and can be given images, documents in uploads if needed.
                  - Basic LLM: This can complete normal tasks
                  - Reasoning LLM: This is a reasoning model best for complex tasks.
                  - Web Search Agent: This can search the web to find brand new information
                  - PDF Generator Agent: This agent takes research provided, plans the document like a report, literary review, research paper, article, biography, etc and then using this generated plan, writes the docuemnt and then produces the pdf. This means, we don't need to add a subtask to write the document. PDFGenerator can write the document and generate a pdf out of it. This means, just use pdf generator when creating the document. Never use for research. And give it all the research you can
                  
                ---


                ## **Output Format:**


                Your response must consist either No Change response or One or More Revised Subtasks Response


                1. A JSON object in exactly one of these forms:
                - **No Change:** 
                    ```json
                    {{
                        "subtasks": []
                    }}
                    ```
                - **One or More Revised Subtasks:** 
                    ```json
                    {{
                        "subtasks": [
                            {{
                                "id": "1",
                                "desc": "New subtask description 1",
                                "deps": ["depID1", "depID2"],
                                "uploads": []

                            }},
                            {{
                                "id": "2",
                                "desc": "New subtask description 2",
                                "deps": ["depID1"],
                                "uploads": ["upload_1.md", "upload_image_1.png"]
                            }}
                        ]
                    }}
                    ```
                Each object inside "subtasks" must have three keys:
                - `"subtaskID"` (string): The new subtask identifier.
                - `"description"` (string): The subtask description.
                - `"dependencies"` (array of strings): The list of dependency subtask IDs.
                - `"uploads"` (array of strings): Filenames of any user‐uploads (MD or image) that this subtask needs.

                No other text or fields should appear.


                ---


                ## **Examples:**

                {examples}
                
                ---
                

                **Final Instructions:**
                - No other text or explanation or Reasoning beyond the json subtask output is allowed.
                - Don't be too aggresive give subtasks only if you think it is necessary and will improve the experience a lot, or is needed.
                - If we only need to generate one pdf, don't split it into multiple pdfs. Meaning, For a pdf task that requires one pdf, never split new subtasks!!!
                - IMPORTANT!: Keep things reasonable. Only create the new smaller subtasks while keeping in mind the flow of all subtask data, what has been done and what will be done. Keep the new subtask list logical and coherent with the complete list of subtasks.
                - Also, don't generate absurd amounts of subtasks. generate normally 1-5 subtasks at max. If there is a need more more than 5 subtasks, group descriptions of multiple subtasks to fit in 5 subtasks.
                - Don't mention specific subtasks in your descriptions. If you really have to mention them, just say incorporate additional information provided.
                - When writing a web or youtube link in the subtask, use the actual case sensitive text in strings. Don't write a short version of the link otherwise it can become a problem as you are providing false information. 
                - IMPORTANT: DON'T mention subtask IDs specifically in your new subtask descriptions.
                - IMPORTANT!: Give all links in case-sensitive format.
                - IMPORTANT!: When the subtask asks for one pdf to be generated, even if you want to break down the subtask, only keep one new subtask to generate a pdf. We don't want multiple document generation when the user wants only one.


                # Now, generate the output for the given data:

                **All Subtasks Data (the complete task status file with all subtasks):** {all_subtasks_str} \n\n

                **Completed Subtask Details (desc + output):**{completed_details} \n\n
                
                **Pending Subtask Description:** {pending_desc}

                **Dependencies Details:** {dep_details} \n\n
                
                # OUTPUT (Only create subtasks if it is too much for the llm or you think it will produce significant improvement):

                """
)

# single parser instance
PARSER = StrOutputParser()


# ──────────────────────────────────────────────────────────────
#                          UTILITIES
# ──────────────────────────────────────────────────────────────
def summarize(data, max_length=100):
    s = str(data)
    return s if len(s) <= max_length else s[:max_length] + "..."


def clean_dependencies(deps: List[str], self_id: str) -> List[str]:
    """
    - Removes any occurrence of self_id
    - Deduplicates while preserving original order
    """
    unique = list(OrderedDict.fromkeys(deps))
    return [d for d in unique if d != self_id]


# ──────────────────────────────────────────────────────────────
#                   SINGLE-DEPENDENT REFINEMENT
# ──────────────────────────────────────────────────────────────
async def _refine_one_pending_dependent(
    completed_details: str,
    all_subtasks_str: str,
    pending_sub: Dict[str, Any],
    logger: logging.Logger,
    chat_id: str,
) -> Tuple[str, List[Dict[str, Any]]]:
    """
    Process a single pending dependent via LLM and return a tuple:
      (old_subtask_id, list_of_new_subtasks).
    If no revisions are required, returns an empty list for new subtasks.

    :param chat_id: Conversation identifier used to fetch dependency details.
    """
    old_id = pending_sub["id"]
    pending_desc = pending_sub.get("desc", "")

    # gather full context of all its dependencies
    dep_details = "\n\n".join(
        get_subtask_details(chat_id, dep_id) for dep_id in pending_sub.get("deps", [])
    )

    # retrieve relevant examples using RAG
    query = f"{pending_desc} {completed_details}"
    examples_text = get_subtask_refiner_examples_text(query, k=3)
    logger.info(f"Retrieved {len(examples_text.split('---')) if examples_text else 0} examples for subtask refinement")

    # render the exact prompt for token counting
    filled_prompt = PROMPT_TEMPLATE.format_prompt(
        all_subtasks_str=all_subtasks_str,
        completed_details=completed_details,
        pending_desc=pending_desc,
        dep_details=dep_details,
        examples=examples_text,
    ).to_string()
    in_tokens = count_tokens(filled_prompt)
    log_input_tokens(LLM_CLIENT.model, in_tokens)
    logger.info(f"[refiner] input_tokens={in_tokens}")

    logger.info(f"Dependent subtask {old_id} desc: {pending_desc}")
    logger.info("Initiating LLM to check if subtask description needs editing...")

    # reuse shared prompt & client
    chain = PROMPT_TEMPLATE | LLM_CLIENT | PARSER

    response_chunks: List[str] = []
    async for chunk in chain.astream(
        {
            "all_subtasks_str": all_subtasks_str,
            "completed_details": completed_details,
            "pending_desc": pending_desc,
            "dep_details": dep_details,
            "examples": examples_text,
        }
    ):
        response_chunks.append(chunk)

    final = "".join(response_chunks).strip().lower()
    logger.info(f"LLM final response for {old_id}: '{final}'")

    out_tokens = count_tokens(final)
    log_output_tokens(LLM_CLIENT.model, out_tokens)
    logger.info(f"[refiner] output_tokens={out_tokens}")

    # clean and parse JSON
    cleaned = re.sub(r"^```json\s*", "", final)
    cleaned = re.sub(r"\s*```$", "", cleaned).strip()
    idx = cleaned.rfind("}")
    if idx != -1:
        cleaned = cleaned[: idx + 1]

    try:
        revision = json.loads(cleaned)
    except Exception as e:
        logger.error(f"Failed to parse LLM output for {old_id}: {e}")
        return old_id, []

    new_subs = revision.get("subtasks", [])
    return old_id, new_subs


# ──────────────────────────────────────────────────────────────
#                MAIN PARALLEL-THEN-SEQUENTIAL REFINER
# ──────────────────────────────────────────────────────────────
async def refine_dependent_subtasks(
    chat_id: str,
    task_status: Dict[str, Any],
    completed_subtask_id: str,
    logger: logging.Logger,
) -> None:
    """
    Parallelize LLM checks for all dependents, then apply deletions/additions
    sequentially.

    :param chat_id: Conversation identifier for locating task data.
    :param task_status: Current task status dictionary.
    :param completed_subtask_id: ID of the subtask that was just completed.
    :param logger: Logger instance for debug output.
    """
    logger.info(
        f"Starting search of dependent subtasks for subtask {completed_subtask_id} before completion."
    )

    subtasks = task_status.get("subtasks", {}).get("subtasks", [])
    all_subtasks_str = json.dumps(task_status, indent=2)

    # get completed details
    completed = next((st for st in subtasks if st["id"] == completed_subtask_id), None)
    if not completed:
        logger.error(
            f"Completed subtask {completed_subtask_id} not found in task status."
        )
        return

    completed_details = get_subtask_details(chat_id, completed_subtask_id)
    logger.info(f"Completed subtask context:\n{completed_details}")

    # 1) discover all pending dependents
    dependents = [
        st
        for st in subtasks
        if st.get("status") in ("pending", "in_progress")
        and completed_subtask_id in st.get("deps", [])
    ]
    if not dependents:
        logger.info("No pending dependents found; skipping refiner.")
        return

    # 2) run LLM checks in parallel, but throttle concurrency
    semaphore = asyncio.Semaphore(5)

    async def limited(sub):
        async with semaphore:
            return await _refine_one_pending_dependent(
                completed_details,
                all_subtasks_str,
                sub,
                logger,
                chat_id,
            )

    tasks = [limited(st) for st in dependents]
    results = await asyncio.gather(*tasks)

    # 3) collect all revisions
    replacement_mapping: Dict[str, List[Dict[str, Any]]] = {
        old: new_list for old, new_list in results if new_list
    }
    if not replacement_mapping:
        logger.info(
            f"LLM indicates no revisions needed for any dependents of {completed_subtask_id}."
        )
        return

    # 4) sequentially apply deletes, adds, and dep replacements
    for old_id, new_subs in replacement_mapping.items():
        logger.info(f"Applying {len(new_subs)} revisions for dependent {old_id}")
        delete_subtask(chat_id, old_id)
        for new in new_subs:
            add_subtask(
                chat_id,
                location_id=old_id,
                new_subtask_id=new.get("id", old_id),
                desc=new.get("desc", ""),
                deps=clean_dependencies(new.get("deps", []), new.get("id", old_id)),
                uploads=new.get("uploads", []),
                status="pending",
            )
            logger.info(f"Added revised subtask {new.get('id')} for {old_id}")
        replace_dependency(chat_id, old_id, [s.get("id") for s in new_subs])
        logger.info(
            f"Replaced dependency '{old_id}' with {[s.get('id') for s in new_subs]} in all subtasks."
        )

    # 5) final sweep log
    logger.info(f"Replacement mapping: {json.dumps(replacement_mapping, indent=2)}")
    for st in subtasks:
        if st.get("status") in ("pending", "in_progress"):
            for old_id in replacement_mapping:
                if old_id in st.get("deps", []):
                    logger.info(
                        f"Pending subtask {st['id']} still lists original dependency '{old_id}'."
                    )


# ──────────────────────────────────────────────────────────────
#             OPTIONAL DEPENDENCY-USAGE LOGGER (unchanged)
# ──────────────────────────────────────────────────────────────
def log_subtask_dependency_usage(
    task_status: Dict[str, Any], target_subtask_id: str, logger: logging.Logger
):
    """
    Logs any subtasks that currently depend on the given target_subtask_id.
    """
    subtasks = task_status.get("subtasks", {}).get("subtasks", [])
    logger.info(f"Checking dependency usage for subtask '{target_subtask_id}'...")
    for st in subtasks:
        if target_subtask_id in st.get("deps", []):
            logger.info(f"Subtask '{st['id']}' depends on '{target_subtask_id}'")
