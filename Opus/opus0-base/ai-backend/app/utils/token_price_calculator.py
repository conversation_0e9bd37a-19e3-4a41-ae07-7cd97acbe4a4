import re
from collections import defaultdict
from typing import Dict

def tally_tokens(log_text: str) -> Dict[str, Dict[str, float]]:
    """
    Parse a log string and tally input/output tokens per model.

    Args:
        log_text: Multiline string containing log lines.

    Returns:
        A dict mapping model names to a dict with keys 'input_tokens' and 'output_tokens'.
    """
    pattern = re.compile(r'(?P<model>models?/\S+)\s+(?P<kind>input_tokens|output_tokens):\s*(?P<value>\d+\.\d+)')
    totals = defaultdict(lambda: {'input_tokens': 0.0, 'output_tokens': 0.0})

    for line in log_text.splitlines():
        m = pattern.search(line)
        if not m:
            continue
        model = m.group('model')
        kind = m.group('kind')
        value = float(m.group('value'))
        totals[model][kind] += value

    return totals

# Cost per 1M tokens (USD)
COSTS_PER_MILLION = {
    'gemini-2.5-flash': {'input': 0.15, 'output': 0.60},
    'gemini-2.0-flash': {'input': 0.10, 'output': 0.40},
    'gemini-2.5-pro':   {'input': 1.25, 'output': 10.00},
}

def calculate_costs(totals: Dict[str, Dict[str, float]]) -> Dict[str, Dict[str, float]]:
    """
    Given token totals per model, compute costs.

    Args:
        totals: Mapping model -> {'input_tokens': x, 'output_tokens': y}.

    Returns:
        Mapping model -> {'input_cost': a, 'output_cost': b, 'total_cost': c}.
    """
    costs = {}
    overall = 0.0

    for model, counts in totals.items():
        # determine which pricing tier applies by matching model name
        for key, price in COSTS_PER_MILLION.items():
            if key in model:
                input_price = price['input']
                output_price = price['output']
                break
        else:
            # unknown model: skip cost calculation
            input_price = output_price = 0.0

        input_tokens = counts['input_tokens']
        output_tokens = counts['output_tokens']

        input_cost = input_tokens / 1_000_000 * input_price
        output_cost = output_tokens / 1_000_000 * output_price
        total_cost = input_cost + output_cost

        costs[model] = {
            'input_cost': input_cost,
            'output_cost': output_cost,
            'total_cost': total_cost
        }
        overall += total_cost

    costs['__overall_total__'] = {'total_cost': overall}
    return costs

if __name__ == '__main__':
    sample_logs = """
    
    [2025-06-18 05:11:58,589] task_planner models/gemini-2.5-flash-preview-04-17 input_tokens: 6374.0000
[2025-06-18 05:12:04,059] task_planner models/gemini-2.5-flash-preview-04-17 output_tokens: 136.0000
[2025-06-18 05:12:05,078] factory models/gemini-2.5-flash-preview-04-17 input_tokens: 1765.0000
[2025-06-18 05:12:07,731] factory models/gemini-2.5-flash-preview-04-17 output_tokens: 2.0000
[2025-06-18 05:12:07,747] llm_worker_agent_1 models/gemini-2.5-flash-preview-04-17 input_tokens: 150.0000
[2025-06-18 05:12:12,215] llm_worker_agent_1 models/gemini-2.5-flash-preview-04-17 output_tokens: 21.0000
[2025-06-18 05:12:13,605] llm_worker_agent_1  llm agent: 5.8656s
[2025-06-18 05:12:14,140] subtask_refiner models/gemini-2.0-flash input_tokens: 6252.0000
[2025-06-18 05:12:17,378] subtask_refiner models/gemini-2.0-flash output_tokens: 147.0000
[2025-06-18 05:12:17,952] factory models/gemini-2.5-flash-preview-04-17 input_tokens: 1968.0000
[2025-06-18 05:12:20,022] factory models/gemini-2.5-flash-preview-04-17 output_tokens: 2.0000
[2025-06-18 05:12:20,030] llm_worker_agent_2.2 models/gemini-2.5-flash-preview-04-17 input_tokens: 284.0000
[2025-06-18 05:12:20,229] factory models/gemini-2.5-flash-preview-04-17 input_tokens: 1968.0000
[2025-06-18 05:12:22,305] factory models/gemini-2.5-flash-preview-04-17 output_tokens: 2.0000
[2025-06-18 05:12:22,311] llm_worker_agent_2 models/gemini-2.5-flash-preview-04-17 input_tokens: 284.0000
[2025-06-18 05:12:22,516] factory models/gemini-2.5-flash-preview-04-17 input_tokens: 1968.0000
[2025-06-18 05:12:22,752] llm_worker_agent_2.2 models/gemini-2.5-flash-preview-04-17 output_tokens: 282.0000
[2025-06-18 05:12:24,616] factory models/gemini-2.5-flash-preview-04-17 output_tokens: 2.0000
[2025-06-18 05:12:24,623] llm_worker_agent_2.1 models/gemini-2.5-flash-preview-04-17 input_tokens: 284.0000
[2025-06-18 05:12:24,735] llm_worker_agent_2 models/gemini-2.5-flash-preview-04-17 output_tokens: 201.0000
[2025-06-18 05:12:25,188] llm_worker_agent_2.2  llm agent: 5.1625s
[2025-06-18 05:12:26,894] llm_worker_agent_2.1 models/gemini-2.5-flash-preview-04-17 output_tokens: 208.0000
[2025-06-18 05:12:27,327] llm_worker_agent_2  llm agent: 5.0189s
[2025-06-18 05:12:28,897] llm_worker_agent_2.1  llm agent: 4.2772s
[2025-06-18 05:12:30,668] aggregator_agent models/gemini-2.5-flash-preview-04-17 input_tokens: 1439.0000
[2025-06-18 05:12:37,180] aggregator_agent models/gemini-2.5-flash-preview-04-17 output_tokens: 753.0000


"""
    # Tally tokens
    totals = tally_tokens(sample_logs)

    # Print token counts per model
    print("Token usage per model:")
    for model, counts in totals.items():
        print(f"  {model}: input_tokens = {counts['input_tokens']}, output_tokens = {counts['output_tokens']}")

    # Calculate and print costs
    costs = calculate_costs(totals)
    print("\nCost breakdown per model (USD):")
    for model, c in costs.items():
        if model == '__overall_total__':
            continue
        print(
            f"  {model}: input_cost = ${c['input_cost']:.6f}, "
            f"output_cost = ${c['output_cost']:.6f}, "
            f"total_cost = ${c['total_cost']:.6f}"
        )

    overall = costs['__overall_total__']['total_cost']
    print(f"\nOverall total cost: ${overall:.6f}")
