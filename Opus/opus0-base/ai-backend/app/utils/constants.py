# ai-backend/app/utils/constants.py
from pathlib import Path
import os

# Document Path
BASE_DIR = Path(__file__).resolve().parent.parent.parent  # → ai-backend
# Directory for runtime data (logs, session files)
DATA_DIR = Path(os.getenv("OPUS_DATA_DIR", BASE_DIR / "data"))
DATA_DIR.mkdir(parents=True, exist_ok=True)

PUBLIC_PDF_PATH = BASE_DIR / "public_pdfs"

# Root folder that will hold all per-chat session data
SESSIONS_ROOT = DATA_DIR / "sessions"

# File Paths
AGENT_COMMUNICATION_DIR = "./app/agent_communication/"
KNOWLEDGE_BASE_DIR = "./app/knowledge_base/"
TASK_STATUS_FILE = "./app/knowledge_base/task_status.json"

# Sleep Intervals
SLEEP_INTERVAL = 1  # Time in seconds for agents to poll for new messages
FAST_INTERVAL  = 0.2      # immediate follow-up after work

# Task Statuses
STATUS_INITIALIZED = "initialized"
STATUS_PENDING = "pending"
STATUS_IN_PROGRESS = "in_progress"
STATUS_COMPLETED = "completed"

# Message Types
MESSAGE_TYPE_TASK_ASSIGNMENT = "task_assignment"
MESSAGE_TYPE_TASK_COMPLETION = "task_completion"
MESSAGE_TYPE_ERROR = "error"
MESSAGE_TYPE_FEEDBACK = "feedback"

# Agent Types
AGENT_TYPE_MANAGER = "manager"
AGENT_TYPE_WORKER = "worker"
AGENT_TYPE_AGGREGATOR = "aggregator"

# Message Filename Template
MESSAGE_FILE_TEMPLATE = "msg_<sender>_<receiver>_<taskID>_<subtaskID>.json"


def get_session_dir(chat_id: str) -> Path:
    """Return the base directory for a specific chat session."""
    return SESSIONS_ROOT / chat_id


def get_knowledge_base_dir(chat_id: str) -> Path:
    """Return the knowledge_base folder for ``chat_id``."""
    return get_session_dir(chat_id) / "knowledge_base"


def get_agent_comm_dir(chat_id: str) -> Path:
    """Return the agent_communication folder for ``chat_id``."""
    return get_session_dir(chat_id) / "agent_communication"


def get_public_pdf_dir(chat_id: str) -> Path:
    """Return the public_pdfs folder for ``chat_id``."""
    return get_session_dir(chat_id) / "public_pdfs"


def get_public_docx_dir(chat_id: str) -> Path:
    """Return the public_docx folder for ``chat_id``."""
    return get_session_dir(chat_id) / "docs"


def get_public_slides_dir(chat_id: str) -> Path:
    """Return the public_slides folder for ``chat_id``."""
    return get_session_dir(chat_id) / "docs"


def get_public_xlsx_dir(chat_id: str) -> Path:
    """Return the public_xlsx folder for ``chat_id``."""
    return get_session_dir(chat_id) / "docs"


def get_user_uploads_dir(chat_id: str) -> Path:
    """Return the user_uploads folder for ``chat_id``."""
    return get_session_dir(chat_id) / "user_uploads"
