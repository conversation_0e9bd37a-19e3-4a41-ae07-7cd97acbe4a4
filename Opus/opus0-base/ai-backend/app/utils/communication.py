# app/utils/communication.py

import os
import json
from typing import Dict, Optional
from app.utils.constants import get_agent_comm_dir


def generate_message_filename(sender: str, receiver: str, subtask_id: str) -> str:
    """
    Generates a standardized filename for message JSON files.
    Filename format: sender_receiver_subtaskid.json
    """
    return f"{sender}_{receiver}_{subtask_id}.json"


def send_message(chat_id: str, sender: str, receiver: str, message: Dict) -> None:
    """
    Sends a message between agents by saving it as a JSON file.
    Handles messages from manager to workers and from workers to the manager.

    Message format:
    - From Manager to Worker:
      {
        "subtask_id": str,
        "subtask_description": str,
        "user_message": str
      }
    - From Worker to Manager:
      {
        "subtask_id": str,
        "output_file": str
      }

    :param sender: The ID of the sending agent (e.g., "manager").
    :param receiver: The ID of the receiving agent (e.g., "llm_worker_agent").
    :param message: The message dictionary.
    """
    try:

        # Validate message structure
        if sender == "manager":
            required_keys = {"subtask_id", "subtask_description", "user_message"}
        else:
            required_keys = {"subtask_id", "output_file"}


        if not required_keys.issubset(message.keys()):
            raise ValueError(f"Message from {sender} to {receiver} is missing required keys: {required_keys}")

        # Generate filename and save message as JSON
        filename = generate_message_filename(
            sender=sender, receiver=receiver, subtask_id=message["subtask_id"]
        )
        comm_dir = get_agent_comm_dir(chat_id)
        os.makedirs(comm_dir, exist_ok=True)
        file_path = os.path.join(comm_dir, filename)

        with open(file_path, "w") as file:
            json.dump(message, file, indent=4)
            
    except Exception as e:
        print(f"Error in send_message: {e}")
        raise


def receive_message(chat_id: str, agent_id: str) -> Optional[Dict]:
    """
    Receives a message addressed to the specified agent.

    Message format:
    - For Manager: Messages from workers.
      {
        "subtask_id": str,
        "output_file": str
      }
    - For Workers: Messages from the manager.
      {
        "subtask_id": str,
        "subtask_description": str,
        "user_message": str
      }

    :param agent_id: The ID of the receiving agent (e.g., "manager" or a specific worker ID).
    :return: Parsed message dictionary or None if no message is found.
    """
    # List all message files for the agent
    comm_dir = get_agent_comm_dir(chat_id)
    if not os.path.isdir(comm_dir):
        return None
    files = [f for f in os.listdir(comm_dir) if f"_{agent_id}_" in f]
    if not files:
        return None

    # Process the first file in the queue
    file_path = os.path.join(comm_dir, files[0])
    with open(file_path, "r") as file:
        message = json.load(file)

    # Validate message based on receiver type
    if "manager" in agent_id:
        required_keys = {"subtask_id", "output_file"}
    elif "worker" in agent_id:
        required_keys = {"subtask_id", "subtask_description", "user_message"}
    else:
        raise ValueError(f"Invalid receiver type: {agent_id}")

    if not required_keys.issubset(message.keys()):
        raise ValueError(f"Message for {agent_id} is missing required keys: {required_keys}")

    # Remove the file after reading
    os.remove(file_path)
    print(f"Message received and removed: {files[0]}")
    return message


def parse_message(file_path: str) -> Dict:
    """
    Parses a JSON message file into a Python dictionary.

    :param file_path: Path to the message file.
    :return: Parsed message dictionary.
    """
    with open(file_path, "r") as file:
        return json.load(file)
