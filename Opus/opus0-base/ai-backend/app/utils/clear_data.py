import os
import logging

def clear_directory(directory_path: str):
    """
    Removes all files and sub-directories in the specified directory.
    Ensures the directory itself exists after clearing.
    """
    if not os.path.exists(directory_path):
        os.makedirs(directory_path, exist_ok=True)
    else:
        for filename in os.listdir(directory_path):
            file_path = os.path.join(directory_path, filename)
            if os.path.isfile(file_path):
                os.remove(file_path)
            elif os.path.isdir(file_path):
                # If for some reason you have subdirectories, either remove them recursively
                # or handle them as needed. For simplicity, let's assume only files.
                pass
    logging.info(f"Cleared directory: {directory_path}")
