# app/utils/document_reader.py

import httpx
import base64
import fitz                      # PyMuPDF
from docx import Document as DocxDocument
from pptx import Presentation
import pandas as pd
from pathlib import Path
from datetime import datetime
from urllib.parse import urlparse
from app.core.config import settings
from app.utils.constants import get_user_uploads_dir
from langchain_core.messages import HumanMessage
from langchain_google_genai import ChatGoogleGenerativeAI

def ensure_upload_dir(chat_id: str) -> Path:
    """Make sure the user_uploads folder for this chat exists."""
    UP = get_user_uploads_dir(chat_id)
    UP.mkdir(parents=True, exist_ok=True)
    return UP

def download_file(url: str, dest_folder: Path) -> Path:
    """
    Fetches the URL (presigned GET) and writes it to dest_folder.
    Returns the local Path.
    """
    r = httpx.get(url)
    r.raise_for_status()
    name = Path(urlparse(url).path).name
    local_path = dest_folder / name
    local_path.write_bytes(r.content)
    return local_path

def parse_pdf(local: Path) -> str:
    doc = fitz.open(str(local))
    pages = []
    for page in doc:
        pages.append(page.get_text("text") or "")
    return "\n\n".join(pages)

def parse_docx(local: Path) -> str:
    doc = DocxDocument(str(local))
    paras = [p.text for p in doc.paragraphs if p.text.strip()]
    return "\n\n".join(paras)

def parse_pptx(local: Path) -> str:
    prs = Presentation(str(local))
    slides = []
    for slide in prs.slides:
        texts = []
        for shape in slide.shapes:
            if hasattr(shape, "text") and shape.text.strip():
                texts.append(shape.text.strip())
        slides.append("\n\n".join(texts))
    return "\n\n---\n\n".join(slides)

def parse_csv(local: Path) -> str:
    df = pd.read_csv(local)
    return df.to_markdown(index=False)

async def describe_image_to_markdown(image_url: str, seq: int, chat_id: str) -> Path:
    """
    Download the image, ask Gemini-Flash to describe it, and save as upload_image_{seq}.md
    """
    UP = ensure_upload_dir(chat_id)
    # 1) fetch bytes (with a 10s timeout)
    try:
        resp = httpx.get(image_url, timeout=10.0)
        resp.raise_for_status()
        data_uri = "data:image/jpeg;base64," + base64.b64encode(resp.content).decode()
    except Exception as e:
        # on error, write a stub md and return
        header = f"# Description of upload_image_{seq}.jpg\n\n"
        stub   = f"**Error fetching image:** {e}"
        out_path = (ensure_upload_dir(chat_id) / f"upload_image_{seq}.md")
        out_path.write_text(header + stub, encoding="utf-8")
        return out_path

    # 2) ask Gemini
    try:
        model = ChatGoogleGenerativeAI(model="gemini-1.5-flash")
        msg = HumanMessage(
            content=[
                {"type":"text",       "text":"Please describe this image in detail."},
                {"type":"image_url",  "image_url":{"url": data_uri}}
            ]
        )
        reply = model.invoke([msg]).content.strip()
    except Exception as e:
        # on LLM error, stub out
        header = f"# Description of upload_image_{seq}.jpg\n\n"
        stub   = f"**Error describing image:** {e}"
        out_path = (ensure_upload_dir(chat_id) / f"upload_image_{seq}.md")
        out_path.write_text(header + stub, encoding="utf-8")
        return out_path

    # 3) write out markdown
    out_path = ensure_upload_dir(chat_id) / f"upload_image_{seq}.md"
    header   = f"# Description of upload_image_{seq}.jpg\n\n"
    out_path.write_text(header + reply, encoding="utf-8")
    return out_path

async def ocr_document_to_markdown(file_url: str, chat_id: str) -> Path:
    """
    Downloads the document at file_url into user_uploads/, parses it to Markdown,
    writes out a .md sibling, and returns its Path.
    """
    UP = ensure_upload_dir(chat_id)
    # 1) download
    local = download_file(file_url, UP)

    # 2) pick parser
    ext = local.suffix.lower()
    if ext == ".pdf":
        text = parse_pdf(local)
    elif ext in (".docx",):
        text = parse_docx(local)
    elif ext in (".pptx",):
        text = parse_pptx(local)
    elif ext == ".csv":
        text = parse_csv(local)
    else:
        # fallback: just read raw bytes as text
        text = local.read_text(errors="ignore")

    # 3) build a sequential filename upload_1.md, upload_2.md, …
    existing = list(UP.glob("upload_*.md"))
    next_i   = len(existing) + 1
    out_path = UP / f"upload_{next_i}.md"
    header = f"# Extracted from {local.name}\n\n"
    out_path.write_text(header + text, encoding="utf-8")
    return out_path
