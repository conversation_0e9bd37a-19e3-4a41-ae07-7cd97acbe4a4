# utils/logging_config.py
import logging
import logging.config
import os
from pathlib import Path
from rich.logging import RichHandler

DATA_DIR = Path(os.getenv("OPUS_DATA_DIR", Path(__file__).resolve().parent.parent.parent / "data"))
DATA_DIR.mkdir(parents=True, exist_ok=True)
LOG_FILE = DATA_DIR / "app.log"

def init_logging():
    config = {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "rich": {
                "format": "[%(levelname)s] %(message)s",  # Minimal format: only level and message
            },
            "minimal": {
                "format": "[%(levelname)s] %(message)s",
            },
        },
        "handlers": {
            "console": {
                "class": "rich.logging.RichHandler",
                "formatter": "rich",
                "level": "DEBUG",
                "rich_tracebacks": True,
                "tracebacks_show_locals": True,
            },
            "file": {
                "class": "logging.FileHandler",
                "formatter": "minimal",
                "filename": str(LOG_FILE),
                "level": "DEBUG",
            },
        },
        "root": {
            "handlers": ["console", "file"],
            "level": "INFO",
        },
        "loggers": {
            "app": {
                "handlers": ["console", "file"],
                "level": "DEBUG",
                "propagate": False,
            },
            "pymongo": {
                "handlers": ["console", "file"],
                "level": "WARNING",
                "propagate": False,
            },
            "uvicorn": {
                "handlers": ["console", "file"],
                "level": "INFO",
                "propagate": False,
            },
            "socketio": {
                "handlers": ["console", "file"],
                "level": "INFO",
                "propagate": False,
            },
        },
    }
    logging.config.dictConfig(config)
