# app/utils/xlsx_chart_handler.py
"""
Module: xlsx_chart_handler.py

This module handles chart creation and configuration for Excel (.xlsx) documents.
It provides utilities for creating, configuring, and positioning charts in Excel worksheets.
"""

import logging
from typing import Dict, Any, List
import openpyxl
from openpyxl.chart import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Reference
from openpyxl.utils import range_boundaries


def validate_chart_data(chart_data: Dict[str, Any], worksheet_data: Dict[str, Any], logger: logging.Logger) -> bool:
    """
    Validate chart specifications against available data.
    """
    try:
        # Check required fields
        if not chart_data.get("type"):
            logger.warning("[XLSXGenerator] Chart missing type specification")
            return False

        # Validate chart type
        valid_types = ["bar", "line", "pie"]
        if chart_data.get("type") not in valid_types:
            logger.warning(f"[XLSXGenerator] Invalid chart type: {chart_data.get('type')}")
            return False

        # Check data range specifications - handle both string and object formats
        data_range = chart_data.get("data_range")
        if not data_range:
            logger.warning("[XLSXGenerator] Chart missing data_range")
            return False

        # Handle different data_range formats
        if isinstance(data_range, str):
            # String format like "A1:E4" - valid for bar/line charts
            if len(data_range) < 3:
                logger.warning(f"[XLSXGenerator] Invalid data_range string: {data_range}")
                return False
        elif isinstance(data_range, dict):
            # Object format with categories/values - required for pie charts
            if chart_data.get("type") == "pie":
                if not data_range.get("categories") or not data_range.get("values"):
                    logger.warning("[XLSXGenerator] Pie chart missing categories or values in data_range")
                    return False
            else:
                # For bar/line charts, either format is acceptable
                pass
        else:
            logger.warning(f"[XLSXGenerator] Invalid data_range format: {type(data_range)}")
            return False

        # Validate position format (basic check)
        position = chart_data.get("position", "D2")
        if not isinstance(position, str) or len(position) < 2:
            logger.warning(f"[XLSXGenerator] Invalid chart position: {position}")
            return False

        return True

    except Exception as e:
        logger.warning(f"[XLSXGenerator] Chart validation error: {e}")
        return False


def resolve_chart_position_conflicts(charts: List[Dict[str, Any]], logger: logging.Logger) -> List[Dict[str, Any]]:
    """
    Resolve chart position conflicts by adjusting positions when charts overlap.
    """
    if len(charts) <= 1:
        return charts

    used_positions = set()
    resolved_charts = []

    # Default positions to use when conflicts arise - prioritize visible areas
    default_positions = ["D2", "D18", "G2", "G18", "D34", "G34", "J2", "J18"]
    position_index = 0

    for chart in charts:
        original_position = chart.get("position", "D2")

        # If position is already used, find a new one
        if original_position in used_positions:
            # Find next available position
            while position_index < len(default_positions) and default_positions[position_index] in used_positions:
                position_index += 1

            if position_index < len(default_positions):
                new_position = default_positions[position_index]
                chart = chart.copy()  # Don't modify original
                chart["position"] = new_position
                logger.info(f"[XLSXGenerator] Moved chart '{chart.get('title', 'Untitled')}' from {original_position} to {new_position} to avoid conflict")
                position_index += 1
            else:
                # Fallback: offset from original position
                new_position = f"D{int(original_position[1:]) + 16}"
                chart = chart.copy()
                chart["position"] = new_position
                logger.info(f"[XLSXGenerator] Moved chart '{chart.get('title', 'Untitled')}' to {new_position} (fallback positioning)")

        used_positions.add(chart.get("position", "D2"))
        resolved_charts.append(chart)

    return resolved_charts


def add_chart_to_worksheet(ws: openpyxl.worksheet.worksheet.Worksheet, chart_data: Dict[str, Any], logger: logging.Logger):
    """
    Create and add a chart to the worksheet.
    """
    try:
        chart_type = chart_data.get("type", "bar")
        chart = create_chart_object(chart_type, chart_data, logger)

        # Set data references
        configure_chart_data(chart, chart_data, ws, logger)

        # Apply styling and options
        apply_chart_styling(chart, chart_data)

        # Position chart
        position = chart_data.get("position", "D2")
        ws.add_chart(chart, position)

        chart_title = chart_data.get('title', 'Untitled')
        logger.info(f"[XLSXGenerator] Successfully added {chart_type.upper()} chart '{chart_title}' at position {position}")

        # Special logging for line charts to help with visibility
        if chart_type == "line":
            logger.info(f"[XLSXGenerator] 📈 LINE CHART CREATED: '{chart_title}' - Check column {position[0]} in your Excel file!")

    except Exception as e:
        logger.warning(f"[XLSXGenerator] Failed to create chart: {e}")


def create_chart_object(chart_type: str, chart_data: Dict[str, Any], logger: logging.Logger):
    """
    Factory method for creating chart objects based on type.
    """
    options = chart_data.get("options", {})

    if chart_type == "bar":
        chart = BarChart()
        chart.type = "col"  # Default to column chart
        chart.grouping = options.get("grouping", "clustered")

        # Handle horizontal bar charts
        if options.get("orientation") == "horizontal":
            chart.type = "bar"

    elif chart_type == "line":
        chart = LineChart()
        chart.grouping = options.get("grouping", "standard")

    elif chart_type == "pie":
        chart = PieChart()

    else:
        # Default to bar chart for unknown types
        logger.warning(f"[XLSXGenerator] Unknown chart type '{chart_type}', defaulting to bar chart")
        chart = BarChart()
        chart.type = "col"
        chart.grouping = "clustered"

    return chart


def configure_chart_data(chart, chart_data: Dict[str, Any], ws: openpyxl.worksheet.worksheet.Worksheet, logger: logging.Logger):
    """
    Configure chart data references from the worksheet.
    """
    chart_type = chart_data.get("type", "bar")

    # Handle pie charts differently - they need special data arrangement
    if chart_type == "pie":
        configure_pie_chart_data(chart, chart_data, ws, logger)
    else:
        configure_standard_chart_data(chart, chart_data, ws, logger)


def configure_pie_chart_data(chart, chart_data: Dict[str, Any], ws: openpyxl.worksheet.worksheet.Worksheet, logger: logging.Logger):
    """
    Configure pie chart data. Pie charts work best with data arranged in columns.
    """
    data_range = chart_data.get("data_range", {})

    categories_range = data_range.get("categories")
    values_range = data_range.get("values")

    if not categories_range or not values_range:
        logger.warning(f"[XLSXGenerator] Pie chart missing categories or values range")
        return

    try:
        # Get the target worksheet and ranges
        cat_ws, cat_min_col, cat_min_row, cat_max_col, cat_max_row = parse_worksheet_range(categories_range, ws, logger)
        val_ws, val_min_col, val_min_row, val_max_col, val_max_row = parse_worksheet_range(values_range, ws, logger)

        # Create references for categories and values
        categories = Reference(cat_ws, min_col=cat_min_col, min_row=cat_min_row, max_col=cat_max_col, max_row=cat_max_row)
        values = Reference(val_ws, min_col=val_min_col, min_row=val_min_row, max_col=val_max_col, max_row=val_max_row)

        # For pie charts, add data first, then set categories
        chart.add_data(values, titles_from_data=False)
        chart.set_categories(categories)

        # Debug: Check what data is actually in the ranges
        try:
            cat_data = []
            val_data = []
            for row in range(cat_min_row, cat_max_row + 1):
                for col in range(cat_min_col, cat_max_col + 1):
                    cat_cell = cat_ws.cell(row=row, column=col)
                    cat_data.append(f"{cat_cell.coordinate}:{cat_cell.value}")
            for row in range(val_min_row, val_max_row + 1):
                for col in range(val_min_col, val_max_col + 1):
                    val_cell = val_ws.cell(row=row, column=col)
                    val_data.append(f"{val_cell.coordinate}:{val_cell.value}")
            logger.info(f"[XLSXGenerator] Pie chart data - Categories: {cat_data}, Values: {val_data}")
        except Exception as debug_e:
            logger.warning(f"[XLSXGenerator] Could not debug pie chart data: {debug_e}")

        logger.info(f"[XLSXGenerator] Configured pie chart - categories: {categories_range} ({cat_min_col},{cat_min_row}:{cat_max_col},{cat_max_row}), values: {values_range} ({val_min_col},{val_min_row}:{val_max_col},{val_max_row})")

    except Exception as e:
        logger.warning(f"[XLSXGenerator] Error configuring pie chart data: {e}")


def configure_standard_chart_data(chart, chart_data: Dict[str, Any], ws: openpyxl.worksheet.worksheet.Worksheet, logger: logging.Logger):
    """
    Configure standard chart data (bar, line, etc.).
    """
    data_range = chart_data.get("data_range")

    # Handle different data_range formats
    if isinstance(data_range, str):
        # String format like "A1:E4" - use entire range as data
        try:
            target_ws, min_col, min_row, max_col, max_row = parse_worksheet_range(data_range, ws, logger)
            data_ref = Reference(target_ws, min_col=min_col, min_row=min_row, max_col=max_col, max_row=max_row)
            chart.add_data(data_ref, titles_from_data=True)
            logger.info(f"[XLSXGenerator] Configured standard chart with range: {data_range}")
        except Exception as e:
            logger.warning(f"[XLSXGenerator] Invalid data range '{data_range}': {e}")

    elif isinstance(data_range, dict):
        # Object format with separate categories and values
        # Get categories (x-axis labels)
        categories_range = data_range.get("categories")
        if categories_range:
            try:
                # Get the target worksheet and range
                target_ws, min_col, min_row, max_col, max_row = parse_worksheet_range(categories_range, ws, logger)
                categories = Reference(target_ws, min_col=min_col, min_row=min_row, max_col=max_col, max_row=max_row)
                chart.set_categories(categories)
            except Exception as e:
                logger.warning(f"[XLSXGenerator] Invalid categories range '{categories_range}': {e}")

        # Get values (data series)
        values_range = data_range.get("values")
        if values_range:
            try:
                # Get the target worksheet and range
                target_ws, min_col, min_row, max_col, max_row = parse_worksheet_range(values_range, ws, logger)
                values = Reference(target_ws, min_col=min_col, min_row=min_row, max_col=max_col, max_row=max_row)
                chart.add_data(values, titles_from_data=False)
            except Exception as e:
                logger.warning(f"[XLSXGenerator] Invalid values range '{values_range}': {e}")

        logger.info(f"[XLSXGenerator] Configured standard chart with categories: {categories_range}, values: {values_range}")

    else:
        logger.warning(f"[XLSXGenerator] Unsupported data_range format: {type(data_range)}")


def parse_worksheet_range(range_string: str, current_ws: openpyxl.worksheet.worksheet.Worksheet, logger: logging.Logger):
    """
    Parse a range string like 'A2:B5' or 'Sheet1'!A2:B5' and return the target worksheet and range.
    Returns (worksheet, min_col, min_row, max_col, max_row)
    """
    target_ws = current_ws  # Default to current worksheet

    # Handle worksheet-qualified ranges like 'Sales Data'!A2:A4
    if '!' in range_string:
        # Split on the last '!' to handle sheet names with exclamation marks
        parts = range_string.rsplit('!', 1)
        if len(parts) == 2:
            sheet_name = parts[0].strip("'\"")  # Remove quotes from sheet name
            range_string = parts[1]  # Use only the cell range part

            # Find the target worksheet in the workbook
            workbook = current_ws.parent
            if sheet_name in workbook.sheetnames:
                target_ws = workbook[sheet_name]
            else:
                logger.warning(f"[XLSXGenerator] Worksheet '{sheet_name}' not found, using current worksheet")

    # Remove any quotes around the range
    range_string = range_string.strip("'\"")

    # Parse the cell range
    min_col, min_row, max_col, max_row = range_boundaries(range_string)

    return target_ws, min_col, min_row, max_col, max_row


def apply_chart_styling(chart, chart_data: Dict[str, Any]):
    """
    Apply styling and options to the chart.
    """
    # Set chart title
    title = chart_data.get("title", "Chart")
    chart.title = title

    options = chart_data.get("options", {})

    # Set chart style
    style = chart_data.get("style", 10)
    chart.style = style

    # Configure legend
    if not options.get("show_legend", True):
        chart.legend = None

    # Set axis titles
    x_axis_title = options.get("x_axis_title")
    if x_axis_title and hasattr(chart, 'x_axis'):
        chart.x_axis.title = x_axis_title

    y_axis_title = options.get("y_axis_title")
    if y_axis_title and hasattr(chart, 'y_axis'):
        chart.y_axis.title = y_axis_title

    # Set chart dimensions
    width = options.get("width", 15)
    height = options.get("height", 10)
    chart.width = width
    chart.height = height
