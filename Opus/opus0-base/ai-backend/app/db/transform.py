# app/db/transform.py
"""
Module: transform.py

Utility helpers to convert MongoDB chat documents to the
camelCase structures expected by the frontend and vice versa.
"""

from __future__ import annotations

from typing import Any, Dict, List


# ──────────────────────────────────────────────────
#                         MESSAGES
# ──────────────────────────────────────────────────


def message_doc_to_model(doc: Dict[str, Any]) -> Dict[str, Any]:
    """Convert a MongoDB message document to camelCase format."""
    runtime_logs = [
        {
            "subtaskId": rl.get("subtask_id"),
            "description": rl.get("description"),
            "status": rl.get("status"),
            "tsStart": rl.get("ts_start"),
            "tsEnd": rl.get("ts_end"),
        }
        for rl in doc.get("runtime_logs", [])
    ]

    return {
        "id": doc.get("message_id"),
        "content": doc.get("content"),
        "attachments": doc.get("attachments") or [],
        "isUser": doc.get("role") == "user",
        "feedback": doc.get("feedback"),
        "timestamp": doc.get("timestamp"),
        "runtimeLogs": runtime_logs or None,
    }


def message_model_to_doc(message: Dict[str, Any]) -> Dict[str, Any]:
    """Convert a camelCase message dict back to MongoDB format."""
    runtime_logs = [
        {
            "subtask_id": rl.get("subtaskId"),
            "description": rl.get("description"),
            "status": rl.get("status"),
            "ts_start": rl.get("tsStart"),
            "ts_end": rl.get("tsEnd"),
        }
        for rl in message.get("runtimeLogs", []) or []
    ]

    return {
        "message_id": message.get("id"),
        "role": "user" if message.get("isUser") else "ai",
        "content": message.get("content"),
        "attachments": message.get("attachments"),
        "feedback": message.get("feedback"),
        "timestamp": message.get("timestamp"),
        "runtime_logs": runtime_logs or None,
    }


# ──────────────────────────────────────────────────
#                          CHATS
# ──────────────────────────────────────────────────


def chat_doc_to_model(doc: Dict[str, Any]) -> Dict[str, Any]:
    """Convert a chat document from MongoDB to camelCase."""
    messages = [message_doc_to_model(m) for m in doc.get("messages", [])]

    return {
        "id": doc.get("conversation_id"),
        "title": doc.get("title", ""),
        "preview": doc.get("preview", ""),
        "messages": messages,
        "runtime": doc.get("runtime", []),
        "createdAt": doc.get("created_at"),
        "updatedAt": doc.get("updated_at"),
        "lastMessageAt": doc.get("last_message_at"),
        "isFavorite": doc.get("is_favorite", False),
        "isTask": doc.get("is_task", False),
    }


def chat_model_to_doc(chat: Dict[str, Any]) -> Dict[str, Any]:
    """Convert a camelCase chat dict to MongoDB format."""
    messages = [message_model_to_doc(m) for m in chat.get("messages", [])]

    doc: Dict[str, Any] = {
        "conversation_id": chat.get("id"),
        "title": chat.get("title"),
        "messages": messages,
        "created_at": chat.get("createdAt"),
        "updated_at": chat.get("updatedAt"),
        "last_message_at": chat.get("lastMessageAt"),
        "is_favorite": chat.get("isFavorite"),
        "is_task": chat.get("isTask"),
        "preview": chat.get("preview"),
    }
    runtime = chat.get("runtime")
    if runtime is not None:
        doc["runtime"] = runtime
    return doc
