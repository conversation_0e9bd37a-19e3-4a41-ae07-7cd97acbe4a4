# app/db/files.py

import gridfs
from bson.objectid import ObjectId
from app.db import db

# Wrap Mongo’s GridFS
fs = gridfs.GridFS(db)

def upload_pdf(local_path: str) -> str:
    """
    Store a PDF on GridFS and return its ObjectId as a hex string.
    """
    with open(local_path, "rb") as f:
        file_id = fs.put(f, filename=local_path, contentType="application/pdf")
    return str(file_id)

def get_pdf(file_id: str) -> bytes:
    """
    Retrieve raw PDF bytes by its GridFS ObjectId (hex string).
    """
    oid = ObjectId(file_id)
    grid_out = fs.get(oid)
    return grid_out.read()
