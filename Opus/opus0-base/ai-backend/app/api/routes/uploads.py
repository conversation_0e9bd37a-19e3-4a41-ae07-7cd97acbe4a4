# app/api/routes/uploads.py

import uuid
import boto3
from fastapi import APIRouter, HTTPException, UploadFile, File
from pydantic import BaseModel
from app.core.config import settings

router = APIRouter(prefix="/uploads", tags=["uploads"])

ALLOWED_DOC_EXTENSIONS = {"docx", "csv", "pptx", "pdf"}
ALLOWED_IMAGE_EXTENSIONS = {"png", "jpg", "jpeg"}


class PresignRequest(BaseModel):
    filename: str
    mime: str

class PresignResponse(BaseModel):
    url: str       # presigned PUT URL
    key: str
    get_url: str   # presigned GET URL for preview

@router.post("/presign", response_model=PresignResponse)
def presign_upload(req: PresignRequest):
    """
    Generate a presigned PUT URL for a file upload to Cloudflare R2.
    """
    
    # Reject unsupported file types early
    ext = req.filename.rsplit(".", 1)[-1].lower()
    if ext not in ALLOWED_DOC_EXTENSIONS | ALLOWED_IMAGE_EXTENSIONS:
        raise HTTPException(400, f"Unsupported file type: {ext}")

    # 1️⃣ Get R2 config from settings
    endpoint   = str(settings.R2_ENDPOINT)    
    bucket     = settings.R2_BUCKET_NAME
    access_id  = settings.R2_ACCESS_KEY_ID
    secret_key = settings.R2_SECRET_ACCESS_KEY

    if not all([endpoint, bucket, access_id, secret_key]):
        raise HTTPException(
            status_code=500,
            detail="Missing one of R2_ENDPOINT, R2_BUCKET_NAME, "
                   "R2_ACCESS_KEY_ID or R2_SECRET_ACCESS_KEY in settings"
        )

    # 2️⃣ Create the S3-compatible client
    client = boto3.client(
        "s3",
        endpoint_url=endpoint,
        region_name="auto",
        aws_access_key_id=access_id,
        aws_secret_access_key=secret_key,
    )

    # 3️⃣ Generate a unique key
    key = f"{uuid.uuid4()}-{req.filename}"

    try:
        # 4️⃣ Mint the presigned PUT URL
        put_url = client.generate_presigned_url(
            ClientMethod="put_object",
            Params={"Bucket": bucket, "Key": key, "ContentType": req.mime},
            ExpiresIn=900,
        )
        
        # 5️⃣ Mint the presigned GET URL for preview
        get_url = client.generate_presigned_url(
            ClientMethod="get_object",
            Params={"Bucket": bucket, "Key": key},
            ExpiresIn=900,
        )

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to generate presign URL: {e}"
        )

    return {"url": put_url, "key": key, "get_url": get_url}

@router.post("", response_model=PresignResponse)
async def upload_file(file: UploadFile = File(...)):
    """Upload a file directly to R2 via the backend."""

    ext = file.filename.rsplit(".", 1)[-1].lower()
    if ext not in ALLOWED_DOC_EXTENSIONS | ALLOWED_IMAGE_EXTENSIONS:
        raise HTTPException(400, f"Unsupported file type: {ext}")

    endpoint   = str(settings.R2_ENDPOINT)
    bucket     = settings.R2_BUCKET_NAME
    access_id  = settings.R2_ACCESS_KEY_ID
    secret_key = settings.R2_SECRET_ACCESS_KEY

    if not all([endpoint, bucket, access_id, secret_key]):
        raise HTTPException(
            status_code=500,
            detail="Missing one of R2_ENDPOINT, R2_BUCKET_NAME, R2_ACCESS_KEY_ID or R2_SECRET_ACCESS_KEY in settings",
        )

    client = boto3.client(
        "s3",
        endpoint_url=endpoint,
        region_name="auto",
        aws_access_key_id=access_id,
        aws_secret_access_key=secret_key,
    )

    key = f"{uuid.uuid4()}-{file.filename}"
    try:
        content = await file.read()
        client.put_object(
            Bucket=bucket,
            Key=key,
            Body=content,
            ContentType=file.content_type,
        )
        get_url = client.generate_presigned_url(
            ClientMethod="get_object",
            Params={"Bucket": bucket, "Key": key},
            ExpiresIn=900,
        )
    except Exception as e:
        raise HTTPException(500, f"Failed to upload file: {e}")

    return {"url": get_url, "key": key, "get_url": get_url}

@router.delete("/{key}")
def delete_upload(key: str):
    """
    Delete the object with the given key from Cloudflare R2.
    """
    endpoint   = str(settings.R2_ENDPOINT)
    bucket     = settings.R2_BUCKET_NAME
    access_id  = settings.R2_ACCESS_KEY_ID
    secret_key = settings.R2_SECRET_ACCESS_KEY

    client = boto3.client(
        "s3",
        endpoint_url=endpoint,
        region_name="auto",
        aws_access_key_id=access_id,
        aws_secret_access_key=secret_key,
    )

    try:
        client.delete_object(Bucket=bucket, Key=key)
    except Exception as e:
        raise HTTPException(500, f"Failed to delete object: {e}")

    return {"deleted": True}
