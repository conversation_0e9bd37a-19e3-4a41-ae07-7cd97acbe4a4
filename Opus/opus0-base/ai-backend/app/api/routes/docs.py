# app/api/routes/docs.py
"""
Module: docs.py

Provides a short redirect link for downloading documents stored in
Cloudflare R2.
"""

import logging
from fastapi import APIRouter, HTTPException
from fastapi.responses import RedirectResponse

from app.utils.r2_client import presign_get_url

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/docs/{key}")
def redirect_doc(key: str):
    """Redirect to a presigned R2 GET URL for the given key."""
    try:
        url = presign_get_url(key)
    except Exception as e:
        logger.error(f"Failed to presign {key}: {e}")
        raise HTTPException(status_code=404, detail="Document not found")
    return RedirectResponse(url)
