# app/api/routes/files.py

from fastapi import APIRouter, HTTPException, Response
from bson import ObjectId
from app.db.files import get_pdf
import logging
logger = logging.getLogger(__name__)

router = APIRouter()

@router.get("/files/{file_id}")
async def serve_file(file_id: str):
    """
    Stream a PDF previously stored in GridFS.
    """
    try:
        data = get_pdf(file_id)
    except Exception as e:
        logger.error(f"failed to load PDF {file_id} from GridFS: {e}")
        raise HTTPException(404, "File not found")
    return Response(content=data, media_type="application/pdf")