# app/api/routes/auth.py
"""
Module: auth.py

Simple access code authentication endpoint.
"""

import logging
from datetime import datetime
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
import requests as httpx
from google.oauth2 import id_token
from google.auth.transport import requests

from app.core.config import settings
from app.db import users_collection

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/auth", tags=["auth"])


class LoginRequest(BaseModel):
    code: str


@router.post("/login")
def login(req: LoginRequest):
    """Validate the provided access code."""
    if req.code != settings.ACCESS_CODE:
        logger.warning("Invalid access code attempt")
        raise HTTPException(status_code=401, detail="Invalid code")
    return {"success": True}


class GoogleToken(BaseModel):
    token: str


@router.post("/google")
def google_login(payload: GoogleToken):
    """Authenticate using a Google ID or access token."""
    try:
        if payload.token.startswith("ya"):
            resp = httpx.get(
                "https://www.googleapis.com/oauth2/v3/userinfo",
                headers={"Authorization": f"Bearer {payload.token}"},
                timeout=10,
            )
            resp.raise_for_status()
            info = resp.json()
        else:
            info = id_token.verify_oauth2_token(
                payload.token,
                requests.Request(),
                settings.GOOGLE_OAUTH_CLIENT_ID,
            )
    except Exception as exc:  # noqa: BLE001
        logger.error("Google token verification failed: %s", exc)
        raise HTTPException(status_code=401, detail="Invalid token") from exc

    google_id = info.get("sub")
    if not google_id:
        raise HTTPException(status_code=400, detail="Missing Google ID")

    users_collection.update_one(
        {"google_id": google_id},
        {
            "$set": {
                "email": info.get("email"),
                "name": info.get("name"),
                "picture": info.get("picture"),
                "last_login": datetime.utcnow(),
            },
            "$setOnInsert": {"created_at": datetime.utcnow()},
        },
        upsert=True,
    )
    user = users_collection.find_one({"google_id": google_id})
    if not user:
        raise HTTPException(status_code=500, detail="User lookup failed")

    return {
        "success": True,
        "user_id": str(user.get("_id")),
        "name": user.get("name"),
        "picture": user.get("picture"),
        "email": user.get("email"),
    }
