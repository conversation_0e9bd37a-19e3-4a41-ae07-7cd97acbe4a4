# app/api/routes/command.py

import logging
import asyncio
from datetime import datetime
from typing import AsyncGenerator
from rich.console import Console
from rich.tree import Tree

from bson import ObjectId
from app.db import chat_histories_collection
from app.agents.managers.task_planner import TaskPlanner
from app.agents.managers.operations_manager import OperationsManagerAgent
from app.agents.agent_factory import AgentFactory
from app.agents.aggregator_agent import AggregatorAgent
from app.utils.task_status import read_task_status

from langchain.memory import ConversationBufferMemory
from langchain import LLMChain

logger = logging.getLogger(__name__)
console = Console()


def print_stage(stage: str):
    """Print a tree-like separator for a new logical stage."""
    tree = Tree(f"[bold blue]{stage}[/]")
    console.print(tree)


def truncate_message(message, max_length=200):
    """Truncate the message if it exceeds max_length characters."""
    return message if len(message) <= max_length else message[:max_length] + "..."


def _serialize_attachments(atts: list[dict] | None) -> list[dict]:
    """Return public attachment metadata with download URLs."""
    if not atts:
        return []
    return [
        {
            "name": a.get("name", ""),
            "size": a.get("size", ""),
            "url": f"/docs/{a.get('key')}" if a.get("key") else "",
        }
        for a in atts
        if a.get("key")
    ]


async def process_message(
    user_message: str,
    chat_id: str,
    user_id: str,
    uploaded_docs_text: str = "",
    uploaded_images: list[str] = None,
    *,
    message_id: str | None = None,
    attachments: list[dict] | None = None,
    message_mode: str = "chat",
) -> AsyncGenerator[str, None]:
    """
    Processes an incoming user message by:
    1) Generating a new task_status.json (via Task Planner).
    2) Running OperationsManagerAgent & AgentFactory until all subtasks are done.
    3) Once done, invoking the AggregatorAgent to produce a final aggregated response,
       which is then yielded back to the frontend.
    """
    try:
        # Stage 1: find or create the history doc for this exact chatId
        print_stage("Chat History Setup")
        chat_history_doc = chat_histories_collection.find_one(
            {
                "conversation_id": chat_id,
            }
        )
        if not chat_history_doc:
            now = datetime.utcnow()
            chat_history_doc = {
                "conversation_id": chat_id,
                "user_id": ObjectId(user_id),
                "title": "Untitled Chat",
                "preview": "",
                "created_at": now,
                "updated_at": now,
                "last_message_at": None,
                "is_favorite": False,
                "is_task": False,
                "messages": [],
            }
            chat_histories_collection.insert_one(chat_history_doc)
            logger.info(f"Created chat_history for {chat_id}")

        chat_history = chat_history_doc.get("messages", [])

        # Stage: Append User Message
        print_stage("Append User Message")
        if not chat_history or chat_history[-1]["content"] != user_message:
            now = datetime.utcnow()
            clean_atts = [
                {"key": a.get("key"), "name": a.get("name"), "size": a.get("size")}
                for a in (attachments or [])
            ]
            new_message = {
                "role": "user",
                "message_id": message_id,
                "content": user_message,
                "attachments": clean_atts,
                "message_mode": message_mode,
                "timestamp": now,
            }
            chat_histories_collection.update_one(
                {"conversation_id": chat_id},
                {
                    "$push": {"messages": new_message},
                    "$set": {"updated_at": now, "last_message_at": now},
                },
            )
            logger.info(f"Appended USER → {chat_id}")
            chat_history.append(new_message)

        # Stage: Task Planning
        print_stage("Task Planning")
        task_planner = TaskPlanner(agent_id="task_planner", chat_id=chat_id)
        task_details = {
            "instructions": user_message,
            "history": chat_history,
            "uploaded_docs_text": uploaded_docs_text,
            "uploaded_images": uploaded_images or [],
        }
        logger.info("Calling Task Planner")
        await task_planner.execute_task(task_details)
        logger.info("Task status generated")

        # Stage: Operations & Assignment
        print_stage("Operations & Assignment")
        done_event = asyncio.Event()
        operations_manager = OperationsManagerAgent(
            agent_id="manager",
            done_event=done_event,
            chat_history=chat_history,
            chat_id=chat_id,
        )
        factory = AgentFactory(done_event=done_event, chat_id=chat_id)

        logger.info("Running Operations Manager and Agent Factory concurrently")
        await asyncio.gather(factory.run_async(), operations_manager.run_async())

        # Stage: Aggregation and Streaming
        print_stage("Aggregation & Streaming")
        aggregator = AggregatorAgent(chat_id)
        combined_output = ""

        logger.info("Starting aggregation and streaming response")
        async for chunk in aggregator.generate_final_response_stream():
            combined_output += chunk
            await asyncio.sleep(0)
            yield chunk

        logger.info("Aggregator final output: " + truncate_message(combined_output))

    except Exception as e:
        logger.error("Error processing message: " + str(e))
        yield {
            "status": "error",
            "message": "Failed to process message. Please try again.",
        }
