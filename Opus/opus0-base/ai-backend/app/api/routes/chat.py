# app/api/routes/chat.py
"""Chat API for simple conversations.

This module provides a streaming chat endpoint that can use either
OpenAI or Google Gemini models via LangChain. The specific model is
selected based on the frontend-provided option.
"""

import logging
from datetime import datetime

from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrO<PERSON><PERSON><PERSON>arser
from typing import AsyncGenerator
from langchain_core.runnables import Runnable

from langchain_openai import Chat<PERSON><PERSON><PERSON><PERSON>
from langchain_google_genai import ChatGoogleGenerative<PERSON>I

from bson import ObjectId
from app.core.config import settings
from app.db import chat_histories_collection

logger = logging.getLogger(__name__)

# ──────────────────────────────────────────────────
#                         MODEL
# ──────────────────────────────────────────────────

_MODEL_MAP = {
    "GPT-4.1": "gpt-4.1",
    "o3": "o3",
    "o4 Mini": "o4-mini",
    "Gemini 2.5 Flash": "gemini-2.5-flash",
    "Gemini 2.5 Pro": "gemini-2.5-pro",
}
prompt = ChatPromptTemplate.from_template(
    """You are a helpful AI assistant.\n\n{chat_history}\nUser: {input}\nAssistant:"""
)


def build_chain(model_choice: str | None) -> Runnable:
    """Return a LangChain chain using the chosen model."""
    name = _MODEL_MAP.get(model_choice or "Gemini 2.5 Flash", "gemini-2.5-flash")
    if name.startswith("gemini"):
        llm = ChatGoogleGenerativeAI(api_key=settings.GOOGLE_API_KEY, model=name)
    else:
        llm = ChatOpenAI(api_key=settings.OPENAI_API_KEY, model=name)
    return prompt | llm | StrOutputParser()


def _serialize_attachments(atts: list[dict] | None) -> list[dict]:
    """Return public attachment metadata with download URLs."""
    if not atts:
        return []
    return [
        {
            "name": a.get("name", ""),
            "size": a.get("size", ""),
            "url": f"/docs/{a.get('key')}" if a.get("key") else "",
        }
        for a in atts
        if a.get("key")
    ]


# ──────────────────────────────────────────────────
#                         PUBLIC API
# ──────────────────────────────────────────────────
async def process_message(
    message: str,
    chat_id: str,
    user_id: str,
    model_choice: str | None = None,
    *,
    message_id: str | None = None,
    attachments: list[dict] | None = None,
    message_mode: str = "chat",
) -> AsyncGenerator[str, None]:
    """Generate a reply using the chosen model and stream the conversation."""
    # Retrieve or create chat history document
    chat_history_doc = chat_histories_collection.find_one({"conversation_id": chat_id})
    if not chat_history_doc:
        now = datetime.utcnow()
        auto_title = message if len(message) <= 50 else f"{message[:47]}..."
        chat_history_doc = {
            "conversation_id": chat_id,
            "user_id": ObjectId(user_id),
            "title": auto_title,
            "preview": "",
            "created_at": now,
            "updated_at": now,
            "last_message_at": None,
            "is_favorite": False,
            "is_task": False,
            "messages": [],
        }
        chat_histories_collection.insert_one(chat_history_doc)
        logger.info(f"Created chat_history for {chat_id}")
    chat_history = chat_history_doc.get("messages", [])
    if not chat_history and chat_history_doc.get("title") == "Untitled Chat":
        auto_title = message if len(message) <= 50 else f"{message[:47]}..."
        chat_histories_collection.update_one(
            {"conversation_id": chat_id}, {"$set": {"title": auto_title}}
        )

    # Append user message with metadata
    now = datetime.utcnow()
    clean_atts = [
        {"key": a.get("key"), "name": a.get("name"), "size": a.get("size")}
        for a in (attachments or [])
    ]

    new_message = {
        "role": "user",
        "message_id": message_id,
        "content": message,
        "attachments": clean_atts,
        "message_mode": message_mode,
        "timestamp": now,
    }
    chat_histories_collection.update_one(
        {"conversation_id": chat_id},
        {
            "$push": {"messages": new_message},
            "$set": {"updated_at": now, "last_message_at": now},
        },
    )
    logger.info(f"Appended USER → {chat_id}")
    chat_history.append(new_message)

    # Format history for the model
    formatted = ""
    for msg in chat_history:
        role = "User" if msg["role"] == "user" else "Assistant"
        formatted += f"{role}: {msg['content']}\n"

    inputs = {"chat_history": formatted, "input": message}

    chain = build_chain(model_choice)

    # Stream model response while collecting the final text
    chunks: list[str] = []
    async for chunk in chain.astream(inputs):
        chunks.append(chunk)
        yield chunk

    # The frontend stores the AI message via ``store_ai_message``.
    # ``result`` holds the complete assistant reply if needed by callers.
    result = "".join(chunks)
