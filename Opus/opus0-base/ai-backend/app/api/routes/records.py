# app/api/routes/records.py
"""
Module: records.py

Expose REST endpoints to retrieve chat documents from MongoDB.
"""

from __future__ import annotations

import logging
from fastapi import APIRouter, HTTPException

from bson import ObjectId
from app.db import chat_histories_collection
from app.db.transform import chat_doc_to_model
from app.api.routes.chat import _serialize_attachments

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/chats", tags=["chats"])


@router.get("/{chat_id}")
def fetch_chat(chat_id: str, user_id: str | None = None) -> dict:
    """Return the full chat document for ``chat_id``."""
    query = {"conversation_id": chat_id}
    if user_id:
        query["user_id"] = ObjectId(user_id)
    doc = chat_histories_collection.find_one(query)
    if not doc:
        raise HTTPException(status_code=404, detail="Chat not found")

    chat = chat_doc_to_model(doc)
    for msg in chat.get("messages", []):
        msg["attachments"] = _serialize_attachments(msg.get("attachments", []))
    return chat


@router.get("")
def list_chats(user_id: str | None = None) -> list[dict]:
    """Return basic summaries for all chats."""
    query = {"user_id": ObjectId(user_id)} if user_id else {}
    docs = chat_histories_collection.find(query).sort("created_at", -1)
    summaries = []
    for doc in docs:
        model = chat_doc_to_model(doc)
        model.pop("messages", None)
        summaries.append(model)
    return summaries


@router.delete("/{chat_id}")
def delete_chat(chat_id: str) -> dict:
    """Remove the chat document for ``chat_id``."""
    result = chat_histories_collection.delete_one({"conversation_id": chat_id})
    if not result.deleted_count:
        raise HTTPException(status_code=404, detail="Chat not found")
    logger.info(f"Deleted chat history for {chat_id}")
    return {"deleted": True}