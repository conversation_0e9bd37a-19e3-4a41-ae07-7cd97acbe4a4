{"id": 1, "task": "Find about the top 5 AI department universities in London and give 10 professors from each uni including their contact details.", "response": {"title": "UCL Professors Contact Details Outline", "sections": [{"UCL Professor List": "Names; Titles; Departments; Contact Info"}]}, "subtask_desc": "Find 10 professors from UCL and their contact details.", "user_input": "Find about the top 5 AI department universities in London and give 10 professors from each uni including their contact details.", "deps_info": "Detailed data on 10 UCL professors, including names, emails, phone numbers, and departmental roles, gathered from recent research.", "explanation": "As this is an pdf outline. Having Different sections for names, department, contact doesn't make sense as all should be together."}
{"id": 2, "task": "Write a research paper on renewable energy trends.", "response": {"title": "Renewable Energy Innovations Outline", "sections": [{"Introduction": "Research objectives; Importance of renewable energy", "Innovations": "Solar advancements; Wind technology; Geothermal breakthroughs", "Market Trends": "Investment opportunities; Growth forecasts; Potential challenges"}]}, "subtask_desc": "Find information focusing on recent innovations in renewable energy.", "user_input": "Write a research paper on renewable energy trends.", "deps_info": "Summarized findings from studies on solar, wind, and geothermal energy, highlighting breakthrough technologies and market trends."}
{"id": 3, "task": "Plan a detailed travel itinerary for a 7-day trip to Japan.", "response": {"title": "Tokyo 7-Day Itinerary Outline", "sections": [{"Month-Long Itinerary": "Introduction; Breif about the trip", "Week 1 Itinerary (Tokyo & Tsujido)": "Day to day details; Day to day budget breif", "Week 2 Itinerary (Kanazawa & Shirakawa-go)": "Day to day details; Day to day budget breif", "Week 3 Itinerary (Kyushu Exploration)": "Day to day details; Day to day budget breif", "Week 4 Itinerary (Hiroshima & Return)": "Day to day details; Day to day budget breif", "Detailed Budget Plan": "Overall budget summary; Cost breakdown by category (accommodation, transport, food, activities); Contingency planning", "Week 1 Budget (Tokyo & Tsujido)": "Accommodation costs; Transportation expenses; Food budget; Activity costs; Daily estimates", "Week 2 Budget (Kanazawa & Shirakawa-go)": "Accommodation costs; Transportation expenses; Food budget; Activity costs; Daily estimates", "Week 3 Budget (Kyushu Exploration)": "Accommodation costs; Transportation expenses; Food budget; Activity costs; Daily estimates", "Week 4 Budget (Hiroshima & Return)": "Accommodation costs; Transportation expenses; Food budget; Activity costs; Daily estimates", "Useful Local Tips, Words & Phrases": "Essential phrases for communication; Cultural etiquette; Transportation tips; Dining advice; Safety guidelines"}]}, "subtask_desc": "Outline day-by-day activities in Tokyo.", "user_input": "Plan a detailed travel itinerary for a 7-day trip to Japan.", "deps_info": "Research includes top attractions, local cuisine spots, and cultural events in Tokyo."}
{"id": 4, "task": "Gather detailed information about Elon Musk.", "response": {"title": "Elon Musk Biography Outline", "sections": [{"Early Life": "Birth details; Education; Formative influences", "Career Milestones": "Entrepreneurial journey; Key achievements; Major innovations", "Personal Insights": "Public controversies; Leadership style; Vision and influence"}]}, "subtask_desc": "Compile detailed info of Elon Musk's biography, covering his early life, career milestones, and personal insights.", "user_input": "Gather detailed information about Elon Musk.", "deps_info": "Collected research includes data on Elon Musk's early education, entrepreneurial journey, major achievements, public controversies, and his influence on technology and space exploration."}
{"id": 5, "task": "Given image 1, 2, 3 and information about the wedding, generate me a wedding invitation card in a pdf that is luxurious. Woody & Jenny's wedding", "response": {"title": "Woody & Jenny", "sections": [{"Wedding invitation": "Luxurious heading and subheading; provided images, cards for Ceremony and reception; RSVP"}]}, "subtask_desc": "Generate the pdf given all the information", "user_input": "Given image 1, 2, 3 and information about the wedding, generate me a wedding invitation card in a pdf that is luxurious. Woody & Jenny's wedding", "deps_info": ""}
{"id": 6, "task": "Given info, research best practices and build me a personal resume", "response": {"title": "Resume", "sections": [{"Resume": "Contact; Skills; Experience; Education; Projects; Mentorship; Awards & Certificates"}]}, "subtask_desc": "Generate the pdf given all the information", "user_input": "Given info, research best practices and build me a personal resume", "deps_info": ""}