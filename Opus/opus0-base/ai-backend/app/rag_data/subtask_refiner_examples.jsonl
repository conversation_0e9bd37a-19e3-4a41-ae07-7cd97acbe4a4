{"id": 1, "task": "find AI professors at universities", "all_subtasks_data": {"subtasks": [{"id": "1", "desc": "Identify 3 famous universities in London that have AI programs or departments.", "deps": [], "uploads": [], "status": "completed", "output_file": "./app/knowledge_base/1_20250309001612.md"}, {"id": "2", "desc": "Collect information on AI professors at the indentified universities.", "deps": ["1"], "uploads": [], "status": "pending"}]}, "completed_subtask_desc": "Identify 3 famous universities in London that have AI programs or departments.", "completed_subtask_output": "Imperial, UCL, and KCL have AI departments.", "pending_subtask_desc": "Find AI researchers in those universities.", "response": {"subtasks": [{"id": "2", "desc": "Find AI researchers at Imperial College", "deps": ["1"], "uploads": []}, {"id": "2.1", "desc": "Find AI researchers at UCL", "deps": ["1"], "uploads": []}, {"id": "2.2", "desc": "Find AI researchers at KCL", "deps": ["1"], "uploads": []}]}, "reasoning": "Specific university names were provided in the completed subtask output. This is information that could be used to create new subtasks, so the pending subtask is split accordingly.", "document_type": "research", "style": "academic"}
{"id": 2, "task": "generate PDFs for multiple resumes", "all_subtasks_data": {"subtasks": [{"id": "1", "desc": "Generate a resume for a person in tech doing software engineering.", "deps": [], "uploads": [], "status": "completed", "output_file": "./app/knowledge_base/1_20250309002000.md"}, {"id": "2", "desc": "Generate a resume for the second person in tech doing software engineering.", "deps": [], "uploads": [], "status": "in_progress"}, {"id": "3", "desc": "Generate a resume for the third person in tech doing software engineering.", "deps": [], "uploads": [], "status": "in_progress"}, {"id": "4", "desc": "Generate a pdf for each resume", "deps": [1, 2, 3], "uploads": [], "status": "pending"}]}, "completed_subtask_desc": "Generate a resume for a person in tech doing software engineering.", "completed_subtask_output": "John Doe Fabrication Engineer | [Phone Number] | [Email Address] | [LinkedIn Profile] Professional Summary Highly motivated and detail-oriented Software Engineer with extensive experience in engineering design and process optimization. Skilled in creating innovative solutions to complex problems and delivering high-quality results. Professional Experience Lead Software Engineer, Lockheed Martin, Bethesda, MD Led the design,...", "pending_subtask_desc": "Generate a pdf for each resume.", "response": {"subtasks": [{"id": "4", "desc": "Generate the pdf for the first resume", "deps": ["1"], "uploads": []}, {"id": "4.1", "desc": "Generate the pdf for the second resume", "deps": ["2"], "uploads": []}, {"id": "4.2", "desc": "Generate the pdf for the third resume", "deps": ["3"], "uploads": []}]}, "reasoning": "As you can see, we need seperate pdf for each person. For that, we need to create seperate subtasks.", "document_type": "resume", "style": "professional"}
{"id": 3, "task": "create detailed travel itinerary", "all_subtasks_data": {"subtasks": [{"id": "1", "desc": "Find famous food spots in India.", "deps": [], "uploads": [], "status": "completed", "output_file": "./app/knowledge_base/1_20250309002000.md"}, {"id": "2", "desc": "Find cultural places in different states in India.", "deps": [], "uploads": [], "status": "in_progress"}, {"id": "3", "desc": "Find best nature site seeing spots in India.", "deps": [], "uploads": [], "status": "completed"}, {"id": "4", "desc": "Generate a 30 day itinerary with food spots, cultural places and best nature site seeing spots.", "deps": [1, 2, 3], "uploads": [], "status": "pending"}]}, "completed_subtask_desc": "Find best nature site seeing spots in India.", "completed_subtask_output": "Day 1-5 we will be in kanto, then we will shift to tohoku till day 10. We will go to hokkaido till 15 and chubu till 19th day. Then, shikoku for 2-3 days till day 22 and kansai till 28. Then finally kyushu to okinawa on the final days.", "pending_subtask_desc": "Generate a 30 day itinerary for the trip.", "response": {"subtasks": [{"id": "4", "desc": "create a high-level 30-day itinerary plan, grouping researched hidden gems, rural areas, and tech locations by region and planning the overall flow and travel time between regions.", "deps": ["1"], "uploads": []}, {"id": "4.1", "desc": "Generate a detailed plan for day 1-5 (Kanto) of the trip beginning the trip", "deps": ["4, 1, 2, 3"], "uploads": []}, {"id": "4.2", "desc": "Generate a detailed plan for the 6-10 (Tohoku) of the trip", "deps": ["4, 1, 2, 3"], "uploads": []}, {"id": "4.3", "desc": "Generate a detailed plan for the 11-15 (Hokkaido) of the trip", "deps": ["4, 1, 2, 3"], "uploads": []}, {"id": "4.4", "desc": "Generate a detailed plan for the 16-19 (Chubu) of the trip", "deps": ["4, 1, 2, 3"], "uploads": []}, {"id": "4.5", "desc": "Generate a detailed plan for the 20-22 (Shikoku) of the trip", "deps": ["4, 1, 2, 3"], "uploads": []}, {"id": "4.6", "desc": "Generate a detailed plan for the 23-28 (Kansai) of the trip", "deps": ["4, 1, 2, 3"], "uploads": []}, {"id": "4.7", "desc": "Generate a detailed plan for the 29-30 (Kyushu -> Okinawa) of the trip ending the trip", "deps": ["4, 1, 2, 3"], "uploads": []}]}, "reasoning": "The output for subtask 4 needs to be detailed but the current description is not detailed enough. We need a overview plan of the trip based on regions of exploration. Then, given how many days we are going to spend in a region, we can generate initerary based on that making it much better quality", "document_type": "itinerary", "style": "detailed"}
{"id": 4, "task": "analyze MKBHD video content", "all_subtasks_data": {"subtasks": [{"id": "1", "desc": "Find the link to the latest MKBHD video on YouTube.", "deps": [], "uploads": [], "status": "completed", "output_file": "./app/knowledge_base/1_20250309001612.md"}, {"id": "2", "desc": "Use the provided link to find information about the video", "deps": ["1"], "uploads": [], "status": "pending"}]}, "completed_subtask_desc": "Find the link to the latest MKBHD video on YouTube.", "completed_subtask_output": "### Latest MKBHD Video:\n    **Title:** CMF Phone 2 Pro: Budget Phone of the Year!\n    **Link:** [https://www.youtube.com/watch?v=hIzjHBwjGCk](https://www.youtube.com/watch?v=hIzjHBwjGCk)\n    **Upload Date:** 7 days ago (from the crawl date)\n    **Views:** 1,524,298 views\n    **Description Snippet:** Modular. Cheap. And Fun.\n\n### Other Recent Videos:\n    **I Have A Problem with the Light Phone III:** [https://www.youtube.com/watch?v=HdTCDxX-UnQ](https://www.youtube.com/watch?v=HdTCDxX-UnQ) (2 weeks ago, 2.5M views)\n    **Reviewing a Bionic Hand!:** [https://www.youtube.com/watch?v=VTs8wnMsh0k](https://www.youtube.com/watch?v=VTs8wnMsh0k) (3 weeks ago, 2.3M views)\n    **Oppo Find X8 Ultra: The Bar's Been Raised!:** [https://www.youtube.com/watch?v=p24qhUFIe68](https://www.youtube.com/watch?v=p24qhUFIe68) (4 weeks ago, 2.4M views)", "pending_subtask_desc": "Use the provided link: [https://www.youtube.com/watch?v=hIzjHBwjGCk] to find information about the video", "response": {"subtasks": []}, "reasoning": "The completed subtask already provided the specific video link and basic information. The pending subtask can proceed with this information without needing to be split further.", "document_type": "video_analysis", "style": "no_change"}
{"id": 5, "task": "answer questions from image", "all_subtasks_data": {"subtasks": [{"id": "1", "desc": "Find all the questions in the given image.", "deps": [], "uploads": ["upload_image_1.png"], "status": "completed", "output_file": "./app/knowledge_base/1_20250501120000.md"}, {"id": "2", "desc": "For each question, generate an answer.", "deps": ["1"], "uploads": [], "status": "pending"}]}, "completed_subtask_desc": "Find all the questions in the given image.", "completed_subtask_output": "List of questions found in upload_image_1.png:\n1. What is the capital of France?\n2. Who wrote 'To Kill a Mockingbird'?\n3. Solve 12 × 8.\n4. Name the largest planet in our solar system.\n5. What year did the Berlin Wall fall?", "pending_subtask_desc": "For each question, generate an answer.", "response": {"subtasks": [{"id": "2", "desc": "Provide the answer to 'What is the capital of France?'", "deps": ["1"], "uploads": ["upload_image_1.png"]}, {"id": "2.1", "desc": "Provide the answer to 'Who wrote To Kill a Mockingbird?'", "deps": ["1"], "uploads": ["upload_image_1.png"]}, {"id": "2.2", "desc": "Provide the answer to 'Solve 12 × 8.'", "deps": ["1"], "uploads": ["upload_image_1.png"]}, {"id": "2.3", "desc": "Provide the answer to 'Name the largest planet in our solar system.'", "deps": ["1"], "uploads": ["upload_image_1.png"]}, {"id": "2.4", "desc": "Provide the answer to 'What year did the Berlin Wall fall?'", "deps": ["1"], "uploads": ["upload_image_1.png"]}]}, "reasoning": "Five distinct questions were extracted from the image, so each one becomes its own pending‐answer subtask, all requiring access to the original image.", "document_type": "qa", "style": "educational"}
{"id": 6, "task": "summarize document sections", "all_subtasks_data": {"subtasks": [{"id": "1", "desc": "Extract all section headings from upload_1.md.", "deps": [], "uploads": ["upload_1.md"], "status": "completed", "output_file": "./app/knowledge_base/1_20250501123000.md"}, {"id": "2", "desc": "Generate detailed notes for each extracted section.", "deps": ["1"], "uploads": [], "status": "pending"}]}, "completed_subtask_desc": "Extract all section headings from upload_1.md.", "completed_subtask_output": "Sections found in upload_1.md:\n- Introduction\n- System Architecture\n- API Endpoints\n- Data Models\n- Security Considerations\n- Deployment Strategy", "pending_subtask_desc": "Generate detailed notes for each extracted section.", "response": {"subtasks": [{"id": "2", "desc": "Write detailed notes for the 'Introduction' section of upload_1.md.", "deps": ["1"], "uploads": ["upload_1.md"]}, {"id": "2.1", "desc": "Write detailed notes for the 'System Architecture' section of upload_1.md.", "deps": ["1"], "uploads": ["upload_1.md"]}, {"id": "2.2", "desc": "Write detailed notes for the 'API Endpoints' section of upload_1.md.", "deps": ["1"], "uploads": ["upload_1.md"]}, {"id": "2.3", "desc": "Write detailed notes for the 'Data Models' section of upload_1.md.", "deps": ["1"], "uploads": ["upload_1.md"]}, {"id": "2.4", "desc": "Write detailed notes for the 'Security Considerations' section of upload_1.md.", "deps": ["1"], "uploads": ["upload_1.md"]}, {"id": "2.5", "desc": "Write detailed notes for the 'Deployment Strategy' section of upload_1.md.", "deps": ["1"], "uploads": ["upload_1.md"]}]}, "reasoning": "Six section headings were identified, so each becomes its own subtask. All require the original document file.", "document_type": "documentation", "style": "technical"}
{"id": 7, "task": "create PDF report with mixed content", "all_subtasks_data": {"subtasks": [{"id": "1", "desc": "Extract relevant text from upload_1.md and upload_image_2.png.", "deps": [], "uploads": ["upload_1.md", "upload_image_2.png"], "status": "completed", "output_file": "./app/knowledge_base/1_20250501124500.md"}, {"id": "2", "desc": "Create a PDF combining extracted text and annotated images.", "deps": ["1"], "uploads": [], "status": "pending"}]}, "completed_subtask_desc": "Extract relevant text from upload_1.md and upload_image_2.png.", "completed_subtask_output": "• From upload_1.md: \n- Overview of module A, B, and C.\n- Detailed API usage examples for module A.\n• From upload_image_2.png: \n- Screenshot annotations for UI flow: Login → Dashboard → Settings.\n- Highlighted data‐entry form fields.", "pending_subtask_desc": "Create a PDF combining extracted text and annotated images.", "response": {"subtasks": [{"id": "2", "desc": "Design PDF layout: arrange module overviews and include UI flow screenshots from upload_image_2.png.", "deps": ["1"], "uploads": ["upload_1.md", "upload_image_2.png"]}, {"id": "2.1", "desc": "Generate the PDF: merge extracted text sections from upload_1.md with annotated screenshots from upload_image_2.png.", "deps": ["2"], "uploads": ["upload_1.md", "upload_image_2.png"]}]}, "reasoning": "Two logical steps are needed—first design the layout, then produce the actual PDF file. Both subtasks require access to the original document and screenshot.", "document_type": "report", "style": "mixed_media"}
