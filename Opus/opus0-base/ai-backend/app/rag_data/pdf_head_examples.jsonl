{"id": 1, "task": "create a wedding invitation poster", "response": {"head_content": "<head>\n  <meta charset=\"UTF-8\" />\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\"/>\n  <title>Wedding Poster</title>\n  <script src=\"https://cdn.tailwindcss.com\"></script>\n  <!-- Fonts: script for cursive, serif for titles -->\n  <link\n    href=\"https://fonts.googleapis.com/css2?family=Great+Vibes&family=Playfair+Display:wght@400;700&display=swap\"\n    rel=\"stylesheet\"\n  />\n  <style>\n    .font-script {{ font-family: 'Great Vibes', cursive; }}\n    .font-serif  {{ font-family: 'Playfair Display', serif; }}\n  </style>\n</head>", "title": "WEDDING INVITATION POSTER WITH IMAGE EXAMPLE"}, "document_type": "wedding_invitation", "style": "elegant"}
{"id": 2, "task": "create a single column resume", "response": {"head_content": "  <head>\n    <meta charset=\"UTF-8\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\n    <title>Jacqueline Thompson — Resume</title>\n\n    <!-- Tailwind CSS CDN -->\n    <script src=\"https://cdn.tailwindcss.com\"></script>\n\n    <!-- Google Fonts -->\n    <link href=\"https://fonts.googleapis.com/css2?family=Inter:wght@400;500&family=Poppins:wght@600;700&display=swap\" rel=\"stylesheet\" />\n\n    <style>\n      body {{ font-family: 'Inter', sans-serif; }}\n      .font-heading {{ font-family: 'Poppins', sans-serif; }}\n      .text-purple {{ --tw-text-opacity: 1; color: rgba(118, 72, 178, var(--tw-text-opacity)); }}\n      .border-purple {{ --tw-border-opacity: 1; border-color: rgba(118, 72, 178, var(--tw-border-opacity)); }}\n    </style>\n  </head>", "title": "SINGLE COLUMN WELL STYLED CV"}, "document_type": "resume", "style": "professional"}
{"id": 3, "task": "create a travel itinerary", "response": {"head_content": "  <head>\n    <meta charset=\"UTF-8\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\n    <title>Japan 7-Day Itinerary</title>\n\n    <!-- Tailwind CSS CDN -->\n    <script src=\"https://cdn.tailwindcss.com\"></script>\n    <script>\n      // Japan-themed fonts and colors\n      tailwind.config = {{\n        theme: {{\n          extend: {{\n            fontFamily: {{\n              body: ['Noto Sans JP', 'sans-serif'],\n              heading: ['Niconne', 'cursive'],\n            }},\n            colors: {{\n              japanRed: '#D32F2F',\n              sakura:   '#F8BBD0',\n              indigo:   '#264653',\n              midnight: '#212121',\n            }},\n          }},\n        }},\n      }}\n    </script>\n\n    <!-- Google Fonts -->\n    <link href=\"https://fonts.googleapis.com/css2?family=Niconne&family=Noto+Sans+JP:wght@400;500&display=swap\" rel=\"stylesheet\" />\n\n    <!-- Ensure custom classes display correctly -->\n    <style>\n      .font-heading {{ font-family: 'Niconne', cursive; }}\n      .font-body    {{ font-family: 'Noto Sans JP', sans-serif; }}\n      .text-japanRed {{ color: #D32F2F !important; }}\n      .bg-japanRed   {{ background-color: #D32F2F !important; }}\n      .text-indigo   {{ color: #264653 !important; }}\n      .text-midnight {{ color: #212121 !important; }}\n    </style>\n  </head>", "title": "ITINERARY EXAMPLE CULTURAL (For a modern one, remove fonts and colours)"}, "document_type": "itinerary", "style": "cultural"}
{"id": 4, "task": "create an article", "response": {"head_content": "  <head>\n    <meta charset=\"UTF-8\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\n    <title>Entwined Rivals: Apple & Samsung—and Beyond</title>\n    <!-- Tailwind CSS -->\n    <script src=\"https://cdn.tailwindcss.com\"></script>\n    <!-- Google Fonts -->\n    <link href=\"https://fonts.googleapis.com/css2?family=Merriweather:ital,wght@0,400;0,700;1,400;1,700&family=Montserrat:wght@400;600;700&display=swap\" rel=\"stylesheet\" />\n    <style>\n      body {{ font-family: 'Merriweather', serif; color: #333; background: #fff; }}\n      h1,h2,h3 {{ font-family: 'Montserrat', sans-serif; }}\n      blockquote {{ font-style: italic; color: #555; border-left: 4px solid #ccc; padding-left: 1rem; }}\n    </style>\n  </head>", "title": "ARTICLE"}, "document_type": "article", "style": "professional"}
