{"user_input": "Create a quarterly sales report for our three product lines", "subtask_desc": "Generate Excel spreadsheet with quarterly sales data", "deps_info": "Product lines: Software, Hardware, Services", "response": {"title": "Quarterly Sales Report 2024", "worksheets": [{"name": "Sales Data", "purpose": "Main quarterly sales figures by product line", "charts": [{"type": "bar", "title": "Quarterly Sales by Product Line", "purpose": "Visual comparison of sales performance across quarters", "position": "F2", "style": 10, "options": {"width": 15, "height": 10, "grouping": "clustered", "show_legend": true, "x_axis_title": "Product Line", "y_axis_title": "Sales ($)"}}]}, {"name": "Summary", "purpose": "Executive summary and key metrics", "charts": [{"type": "pie", "title": "Total Sales Distribution", "purpose": "Show proportion of sales by product line", "position": "D2", "style": 12, "options": {"width": 12, "height": 8, "show_legend": true}}]}], "formatting": {"currency_format": "$#,##0.00", "percentage_format": "0.0%", "header_style": {"bold": true, "background_color": "CCCCCC"}}}, "explanation": "Creates a professional sales report with separate worksheets for data and summary, including bar chart for quarterly comparisons and pie chart for distribution analysis"}
{"user_input": "Track monthly expenses for our department", "subtask_desc": "Create expense tracking spreadsheet", "deps_info": "Categories: Travel, Office Supplies, Software, Training", "response": {"title": "Monthly Expense Tracker", "worksheets": [{"name": "Expenses", "purpose": "Monthly expense entries by category", "charts": [{"type": "bar", "title": "Expenses by Category", "purpose": "Compare spending across different expense categories", "position": "G2", "style": 10, "options": {"width": 14, "height": 9, "grouping": "clustered", "show_legend": true, "x_axis_title": "Category", "y_axis_title": "Amount ($)"}}]}, {"name": "Budget vs Actual", "purpose": "Budget comparison and variance analysis", "charts": [{"type": "line", "title": "Budget vs Actual Trend", "purpose": "Track budget variance over time", "position": "E2", "style": 11, "options": {"width": 15, "height": 10, "show_legend": true, "x_axis_title": "Month", "y_axis_title": "Amount ($)"}}]}], "formatting": {"currency_format": "$#,##0.00", "percentage_format": "0.0%", "header_style": {"bold": true, "background_color": "E6F3FF"}}}, "explanation": "Expense tracking with budget comparison functionality, including bar chart for category analysis and line chart for trend tracking"}
{"user_input": "Create an inventory management system", "subtask_desc": "Build inventory tracking Excel file", "deps_info": "Track: Product ID, Name, Quantity, Reorder Level, Supplier", "response": {"title": "Inventory Management System", "worksheets": [{"name": "Current Inventory", "purpose": "Current stock levels and product details"}, {"name": "Reorder Report", "purpose": "Items that need reordering"}, {"name": "Supplier Info", "purpose": "Supplier contact and pricing information"}], "formatting": {"currency_format": "$#,##0.00", "percentage_format": "0.0%", "header_style": {"bold": true, "background_color": "F0F8E8"}}}, "explanation": "Comprehensive inventory system with reorder alerts and supplier management"}
{"user_input": "Generate a project timeline and budget tracker", "subtask_desc": "Create project management Excel template", "deps_info": "Track: Tasks, Start/End dates, Budget, Actual costs, Progress", "response": {"title": "Project Management Dashboard", "worksheets": [{"name": "Timeline", "purpose": "Project tasks and schedule", "charts": [{"type": "bar", "title": "Task Progress Overview", "purpose": "Visual representation of task completion status", "position": "H2", "style": 10, "options": {"width": 16, "height": 10, "grouping": "stacked", "show_legend": true, "x_axis_title": "Tasks", "y_axis_title": "Progress %"}}]}, {"name": "Budget", "purpose": "Budget vs actual cost tracking", "charts": [{"type": "line", "title": "Budget vs Actual Costs", "purpose": "Track budget variance over project timeline", "position": "F2", "style": 11, "options": {"width": 15, "height": 10, "show_legend": true, "x_axis_title": "Time Period", "y_axis_title": "Cost ($)"}}]}, {"name": "Dashboard", "purpose": "Project overview and key metrics", "charts": [{"type": "pie", "title": "Budget Allocation", "purpose": "Show how budget is distributed across project phases", "position": "D2", "style": 12, "options": {"width": 12, "height": 8, "show_legend": true}}]}], "formatting": {"currency_format": "$#,##0.00", "percentage_format": "0.0%", "date_format": "mm/dd/yyyy", "header_style": {"bold": true, "background_color": "FFF2CC"}}}, "explanation": "Complete project management solution with timeline and budget tracking, featuring progress bar chart, cost trend line chart, and budget allocation pie chart"}
{"user_input": "Create a customer database with sales history", "subtask_desc": "Build customer relationship management spreadsheet", "deps_info": "Include: Customer details, Purchase history, Contact information", "response": {"title": "Customer Database & Sales History", "worksheets": [{"name": "Customers", "purpose": "Customer contact and demographic information"}, {"name": "Sales History", "purpose": "Purchase records and transaction details", "charts": [{"type": "line", "title": "Sales Trend Over Time", "purpose": "Track sales performance trends by month", "position": "G2", "style": 11, "options": {"width": 15, "height": 10, "show_legend": true, "x_axis_title": "Month", "y_axis_title": "Sales ($)"}}]}, {"name": "Analytics", "purpose": "Customer analytics and sales trends", "charts": [{"type": "bar", "title": "Top Customers by Revenue", "purpose": "Identify highest value customers", "position": "F2", "style": 10, "options": {"width": 14, "height": 9, "grouping": "clustered", "show_legend": true, "x_axis_title": "Customer", "y_axis_title": "Revenue ($)"}}, {"type": "pie", "title": "Customer Tier Distribution", "purpose": "Show distribution of customers across tiers", "position": "F15", "style": 12, "options": {"width": 10, "height": 7, "show_legend": true}}]}], "formatting": {"currency_format": "$#,##0.00", "percentage_format": "0.0%", "date_format": "mm/dd/yyyy", "header_style": {"bold": true, "background_color": "E8F4FD"}}}, "explanation": "CRM system with customer data and sales analytics, including sales trend line chart, top customers bar chart, and customer tier pie chart"}
{"user_input": "Create a financial dashboard with revenue analysis and charts", "subtask_desc": "Generate comprehensive financial reporting spreadsheet", "deps_info": "Include revenue by month, product performance, and cost analysis", "response": {"title": "Financial Dashboard 2024", "worksheets": [{"name": "Revenue Analysis", "purpose": "Monthly revenue breakdown and trends", "charts": [{"type": "line", "title": "Monthly Revenue Trend", "purpose": "Track revenue growth over time", "position": "F2", "style": 11, "options": {"width": 16, "height": 11, "show_legend": true, "x_axis_title": "Month", "y_axis_title": "Revenue ($)"}}, {"type": "bar", "title": "Revenue by Product Category", "purpose": "Compare performance across product categories", "position": "F18", "style": 10, "options": {"width": 15, "height": 10, "grouping": "clustered", "show_legend": true, "x_axis_title": "Product Category", "y_axis_title": "Revenue ($)"}}]}, {"name": "Cost Analysis", "purpose": "Cost breakdown and margin analysis", "charts": [{"type": "pie", "title": "Cost Distribution", "purpose": "Show proportion of different cost categories", "position": "E2", "style": 12, "options": {"width": 12, "height": 8, "show_legend": true}}, {"type": "bar", "title": "Profit Margins by Product", "purpose": "Compare profitability across products", "position": "E15", "style": 10, "options": {"width": 14, "height": 9, "grouping": "clustered", "show_legend": true, "x_axis_title": "Product", "y_axis_title": "Margin %"}}]}], "formatting": {"currency_format": "$#,##0.00", "percentage_format": "0.0%", "header_style": {"bold": true, "background_color": "F0F8E8"}}}, "explanation": "Comprehensive financial dashboard with multiple chart types for revenue trends, product performance, cost analysis, and profit margins"}
{"user_input": "Build a survey results analysis with multiple chart visualizations", "subtask_desc": "Create survey data analysis spreadsheet with charts", "deps_info": "Survey responses on customer satisfaction, demographics, and preferences", "response": {"title": "Customer Survey Analysis", "worksheets": [{"name": "Response Data", "purpose": "Raw survey responses and calculations"}, {"name": "Satisfaction Analysis", "purpose": "Customer satisfaction metrics and trends", "charts": [{"type": "bar", "title": "Satisfaction Ratings by Category", "purpose": "Compare satisfaction across different service categories", "position": "G2", "style": 10, "options": {"width": 15, "height": 10, "grouping": "clustered", "show_legend": true, "x_axis_title": "Service Category", "y_axis_title": "Average Rating"}}, {"type": "pie", "title": "Overall Satisfaction Distribution", "purpose": "Show distribution of satisfaction levels", "position": "G18", "style": 12, "options": {"width": 11, "height": 8, "show_legend": true}}]}, {"name": "Demographics", "purpose": "Demographic breakdown of respondents", "charts": [{"type": "pie", "title": "Age Group Distribution", "purpose": "Show age demographics of survey respondents", "position": "D2", "style": 12, "options": {"width": 10, "height": 7, "show_legend": true}}, {"type": "bar", "title": "Responses by Region", "purpose": "Geographic distribution of survey responses", "position": "D12", "style": 10, "options": {"width": 13, "height": 8, "grouping": "clustered", "show_legend": true, "x_axis_title": "Region", "y_axis_title": "Response Count"}}]}], "formatting": {"percentage_format": "0.0%", "header_style": {"bold": true, "background_color": "E6F3FF"}}}, "explanation": "Survey analysis with multiple visualization types including satisfaction bar charts, distribution pie charts, and demographic breakdowns"}
