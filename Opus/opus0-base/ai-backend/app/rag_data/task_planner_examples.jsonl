{"id": 1, "task": "Translate \"How are you\" into Spanish.", "response": {"subtasks": [{"id": "1", "desc": "Translate 'How are you' into Spanish.", "deps": [], "uploads": [], "status": "pending"}], "criteria": "Ensure the translation accurately reflects the original meaning."}, "uploads": {"documents": [], "images": []}, "chat_history": null}
{"id": 2, "task": "What are the capitals of France, UK, India, and the US?", "response": {"subtasks": [{"id": "1", "desc": "Identify the capitals of France, UK, India, and the US.", "deps": [], "uploads": [], "status": "pending"}], "criteria": "Ensure all capitals are accurate and match their respective countries."}, "uploads": {"documents": [], "images": []}, "chat_history": null}
{"id": 3, "task": "Compile a list of 5 FDA-approved medications for hypertension, including their generic names, brand names, manufacturers, and common side effects.", "response": {"subtasks": [{"id": "1", "desc": "Compile a list of 5 unique FDA-approved medications for hypertension.", "deps": [], "uploads": [], "status": "pending"}, {"id": "2", "desc": "For each medication from the list, gather their generic names, brand names, manufacturers, and common side effects.", "deps": ["1"], "uploads": [], "status": "pending"}], "criteria": "Ensure all 5 medications are unique and include accurate and complete details as requested."}, "uploads": {"documents": [], "images": []}, "chat_history": null}
{"id": 4, "task": "Identify at least 20 AI researchers who teach at London universities, including their research interests and contact details and give in a pdf.", "response": {"subtasks": [{"id": "1", "desc": "Identify universities in London that have AI programs or departments.", "deps": [], "uploads": [], "status": "pending"}, {"id": "2", "desc": "Collect information on AI researchers teaching at the identified universities, including their research interests and contact details.", "deps": ["1"], "uploads": [], "status": "pending"}, {"id": "3", "desc": "Generate a PDF with all the information.", "deps": ["1", "2"], "uploads": [], "status": "pending"}], "criteria": "Identify at least 20 unique AI researchers teaching at London universities, including their research interests and contact details, and compile the results accurately into a cohesive response."}, "uploads": {"documents": [], "images": []}, "chat_history": null}
{"id": 5, "task": "Conduct a literature review on the effects of artificial intelligence on workforce dynamics in the next decade.", "response": {"subtasks": [{"id": "1", "desc": "Define the scope and keywords for the literature review on AI's effects on workforce dynamics in the next decade.", "deps": [], "uploads": [], "status": "pending"}, {"id": "2", "desc": "Conduct a literature search using the defined keywords to find relevant publications.", "deps": ["1"], "uploads": [], "status": "pending"}, {"id": "3", "desc": "Read and analyze the collected literature to extract key findings.", "deps": ["2"], "uploads": [], "status": "pending"}, {"id": "4", "desc": "Organize findings to identify common themes, trends, and insights.", "deps": ["3"], "uploads": [], "status": "pending"}], "criteria": "Produce a comprehensive and accurate literature review based on the collected research, strictly adhering to the user's instructions without adding assumptions or external information."}, "uploads": {"documents": [], "images": []}, "chat_history": null}
{"id": 6, "task": "I am planning on visiting japan next month. The plan is to have a 7 day trip. I want to really experience the culture and want to visit hidden places that don't have much tourists. Something that gives me the authentic culture and rural vibes of japan. I also want to see some advanced tech and interesting places in japan. Can you generate me a day by day guide of what I should do and where I should go in japan? I am looking for something that is in my budget of 3000 pounds and want to be in the budget. I will be going for 6 days from Delhi. Plan a trip for me including the expenses and what I am going to do each day. and what cost. Then, also give me some general tips and stuff. Something like words and phrases I should know. General tips about japan from a person who has lived there. Cultural things I need to be aware of and stuff like that Once you have done the research, generate a pdf with all this information and give it to me", "response": {"subtasks": [{"id": "1", "desc": "Research hidden, less touristy locations in Japan that showcase authentic culture and rural vibes.", "deps": [], "uploads": [], "status": "pending"}, {"id": "2", "desc": "Research advanced tech and interesting places in Japan to visit.", "deps": [], "uploads": [], "status": "pending"}, {"id": "3", "desc": "Create a 6-day itinerary that combines cultural experiences, rural locations, and technological attractions, starting the trip from Delhi.", "deps": ["1", "2"], "uploads": [], "status": "pending"}, {"id": "4", "desc": "Estimate the cost for each day of the itinerary, ensuring the total cost is within the 3000 pounds budget.", "deps": ["3"], "uploads": [], "status": "pending"}, {"id": "5", "desc": "Compile a list of general tips for traveling in Japan, including useful words and phrases, cultural awareness points, and other helpful information.", "deps": [], "uploads": [], "status": "pending"}, {"id": "6", "desc": "Generate a PDF document containing the 6-day itinerary, cost estimations, and travel tips.", "deps": ["3", "4", "5"], "uploads": [], "status": "pending"}], "criteria": "..."}, "uploads": {"documents": [], "images": []}, "chat_history": null}
{"id": 7, "task": "I want you to in the end, generate a research paper like pdf that has an abstract, introduction, etc... It should be a 2 column document just like some of the research papers. Use text and styling that match this style or research papers. Now, what you are going to do is write this research paper on ai advancements in multi -agent systems. Find all info about multi agent systems. things like open source projects. products, reesarch going on and more.", "response": {"subtasks": [{"id": "1", "desc": "Gather information about advancements in multi-agent systems.", "deps": [], "uploads": [], "status": "pending"}, {"id": "2", "desc": "Gather information about open source projects on multi-agent systems.", "deps": [], "uploads": [], "status": "pending"}, {"id": "3", "desc": "Gather information about products in the market that have multi-agent systems.", "deps": [], "uploads": [], "status": "pending"}, {"id": "4", "desc": "Gather information about ongoing research in multi-agent systems.", "deps": [], "uploads": [], "status": "pending"}, {"id": "5", "desc": "Generate a research paper-like PDF with an abstract, introduction, and other standard sections, styled in a 2-column format, focusing on AI advancements in multi-agent systems.", "deps": [1, 2, 3, 4], "uploads": [], "status": "pending"}], "criteria": "..."}, "uploads": {"documents": [], "images": []}, "chat_history": null}
{"id": 8, "task": "Build me a production ready snake game that can be shipped to the users in javascript", "response": {"subtasks": [{"id": "1", "desc": "Develop the core game logic for Snake in JavaScript, including movement, eating, and collision detection.", "deps": [], "uploads": [], "status": "pending"}, {"id": "2", "desc": "Create the game board and rendering mechanism using HTML and CSS, ensuring it's visually appealing and responsive.", "deps": [], "uploads": [], "status": "pending", "output_file": "./app/knowledge_base/2_20250427201052.md"}, {"id": "3", "desc": "Implement user input handling for controlling the snake's direction (e.g., arrow keys, touch controls).", "deps": ["1"], "uploads": [], "status": "pending", "output_file": "./app/knowledge_base/3_20250427201126.md"}, {"id": "4", "desc": "Add scoring, high score and game over functionality.", "deps": ["1"], "uploads": [], "status": "pending", "output_file": "./app/knowledge_base/4_20250427201204.md"}, {"id": "5", "desc": "Implement responsive design principles to ensure the game is playable on various screen sizes.", "deps": ["2"], "uploads": [], "status": "pending", "output_file": "./app/knowledge_base/5_20250427201139.md"}, {"id": "6", "desc": "thoroughly test the game on different browsers (desktop, mobile, tablet) to ensure compatibility and identify bugs.", "deps": ["1", "2", "3", "4", "5"], "uploads": [], "status": "pending", "output_file": "./app/knowledge_base/6_20250427201239.md"}, {"id": "7", "desc": "Do a final testing and review before shipping. Add anything necessary here.", "deps": ["1", "2", "3", "4", "5", "6"], "status": "pending", "output_file": "./app/knowledge_base/7_20250427201333.md"}]}, "uploads": {"documents": [], "images": []}, "chat_history": null}
{"id": 9, "task": "Compare these two contracts by pulling out all key obligations, spotting any conflicts, and summarizing the differences.", "response": {"subtasks": [{"id": "1", "desc": "Extract and list all key obligations from upload_1.md (client_agreement.pdf).", "deps": [], "uploads": ["upload_1.md"], "status": "pending"}, {"id": "2", "desc": "Extract and list all key obligations from upload_2.md (service_terms.pdf).", "deps": [], "uploads": ["upload_2.md"], "status": "pending"}, {"id": "3", "desc": "Identify and describe any conflicting or overlapping obligations between the two documents.", "deps": ["1", "2"], "uploads": [], "status": "pending"}, {"id": "4", "desc": "Summarize the differences and conflicts in a comparative table format.", "deps": ["3"], "uploads": ["upload_1.md", "upload_2.md"], "status": "pending"}], "criteria": "All obligations must be accurately extracted; conflicts clearly highlighted; summary presented as a concise, side-by-side table."}, "uploads": {"documents": ["upload_1.md", "upload_2.md"], "images": []}, "chat_history": null}
{"id": 10, "task": "given the image, generate me the exact same thing in html tailwind.", "response": {"subtasks": [{"id": "1", "desc": "Analyze the provided image (upload_image_1.png) to identify all visual elements, their layout, content (text, images), and styling properties (colors, fonts, spacing, dimensions, etc.).", "deps": [], "uploads": ["upload_image_1.png"], "status": "pending"}, {"id": "2", "desc": "Using the analysis from Subtask 1, generate the complete HTML code with Tailwind CSS classes to replicate the exact visual appearance of the image (upload_image_1.png). This task requires translating the visual structure and styling into code logic.", "deps": ["1"], "uploads": ["upload_image_1.png"], "status": "pending"}], "criteria": "given the image, generate me the exact same thing in html tailwind. Use a reasoner for this complex task"}, "uploads": {"documents": [], "images": ["upload_image_1.png"]}, "chat_history": null}
{"id": 11, "task": "Generate very detailed, SEO-friendly descriptions for each image and craft engaging captions with at least five relevant hashtags. Then give a pdf with these including the images.", "response": {"subtasks": [{"id": "1", "desc": "Provide a detailed description of upload_image_1.jpg (`product_shot.jpg`), focusing on design, color, and features.", "deps": [], "uploads": ["upload_image_1.jpg"], "status": "pending"}, {"id": "2", "desc": "Provide a detailed description of upload_image_2.jpg (`lifestyle_shot.jpg`), highlighting use-case context and mood.", "deps": [], "uploads": ["upload_image_2.jpg"], "status": "pending"}, {"id": "3", "desc": "Generate SEO-optimized captions for both images, each including at least 5 relevant hashtags.", "deps": ["1", "2"], "uploads": [], "status": "pending"}, {"id": "4", "desc": "Generate a PDF for the information using the images in the pdf", "deps": ["3"], "uploads": ["upload_image_1.jpg", "upload_image_2.jpg"], "status": "pending"}], "criteria": "Descriptions must be rich and accurate; captions must be catchy, clear, and include appropriate hashtags."}, "uploads": {"documents": [], "images": ["upload_image_1.jpg", "upload_image_2.jpg"]}, "chat_history": null}
{"id": 12, "task": "Create a plan to annotate the architecture diagram using details from the spec. Give it in a PDF.", "response": {"subtasks": [{"id": "1", "desc": "Extract all major system modules and their descriptions from upload_1.md (`system_design.docx`).", "deps": [], "uploads": ["upload_1.md"], "status": "pending"}, {"id": "2", "desc": "Analyze upload_image_1.jpg (`architecture_diagram.jpg`) and identify each diagram component.", "deps": [], "uploads": ["upload_image_1.png"], "status": "pending"}, {"id": "3", "desc": "Map each extracted module from the spec to the corresponding component in the diagram.", "deps": ["1", "2"], "uploads": [], "status": "pending"}, {"id": "4", "desc": "Produce an annotated overview showing how each specification module aligns with diagram parts.", "deps": ["3"], "uploads": [], "status": "pending"}, {"id": "5", "desc": "Generate a PDF with the provided information", "deps": ["3"], "uploads": ["upload_1.md", "upload_image_1.png"], "status": "pending"}], "criteria": "All modules from the spec must be correctly matched to diagram elements; final annotation plan should be clear and actionable."}, "uploads": {"documents": ["upload_1.md"], "images": ["upload_image_1.png"]}, "chat_history": null}
{"id": 13, "task": "Calculate the factorial of 5 using Python.", "response": {"subtasks": [{"id": "1", "desc": "Use the code_interpreter agent to run a Python script printing the factorial of 5.", "deps": [], "uploads": [], "status": "pending"}], "criteria": "Return the correct factorial value."}, "uploads": {"documents": [], "images": []}, "chat_history": null}
