{"id": 1, "task": "Create a minimalist title slide with gradient header and styled text elements", "code": "from pptx import Presentation\nfrom pptx.util import Inches, Pt\nfrom pptx.dml.color import RGBColor\nfrom pptx.enum.shapes import MSO_SHAPE, MSO_CONNECTOR\n\n# Load presentation and blank slide\nprs = Presentation('presentation_path.pptx')\nslide = prs.slides.add_slide(prs.slide_layouts[6])  # 6 = blank\n\n# Constants for slide dimensions\nSLD_W = prs.slide_width\nSLD_H = prs.slide_height\n\n# 1) Gradient header\nheader_height = Inches(2.5)\ngrad_shape = slide.shapes.add_shape(\n    MSO_SHAPE.RECTANGLE, 0, 0, SLD_W, header_height\n)\ngrad = grad_shape.fill\ngrad.gradient()                     # enable gradient fill\nstops = grad.gradient_stops         # get stops collection\n# Left stop (light green)\nstops[0].position = 0.0\nstops[0].color.rgb = RGBColor(126, 231, 135)\n# Right stop (turquoise)\nstops[1].position = 1.0\nstops[1].color.rgb = RGBColor(91, 245, 225)\ngrad.gradient_angle = 0            # 0° = left→right\ngrad_shape.line.fill.background() # remove border line\n\n# 2) Company name (top left)\ntx = slide.shapes.add_textbox(Inches(0.5), Inches(0.2), Inches(3), Inches(0.4))\np = tx.text_frame.paragraphs[0]\np.text = \"COMPANY NAME\"\np.font.name = \"Arial\"\np.font.size = Pt(12)\np.font.color.rgb = RGBColor(0, 0, 0)\n\n# 3) Date (top right)\ntx = slide.shapes.add_textbox(SLD_W - Inches(2.5), Inches(0.2), Inches(2), Inches(0.4))\np = tx.text_frame.paragraphs[0]\np.text = \"July 2024\"\np.font.name = \"Arial\"\np.font.size = Pt(12)\np.font.color.rgb = RGBColor(0, 0, 0)\np.alignment = 2  # right-align\n\n# 4) \"Simple\" (small) over title\ntx = slide.shapes.add_textbox(Inches(0.5), header_height + Inches(0.3), Inches(2), Inches(0.6))\np = tx.text_frame.paragraphs[0]\np.text = \"Simple\"\np.font.name = \"Arial\"\np.font.size = Pt(24)\np.font.color.rgb = RGBColor(0, 0, 0)\n\n# 5) \"Performance Review\" (big, two lines)\ntx = slide.shapes.add_textbox(Inches(0.5), header_height + Inches(1.0), Inches(8), Inches(3))\ntf = tx.text_frame\ntf.clear()\np1 = tf.paragraphs[0]\np1.text = \"Performance\"\np1.font.name = \"Arial\"\np1.font.size = Pt(64)\np1.font.bold = True\np1.font.color.rgb = RGBColor(0, 0, 0)\n# second line\np2 = tf.add_paragraph()\np2.text = \"Review\"\np2.font.name = \"Arial\"\np2.font.size = Pt(64)\np2.font.bold = True\np2.font.color.rgb = RGBColor(0, 0, 0)\n\n# 6) Underline (connector line)\ny_line = header_height + Inches(4.4)\nline = slide.shapes.add_connector(\n    MSO_CONNECTOR.STRAIGHT,\n    Inches(0.5), y_line,\n    Inches(8.0), y_line\n)\nline.line.width = Pt(2)\nline.line.color.rgb = RGBColor(0, 0, 0)\n\n# 7) \"Slides\" at end of line\ntx = slide.shapes.add_textbox(Inches(8.2), y_line - Pt(2), Inches(2), Inches(0.5))\np = tx.text_frame.paragraphs[0]\np.text = \"Slides\"\np.font.name = \"Arial\"\np.font.size = Pt(18)\np.font.color.rgb = RGBColor(0, 0, 0)\n\n# Save the file\nprs.save('presentation_path.pptx')", "explanation": "Creates a complete presentation with a professional slide featuring gradient header background, company branding elements, multi-line title text, and decorative connector line with proper imports and file saving"}
{"id": 2, "task": "Create content slide with title and body text paragraphs", "code": "from pptx import Presentation\nfrom pptx.util import Inches, Pt\nfrom pptx.dml.color import RGBColor\n\n# -------------------- 1. Load presentation & blank slide -------------------\nprs   = Presentation('presentation_path.pptx')\nslide = prs.slides.add_slide(prs.slide_layouts[6])   # blank\n\nSLD_W, SLD_H = prs.slide_width, prs.slide_height\n\n# Helper to place objects via inches\ndef px(left, top, width, height):\n    return Inches(left), Inches(top), Inches(width), Inches(height)\n\n# -------------------- 2. Company name (top-left) -----------------------------\ntb = slide.shapes.add_textbox(*px(0.8, 0.5, 4, 0.5))\np  = tb.text_frame.paragraphs[0]\np.text = \"COMPANY  NAME\"\np.font.name = \"Calibri Light\"\np.font.size = Pt(12)\np.font.color.rgb = RGBColor(0, 0, 0)\n\n# -------------------- 3. Date (top-right) ------------------------------------\ndate_tb = slide.shapes.add_textbox(SLD_W - Inches(2.3), Inches(0.5),\n                                   Inches(2.0), Inches(0.5))\np = date_tb.text_frame.paragraphs[0]\np.text = \"July 2024\"\np.font.name = \"Calibri Light\"\np.font.size = Pt(12)\np.font.color.rgb = RGBColor(0, 0, 0)\np.alignment = 2   # right-align\n\n# -------------------- 4. Slide title -----------------------------------------\ntitle_tb = slide.shapes.add_textbox(*px(0.8, 1, 10, 1.2))\np = title_tb.text_frame.paragraphs[0]\np.text = \"Title\"\np.font.name = \"Calibri Light\"\np.font.size = Pt(60)\np.font.color.rgb = RGBColor(0, 0, 0)\n\n# -------------------- 5. Body text (two paragraphs) ---------------------------\nlorem = (\n    \"Lorem ipsum dolor sit amet, consectetur adipiscing elit. Integer vitae \"\n    \"leo sapien. Morbi eget dictum turpis. Class aptent taciti sociosqu ad \"\n    \"litora torquent per conubia nostra, per inceptos himenaeos. Aliquam \"\n    \"euismod pharetra laoreet. Phasellus et nisl at eros maximus luctus. \"\n    \"Maecenas ornare ligula euismod sagittis pretium. Nunc vestibulum dictum \"\n    \"nulla, sed aliquet metus ornare eu.\"\n)\n\nbody_tb = slide.shapes.add_textbox(*px(0.5, 2.5, 9, 4.5))\ntf = body_tb.text_frame\ntf.word_wrap = True\ntf.clear()\n\n# First paragraph\np1 = tf.paragraphs[0]\np1.text = lorem\np1.font.name = \"Calibri\"\np1.font.size = Pt(20)\np1.font.color.rgb = RGBColor(0, 0, 0)\n\n# Spacing between paragraphs\np_spacing = tf.add_paragraph()\np_spacing.text = \"\"  # blank paragraph for spacing\n\n# Second paragraph (duplicate lorem text)\np2 = tf.add_paragraph()\np2.text = lorem\np2.font.name = \"Calibri\"\np2.font.size = Pt(20)\np2.font.color.rgb = RGBColor(0, 0, 0)\n\n# -------------------- 6. Save -------------------------------------------------\nprs.save('presentation_path.pptx')", "explanation": "Creates a content slide with company branding, title, and multi-paragraph body text using helper functions for positioning and proper text frame handling"}
{"id": 3, "task": "Create bullet point slide with gradient banner and styled content", "code": "from pptx import Presentation\nfrom pptx.util import Inches, Pt\nfrom pptx.dml.color import RGBColor\nfrom pptx.enum.shapes import MSO_SHAPE\nfrom pptx.oxml import parse_xml\n\n# 1) Load presentation and add a \"Title and Content\" slide for bullets\nprs = Presentation('presentation_path.pptx')\nbullet_slide_layout = prs.slide_layouts[1]\nslide = prs.slides.add_slide(bullet_slide_layout)\n\nSLD_W, SLD_H = prs.slide_width, prs.slide_height\n\n# 2) Draw the gradient banner behind the title\nbanner_h = Inches(1.8)\nbanner = slide.shapes.add_shape(\n    MSO_SHAPE.RECTANGLE, 0, 0, SLD_W, banner_h\n)\nfill = banner.fill\nfill.gradient()\nstops = fill.gradient_stops\nstops[0].position, stops[0].color.rgb = 0.0, RGBColor(126, 231, 135)\nstops[1].position, stops[1].color.rgb = 1.0, RGBColor(91, 245, 225)\nfill.gradient_angle = 0  # left→right\nbanner.line.fill.background()  # remove border\n\n# 3) Send the banner to the back so title & bullets appear on top\nspTree = slide.shapes._spTree\n# remove and re-insert at position 2 (after slide background)\nspTree.remove(banner._element)\nspTree.insert(2, banner._element)\n\n# 4) Header text over the banner (optional placeholders in layout)\n# SKIP\n\n# 5) Title text is already in the title placeholder; you can style it:\ntitle_shape = slide.shapes.title\ntitle_shape.text = \"Title Here\"\ntitle_shape.text_frame.paragraphs[0].font.name = \"Calibri Light\"\ntitle_shape.text_frame.paragraphs[0].font.size = Pt(60)\n\n# 6) Populate bullets in the content placeholder\nbody_shape = slide.shapes.placeholders[1]\ntf = body_shape.text_frame\n\n# First bullet\ntf.text = \"Bulletpoint 1…\"\np0 = tf.paragraphs[0]\np0.level = 0\np0.font.name = \"Calibri\"\np0.font.size = Pt(20)\n\n# Additional bullets\npoints = [\n    \"Bulletpoint 2…\",\n    \"Bulletpoint 3…\",\n    \"Bulletpoint 4…\",\n    \"Bulletpoint 5…\",\n    \"Bulletpoint 6…\",\n    \"Bulletpoint 7…\",\n]\nfor text in points:\n    p = tf.add_paragraph()\n    p.text = text\n    p.level = 0\n    p.font.name = \"Calibri\"\n    p.font.size = Pt(20)\n\n# 7) Save\nprs.save('presentation_path.pptx')", "explanation": "Creates a bullet point slide using Title and Content layout with gradient banner background, styled title text, and multiple bullet points with proper layering and font formatting"}
