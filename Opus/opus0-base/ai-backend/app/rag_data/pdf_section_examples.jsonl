{"id": 1, "task": "create a vertical timeline", "response": {"html_content": "  <section class=\"mx-20 my-12\">\n    <ol class=\"relative border-l-2 border-gray-800\">\n      <!-- Item 1 -->\n      <li class=\"mb-10 ml-6\">\n        <span class=\"absolute w-3 h-3 bg-gray-800 rounded-full -left-1.5 mt-1.5\"></span>\n        <time class=\"block mb-1 text-sm font-normal leading-none text-gray-600\">June 2022</time>\n        <h3 class=\"text-lg font-semibold text-gray-900\">Heading CSS</h3>\n        <p class=\"mb-4 text-base font-normal text-gray-700\">\n          Get access\n        </p>\n      </li>\n      <!-- Item 2 -->\n      <li class=\"mb-10 ml-6\">\n        <span class=\"absolute w-3 h-3 bg-gray-800 rounded-full -left-1.5 mt-1.5\"></span>\n        <time class=\"block mb-1 text-sm font-normal leading-none text-gray-600\"> Feb 2023</time>\n        <h3 class=\"text-lg font-semibold text-gray-900\">heading</h3>\n        <p class=\"text-base font-normal text-gray-700\">\n          More...\n        </p>\n      </li>\n      <!-- Item 3 -->\n      <li class=\"ml-6\">\n        <span class=\"absolute w-3 h-3 bg-gray-800 rounded-full -left-1.5 mt-1.5\"></span>\n        <time class=\"block mb-1 text-sm font-normal leading-none text-gray-600\">April 2023</time>\n        <h3 class=\"text-lg font-semibold text-gray-900\">heading</h3>\n        <p class=\"text-base font-normal text-gray-700\">\n          More...\n        </p>\n      </li>\n    </ol>\n  </section>", "title": "VERTICAL TIMELINE EXAMPLE"}, "document_type": "timeline", "style": "professional"}
{"id": 2, "task": "create a wedding invitation poster", "response": {"html_content": "  <div class=\"bg-[#f3efe8] border border-[#e1d6cd]\">\n    <!-- Photo with fade -->\n    <div class=\"relative overflow-hidden\">\n      <img\n        src=\"https://images.unsplash.com/photo-1519741497674-611481863552?q=80&w=1170&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D\"\n        alt=\"Wedding couple\"\n        class=\"w-full h-80 object-cover\"\n      />\n      <div class=\"absolute inset-x-0 bottom-0 h-32 bg-gradient-to-t from-[#f3efe8] to-transparent\"></div>\n    </div>\n\n    <!-- Text content -->\n    <div class=\"text-center px-6 py-12 mt-12\">\n      <h1 class=\"font-script text-8xl text-[#6e5a4f]\">Welcome</h1>\n      <p class=\"font-serif text-lg text-[#7a6e67] mt-4 tracking-widest\">TO THE CELEBRATION OF</p>\n      <h2 class=\"font-serif text-5xl text-[#3e352f] uppercase mt-8\">Girl</h2>\n      <p class=\"font-script text-3xl text-[#6e5a4f] mt-2\">and</p>\n      <h2 class=\"font-serif text-5xl text-[#3e352f] uppercase\">Boy</h2>\n      <p class=\"font-serif text-lg text-[#7a6e67] tracking-widest mt-8\">WEDDING</p>\n    </div>\n\n    <!-- Date with lines -->\n    <div class=\"flex items-center justify-center px-6 pb-8 mb-4\">\n      <div class=\"flex-grow border-t border-[#e1d6cd]\"></div>\n      <span class=\"font-serif text-base text-[#7a6e67] px-4\">daytime</span>\n      <div class=\"flex-grow border-t border-[#e1d6cd]\"></div>\n    </div>\n\n  </div>", "title": "WEDDING INVITATION POSTER WITH IMAGE EXAMPLE", "head_content": "<head>\n  <meta charset=\"UTF-8\" />\n  <title>Wedding Poster</title>\n  <script src=\"https://cdn.tailwindcss.com\"></script>\n  <!-- Fonts: script for cursive, serif for titles -->\n  <link\n    href=\"https://fonts.googleapis.com/css2?family=Great+Vibes&family=Playfair+Display:wght@400;700&display=swap\"\n    rel=\"stylesheet\"\n  />\n  <style>\n    .font-script {{ font-family: 'Great Vibes', cursive; }}\n    .font-serif  {{ font-family: 'Playfair Display', serif; }}\n  </style>\n</head>"}, "document_type": "wedding_invitation", "style": "elegant"}
{"id": 3, "task": "create a single column resume", "response": {"html_content": "    <div class=\"px-10 py-10\">\n      <!-- Name & Contact -->\n      <header class=\"mb-8 text-center\">\n        <h1 class=\"font-heading text-purple text-3xl uppercase\">Name</h1>\n        <p class=\"mt-1 text-gray-600\">\n          123 Anywhere St., Any City · 123-456-7890 · <EMAIL><br />\n          www.synagi.com\n        </p>\n      </header>\n\n      <!-- Summary -->\n      <section class=\"mb-8\">\n        <div class=\"mb-4 flex items-center\">\n          <h2 class=\"font-heading text-purple text-sm uppercase\">Summary</h2>\n          <div class=\"border-purple ml-4 flex-grow border-t-2\"></div>\n        </div>\n        <p>Summary</p>\n      </section>\n\n      <!-- Work Experience -->\n      <section class=\"mb-8\">\n        <div class=\"mb-4 flex items-center\">\n          <h2 class=\"font-heading text-purple text-sm uppercase\">Work Experience</h2>\n          <div class=\"border-purple ml-4 flex-grow border-t-2\"></div>\n        </div>\n        <div class=\"space-y-6\">\n          <!-- Item 1 -->\n          <div class=\"flex justify-between\">\n            <div class=\"w-3/4\">\n              <h3 class=\"font-heading text-base\">Exp1</h3>\n              <ul class=\"mt-2 list-inside list-disc space-y-1\">\n                <li>One.</li>\n                <li>two.</li>\n              </ul>\n            </div>\n            <div class=\"text-right text-gray-600\">Jan 2023 – Present</div>\n          </div>\n\n          <!-- Item 2 -->\n          <div class=\"flex justify-between\">\n            <div class=\"w-3/4\">\n              <h3 class=\"font-heading text-base\">Exp2</h3>\n              <ul class=\"mt-2 list-inside list-disc space-y-1\">\n                <li>One.</li>\n                <li>two.</li>\n              </ul>\n            </div>\n            <div class=\"text-right text-gray-600\">Mar 2021 – Dec 2022</div>\n          </div>\n\n          <!-- Item 3 -->\n          <div class=\"flex justify-between\">\n            <div class=\"w-3/4\">\n              <h3 class=\"font-heading text-base\">Exp3</h3>\n              <ul class=\"mt-2 list-inside list-disc space-y-1\">\n                <li>One.</li>\n                <li>two.</li>\n                <li>Three.</li>\n              </ul>\n            </div>\n            <div class=\"text-right text-gray-600\">Feb 2020 – Jan 2021</div>\n          </div>\n        </div>\n      </section>\n\n      <!-- Education -->\n      <section class=\"mb-8\">\n        <div class=\"mb-4 flex items-center\">\n          <h2 class=\"font-heading text-purple text-sm uppercase\">Education</h2>\n          <div class=\"border-purple ml-4 flex-grow border-t-2\"></div>\n        </div>\n        <div class=\"space-y-6\">\n          <!-- Degree 1 -->\n          <div class=\"flex justify-between\">\n            <div class=\"w-3/4\">\n              <h3 class=\"font-heading text-base\">Degree</h3>\n              <p class=\"mt-1\">University</p>\n              <ul class=\"mt-1 list-inside list-disc space-y-1 text-sm text-gray-700\">\n                <li>point.</li>\n                <li>point.</li>\n              </ul>\n            </div>\n            <div class=\"text-right text-gray-600\">Sep 2019 – Oct 2020</div>\n          </div>\n\n          <!-- Degree 2 -->\n          <div class=\"flex justify-between\">\n            <div class=\"w-3/4\">\n              <h3 class=\"font-heading text-base\">degree</h3>\n              <p class=\"mt-1\">college</p>\n              <ul class=\"mt-1 list-inside list-disc space-y-1 text-sm text-gray-700\">\n                <li>point.</li>\n              </ul>\n            </div>\n            <div class=\"text-right text-gray-600\">Aug 2015 – Aug 2019</div>\n          </div>\n        </div>\n      </section>\n\n      <!-- Additional Information -->\n      <section>\n        <div class=\"mb-4 flex items-center\">\n          <h2 class=\"font-heading text-purple text-sm uppercase\">Additional Information</h2>\n          <div class=\"border-purple ml-4 flex-grow border-t-2\"></div>\n        </div>\n        <ul class=\"list-none space-y-2 text-sm text-gray-700\">\n          <li><strong>Technical Skills:</strong> a, b, c</li>\n          <li><strong>Languages:</strong> English, German...</li>\n          <li><strong>Certifications:</strong> Professional Engineer (PE) License, Project Management Professional (PMP)</li>\n          ...\n        </ul>\n      </section>\n    </div>", "title": "SINGLE COLUMN WELL STYLED CV", "head_content": "  <head>\n    <meta charset=\"UTF-8\" />\n    <title>Jacqueline Thompson — Resume</title>\n\n    <!-- Tailwind CSS CDN -->\n    <script src=\"https://cdn.tailwindcss.com\"></script>\n\n    <!-- Google Fonts -->\n    <link href=\"https://fonts.googleapis.com/css2?family=Inter:wght@400;500&family=Poppins:wght@600;700&display=swap\" rel=\"stylesheet\" />\n\n    <style>\n      body {{ font-family: 'Inter', sans-serif; }}\n      .font-heading {{ font-family: 'Poppins', sans-serif; }}\n      .text-purple {{ --tw-text-opacity: 1; color: rgba(118, 72, 178, var(--tw-text-opacity)); }}\n      .border-purple {{ --tw-border-opacity: 1; border-color: rgba(118, 72, 178, var(--tw-text-opacity)); }}\n    </style>\n\n  </head>"}, "document_type": "resume", "style": "professional"}
{"id": 4, "task": "create a travel itinerary", "response": {"html_content": "    <!-- Hero with Tokyo image and overlay - japan themed -->\n    <header class=\"relative h-64 w-full overflow-hidden\">\n      <img src=\"https://images.unsplash.com/photo-1545569341-9eb8b30979d9?w=1200&auto=format&fit=crop&q=80\" alt=\"Tokyo Skyline\" class=\"h-full w-full object-cover\" />\n      <div class=\"absolute inset-0 bg-black/30\"></div>\n      <!-- Sakura icon + title with solid backdrop -->\n      <div class=\"absolute bottom-6 left-6 flex items-center space-x-2 rounded bg-white/80 p-2\">\n        <svg width=\"40\" height=\"40\" viewBox=\"0 0 64 64\" fill=\"none\">\n          <circle cx=\"32\" cy=\"32\" r=\"6\" fill=\"#F8BBD0\" />\n          <circle cx=\"32\" cy=\"12\" r=\"8\" fill=\"#F8BBD0\" />\n          <circle cx=\"52\" cy=\"32\" r=\"8\" fill=\"#F8BBD0\" />\n          <circle cx=\"32\" cy=\"52\" r=\"8\" fill=\"#F8BBD0\" />\n          <circle cx=\"12\" cy=\"32\" r=\"8\" fill=\"#F8BBD0\" />\n        </svg>\n        <h1 class=\"font-heading text-indigo text-5xl\">Japan Adventure</h1>\n      </div>\n    </header>\n\n    <!-- Vertical timeline -->\n    <main class=\"px-20 py-12\">\n      <ol class=\"relative border-l-2 border-gray-300\">\n        <!-- Day 1 -->\n        <li class=\"mb-12 ml-6\">\n          <span class=\"bg-japanRed absolute top-1 -left-3 h-6 w-6 rounded-full border-2 border-white\"></span>\n          <h2 class=\"font-heading text-indigo mb-2 text-2xl\">Day 1: Tokyo Arrival</h2>\n          <p class=\"text-sm text-gray-700\">Info...</p>\n          <p class=\"text-japanRed mt-1 text-sm font-medium\">Budget: ¥ (~$)</p>\n          <ul class=\"list-inside list-disc text-xs text-gray-600\">\n            <li>Transport: ¥</li>\n            <li>Meals: ¥</li>\n            <li>Accommodation: ¥</li>\n            ...\n          </ul>\n          <img src=\"https://images.unsplash.com/photo-1674814343675-23c631bfd3ab?w=400&auto=format&fit=crop&q=60\" alt=\"Tokyo Airport\" class=\"mt-4 h-40 w-full rounded object-cover\" />\n        </li>\n\n        <!-- Day 2 -->\n        <li class=\"mb-12 ml-6\">\n          <span class=\"bg-japanRed absolute top-1 -left-3 h-6 w-6 rounded-full border-2 border-white\"></span>\n          <h2 class=\"font-heading text-indigo mb-2 text-2xl\">Day 2: Location</h2>\n          <p class=\"text-sm text-gray-700\">Info...</p>\n          <p class=\"text-japanRed mt-1 text-sm font-medium\">Budget: ¥ (~$)</p>\n          <ul class=\"list-inside list-disc text-xs text-gray-600\">\n            <li>Transport: ¥</li>\n            <li>Meals: ¥</li>\n            <li>Accommodation: ¥</li>\n            ...\n          </ul>\n          <img src=\"placeholder image\" alt=\"Senso-ji Temple\" class=\"mt-4 h-40 w-full rounded object-cover\" />\n        </li>\n\n        <!-- Day 3 -->\n        <li class=\"mb-12 ml-6\">\n          <span class=\"bg-japanRed absolute top-1 -left-3 h-6 w-6 rounded-full border-2 border-white\"></span>\n          <h2 class=\"font-heading text-indigo mb-2 text-2xl\">Day 3: location</h2>\n          <p class=\"text-sm text-gray-700\">Info...</p>\n          <p class=\"text-japanRed mt-1 text-sm font-medium\">Budget: ¥ (~$)</p>\n          <ul class=\"list-inside list-disc text-xs text-gray-600\">\n            <li>Transport: ¥</li>\n            <li>Meals: ¥</li>\n            <li>Accommodation: ¥</li>\n            ...\n          </ul>\n          <img src=\"placeholder image..\" alt=\"Mount Fuji\" class=\"mt-4 h-40 w-full rounded object-cover\" />\n        </li>\n\n      </ol>\n    </main>\n\n    <section class=\"px-20 py-12\">\n      <hr class=\"mb-8 border-t border-gray-300\" />\n      <h2 class=\"font-heading text-indigo mt-12 mb-4 text-center text-3xl\">Budget Summary</h2>\n      <ol class=\"relative border-l-2 border-gray-300 pl-8\">\n        <li class=\"relative mb-6\">\n          <span class=\"bg-sakura absolute top-1 -left-4 h-4 w-4 rounded-full border-2 border-white\"></span>\n          <div class=\"flex justify-between\">\n            <span class=\"font-heading text-indigo\">Day 1</span>\n            <span class=\"text-midnight font-medium\">¥20,000 (~$150)</span>\n          </div>\n        </li>\n        <li class=\"relative mb-6\">\n          <span class=\"bg-sakura absolute top-1 -left-4 h-4 w-4 rounded-full border-2 border-white\"></span>\n          <div class=\"flex justify-between\">\n            <span class=\"font-heading text-indigo\">Day 2</span>\n            <span class=\"text-midnight font-medium\">¥15,000 (~$110)</span>\n          </div>\n        </li>\n        <li class=\"relative mb-6\">\n          <span class=\"bg-sakura absolute top-1 -left-4 h-4 w-4 rounded-full border-2 border-white\"></span>\n          <div class=\"flex justify-between\">\n            <span class=\"font-heading text-indigo\">Day 3</span>\n            <span class=\"text-midnight font-medium\">¥18,000 (~$130)</span>\n          </div>\n        </li>\n\n      </ol>\n      <div class=\"mt-10 flex items-center justify-center space-x-4\">\n        <span class=\"bg-japanRed inline-block h-6 w-6 rounded-full border-2 border-white\"></span>\n        <span class=\"font-heading text-japanRed text-2xl\">Total: ¥120,000 (~$890)</span>\n      </div>\n    </section>\n\n    <section class=\"px-20 py-12\">\n      <hr class=\"mb-8 border-t border-gray-300\" />\n      <div class=\"relative mt-12 mb-4 text-center\">\n        <h2 class=\"font-heading text-indigo text-3xl\">Travel Tips &amp; Useful Phrases</h2>\n        <div class=\"bg-sakura absolute top-full left-1/2 mt-2 h-1 w-24 -translate-x-1/2 transform rounded\"></div>\n      </div>\n\n      <div class=\"bg-indigo/10 rounded-lg p-8\">\n        <div class=\"grid grid-cols-1 gap-12 md:grid-cols-2\">\n          <!-- Travel Tips -->\n          <div>\n            <h3 class=\"text-midnight mb-4 text-2xl font-semibold\">Travel Tips</h3>\n            <ul class=\"space-y-6\">\n              <li class=\"flex items-start\">\n                <svg class=\"text-japanRed mt-1 h-6 w-6 flex-shrink-0\" fill=\"currentColor\" viewBox=\"0 0 24 24\"><path d=\"M12 2a1 1 0 011 1v4h4a1 1 0 110 2h-4v4a1 1 0 11-2 0v-4H7a1 1 0 110-2h4V3a1 1 0 011-1z\" /></svg>\n                <span class=\"ml-3 text-sm text-gray-800\"> tip. </span>\n              </li>\n              <li class=\"flex items-start\">\n                <svg class=\"text-japanRed mt-1 h-6 w-6 flex-shrink-0\" fill=\"currentColor\" viewBox=\"0 0 24 24\"><path d=\"M12 2a1 1 0 011 1v4h4a1 1 0 110 2h-4v4a1 1 0 11-2 0v-4H7a1 1 0 110-2h4V3a1 1 0 011-1z\" /></svg>\n                <span class=\"ml-3 text-sm text-gray-800\"> tip. </span>\n              </li>\n              <li class=\"flex items-start\">\n                <svg class=\"text-japanRed mt-1 h-6 w-6 flex-shrink-0\" fill=\"currentColor\" viewBox=\"0 0 24 24\"><path d=\"M12 2a1 1 0 011 1v4h4a1 1 0 110 2h-4v4a1 1 0 11-2 0v-4H7a1 1 0 110-2h4V3a1 1 0 011-1z\" /></svg>\n                <span class=\"ml-3 text-sm text-gray-800\"> tip. </span>\n              </li>\n              <li class=\"flex items-start\">\n                <svg class=\"text-japanRed mt-1 h-6 w-6 flex-shrink-0\" fill=\"currentColor\" viewBox=\"0 0 24 24\"><path d=\"M12 2a1 1 0 011 1v4h4a1 1 0 110 2h-4v4a1 1 0 11-2 0v-4H7a1 1 0 110-2h4V3a1 1 0 011-1z\" /></svg>\n                <span class=\"ml-3 text-sm text-gray-800\"> tip. </span>\n              </li>\n              <li class=\"flex items-start\">\n                <svg class=\"text-japanRed mt-1 h-6 w-6 flex-shrink-0\" fill=\"currentColor\" viewBox=\"0 0 24 24\"><path d=\"M12 2a1 1 0 011 1v4h4a1 1 0 110 2h-4v4a1 1 0 11-2 0v-4H7a1 1 0 110-2h4V3a1 1 0 011-1z\" /></svg>\n                <span class=\"ml-3 text-sm text-gray-800\"> tip. </span>\n              </li>\n              ...\n            </ul>\n          </div>\n\n          <!-- Useful Phrases -->\n          <div>\n            <h3 class=\"text-midnight mb-4 text-2xl font-semibold\">Useful Japanese Phrases</h3>\n            <ul class=\"space-y-6\">\n              <li>\n                <span class=\"font-heading text-japanRed text-lg\">こんにちは</span>\n                <span class=\"ml-2 text-sm text-gray-700 italic\">(Konnichiwa)</span>\n                <span class=\"ml-2 text-sm text-gray-800\">– Hello</span>\n              </li>\n              <li>\n                <span class=\"font-heading text-japanRed text-lg\">お願いします</span>\n                <span class=\"ml-2 text-sm text-gray-700 italic\">(Onegaishimasu)</span>\n                <span class=\"ml-2 text-sm text-gray-800\">– Please</span>\n              </li>\n              <li>\n                <span class=\"font-heading text-japanRed text-lg\">ありがとう</span>\n                <span class=\"ml-2 text-sm text-gray-700 italic\">(Arigatō)</span>\n                <span class=\"ml-2 text-sm text-gray-800\">– Thank you</span>\n              </li>\n              <li>\n                <span class=\"font-heading text-japanRed text-lg\">すみません</span>\n                <span class=\"ml-2 text-sm text-gray-700 italic\">(Sumimasen)</span>\n                <span class=\"ml-2 text-sm text-gray-800\">– Excuse me / Sorry</span>\n              </li>\n              <li>\n                <span class=\"font-heading text-japanRed text-lg\">いくらですか？</span>\n                <span class=\"ml-2 text-sm text-gray-700 italic\">(Ikura desu ka?)</span>\n                <span class=\"ml-2 text-sm text-gray-800\">– How much is it?</span>\n              </li>\n              <li>\n                <span class=\"font-heading text-japanRed text-lg\">トイレはどこですか？</span>\n                <span class=\"ml-2 text-sm text-gray-700 italic\">(Toire wa doko desu ka?)</span>\n                <span class=\"ml-2 text-sm text-gray-800\">– Where is the restroom?</span>\n              </li>\n              ...\n            </ul>\n          </div>\n        </div>\n      </div>\n    </section>", "title": "ITINERARY EXAMPLE CULTURAL (For a modern one, remove fonts and colours)", "head_content": "  <head>\n    <meta charset=\"UTF-8\" />\n    <title>Japan 7-Day Itinerary</title>\n\n    <!-- Tailwind CSS CDN -->\n    <script src=\"https://cdn.tailwindcss.com\"></script>\n    <script>\n      // Japan-themed fonts and colors\n      tailwind.config = {{\n        theme: {{\n          extend: {{\n            fontFamily: {{\n              body: ['Noto Sans JP', 'sans-serif'],\n              heading: ['Niconne', 'cursive'],\n            }},\n            colors: {{\n              japanRed: '#D32F2F',\n              sakura:   '#F8BBD0',\n              indigo:   '#264653',\n              midnight: '#212121',\n            }},\n          }},\n        }},\n      }}\n    </script>\n\n    <!-- Google Fonts -->\n    <link href=\"https://fonts.googleapis.com/css2?family=Niconne&family=Noto+Sans+JP:wght@400;500&display=swap\" rel=\"stylesheet\" />\n\n    <!-- Ensure custom classes display correctly -->\n    <style>\n      .font-heading {{ font-family: 'Niconne', cursive; }}\n      .font-body    {{ font-family: 'Noto Sans JP', sans-serif; }}\n      .text-japanRed {{ color: #D32F2F !important; }}\n      .bg-japanRed   {{ background-color: #D32F2F !important; }}\n      .text-indigo   {{ color: #264653 !important; }}\n      .text-midnight {{ color: #212121 !important; }}\n    </style>\n\n  </head>"}, "document_type": "itinerary", "style": "cultural"}
{"id": 5, "task": "create an article", "response": {"html_content": "    <article class=\"mx-auto max-w-audo p-14\">\n      <!-- Header -->\n      <header class=\"mb-12\">\n        <h1 class=\"mb-2 text-4xl font-bold\">Heading</h1>\n        <p class=\"text-sm text-gray-600\">By <strong>Synagi</strong> | May 1, 2025</p>\n        <div class=\"mt-6\">\n          <img src=\"https://picsum.photos/1200/600?tech\" alt=\"Apple & Samsung\" class=\"h-auto w-full rounded-lg shadow-lg\" />\n        </div>\n      </header>\n\n      <!-- Pull-quote -->\n      <blockquote class=\"my-10 text-xl leading-snug\">quote...\"</blockquote>\n\n      <!-- Two-column body -->\n      <div class=\"grid grid-cols-1 gap-8 text-lg leading-relaxed lg:grid-cols-2\">\n        <!-- Section 1 -->\n        <section>\n          <h2 class=\"mb-4 text-2xl font-semibold\">head</h2>\n          <p>para.</p>\n          <p class=\"mt-4\">para</p>\n        </section>\n\n        <!-- Section 2 -->\n        <section>\n          <h2 class=\"mb-4 text-2xl font-semibold\">head</h2>\n          <p>para</p>\n          <p class=\"mt-4\">para.</p>\n        </section>\n\n        <!-- Full-width visual -->\n        <div class=\"my-12 lg:col-span-2\">\n          <img src=\"https://picsum.photos/1200/400?economics\" alt=\"Supply chain\" class=\"h-auto w-full rounded-lg shadow\" />\n          <figcaption class=\"mt-2 text-center text-sm text-gray-500\">Figure: When rivalry meets cooperation, big tech's true power emerges.</figcaption>\n        </div>\n\n        <!-- Conclusion -->\n        <section>\n          <h2 class=\"mb-4 text-2xl font-semibold\">head</h2>\n          <p>para.</p>\n          <p class=\"mt-4\">para.</p>\n        </section>\n      </div>\n    </article>", "title": "ARTICLE EXAMPLE", "head_content": "  <head>\n    <meta charset=\"UTF-8\" />\n    <title>Entwined Rivals: Apple & Samsung—and Beyond</title>\n    <!-- Tailwind CSS -->\n    <script src=\"https://cdn.tailwindcss.com\"></script>\n    <!-- Google Fonts -->\n    <link href=\"https://fonts.googleapis.com/css2?family=Merriweather:ital,wght@0,400;0,700;1,400;1,700&family=Montserrat:wght@400;600;700&display=swap\" rel=\"stylesheet\" />\n    <style>\n      body {{ font-family: 'Merriweather', serif; color: #333; background: #fff; }}\n      h1,h2,h3 {{ font-family: 'Montserrat', sans-serif; }}\n      blockquote {{ font-style: italic; color: #555; border-left: 4px solid #ccc; padding-left: 1rem; }}\n    </style>\n  </head>"}, "document_type": "article", "style": "professional"}
