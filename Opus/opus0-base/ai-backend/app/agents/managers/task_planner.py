# app/agents/task_planner.py
"""
Module: task_planner.py

This module defines the TaskPlanner class, a manager bot responsible for generating the
`task_status.json` file based on the user input. It uses GPT-4 (via <PERSON><PERSON><PERSON><PERSON>) to break down
the main task into subtasks and assignment criteria, and then initializes the task status using
the generated plan. The class inherits from BaseAgent.
"""

import os
import logging
import json
from typing import Dict, Any
from langchain_openai import ChatOpenAI
from langchain_google_genai import Chat<PERSON>oogleGenerativeAI
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.messages import SystemMessage, HumanMessage
from langchain_core.output_parsers import StrOutputParser
from app.utils.constants import TASK_STATUS_FILE, get_knowledge_base_dir
from app.utils.task_status import initialize_task_status
from app.agents.base_agent import BaseAgent
from app.utils.token_counter import count_tokens
from app.utils.example_rag import get_examples_text

# for multimodal image caching
import base64
from google import genai
from google.genai import types
import time
from pathlib import Path
from app.core.config import settings


# Helper to summarize long data for logging
def summarize(data, max_length=100):
    s = str(data)
    return s if len(s) <= max_length else s[:max_length] + "..."


class TaskPlanner(BaseAgent):
    """
    Manager Bot responsible for generating the `task_status.json` file based on user input.

    Inherits from BaseAgent to leverage common functionalities such as logging and error handling.
    """

    def __init__(self, agent_id: str, chat_id: str):
        """
        Initializes the TaskPlanner with a specific agent ID and sets up the GPT-4 instance.

        Args:
            agent_id (str): Unique identifier for the TaskPlanner agent.
        """
        super().__init__(agent_id, agent_type="manager_bot")
        self.chat_id = chat_id
        self.gpt4 = ChatGoogleGenerativeAI(
            model="gemini-2.5-flash", temperature=0.1, top_p=1.0
        )

    async def execute_task(self, task_details: Dict[str, Any]):
        """
        Executes the main task of creating the `task_status.json` file.

        Steps:
          1. Extract user input and chat history.
          2. Generate subtasks and assignment criteria using GPT-4.
          3. Prepare the task status data and generate a unique task ID.
          4. Initialize the task status file using the provided utility.
          5. Report the successful creation of the task status file.

        Args:
            task_details (Dict[str, Any]): Dictionary containing task details, including the user query and history.
        """
        try:
            # Extract user input and history from task details.
            user_input = task_details.get("instructions", "")
            chat_history = task_details.get("history", [])
            uploaded_docs_text = task_details.get("uploaded_docs_text", "")
            raw_images = task_details.get("uploaded_images", [])
            # only ever work with bare filenames:
            uploaded_images = [Path(p).name for p in raw_images]

            if not user_input:
                raise ValueError("User input is missing in task details.")

            self.logger.info(f"Input: {user_input}")
            self.logger.info(f"History: {len(chat_history)} msgs")

            # Step 1: Generate Subtasks and Assignment Criteria Using GPT-4.
            self.logger.info("Generating subtasks...")
            gpt_response = await self.generate_subtasks_and_criteria(
                user_input, chat_history, uploaded_docs_text, uploaded_images
            )
            self.logger.info(f"GPT-4 Resp: {summarize(gpt_response)}")

            # Extract subtasks and criteria from GPT response.
            subtasks = gpt_response.get("subtasks", [])
            criteria = user_input  # Use the user input as the overall criteria.

            # Prepare data for the task status file.
            task_status_data = {
                "criteria": criteria,
                "subtasks": [
                    {
                        "id": subtask["id"],
                        "desc": subtask["desc"],
                        "deps": subtask.get("deps", []),
                        "uploads": subtask.get("uploads", []),
                        "status": subtask["status"],
                    }
                    for subtask in subtasks
                ],
            }

            # Step 2: Generate a unique task ID.
            task_id = self.generate_unique_task_id()

            # Step 3: Initialize the task status using the provided utility function.
            self.logger.info(f"Init task status for ID: {task_id}")
            initialize_task_status(
                chat_id=self.chat_id,
                task_id=task_id,
                subtasks=task_status_data,
            )
            ts_file = get_knowledge_base_dir(self.chat_id) / "task_status.json"
            self.logger.info(f"Status init OK: {ts_file}")

            # Report success.
            self.report_results({"status": "success", "task_file": str(ts_file)})

        except Exception as e:
            self.logger.error(f"Error during task execution: {str(e)}")
            self.handle_error(e)

    async def generate_subtasks_and_criteria(
        self,
        user_input: str,
        chat_history: list,
        uploaded_docs_text: str,
        uploaded_images: list[str],
    ) -> Dict[str, Any]:
        """
        Uses ChatOpenAI (via LangChain) to generate subtasks and assignment criteria.

        This method formats the chat history, defines a detailed prompt template, and generates a JSON-like
        response that includes the subtasks and criteria for the task.

        Args:
            user_input (str): The user-provided task or query.
            chat_history (list): The history of messages for context.

        Returns:
            Dict[str, Any]: A dictionary containing subtasks and assignment criteria.

        Raises:
            json.JSONDecodeError: If the response cannot be parsed as valid JSON.
            Exception: For any unexpected errors during generation.
        """
        try:
            # Format the chat history for inclusion in the prompt.
            history_text = "\n".join(
                [f"{msg['role']}: {msg['content']}" for msg in chat_history]
            )

            examples_text = get_examples_text(user_input, k=3)

            self.logger.info(f"this is the history: {history_text}")

            # ── STEP 0: try multimodal image caching, else fall back to text-only ──
            llm = self.gpt4

            # build a HumanMessage content list
            # start with your normal text instruction
            human_content = [{"type": "text", "text": f"Task: {user_input}"}]

            if uploaded_images:
                human_content.append(
                    {
                        "type": "text",
                        "text": "Available images: " + ", ".join(uploaded_images),
                    }
                )

            # we’ll just inline-embed images; no more cache attempts
            llm = self.gpt4

            prompt = ChatPromptTemplate.from_template(
                """
                **Role:**

                You are a **Task Management Assistant**. Your primary function is to analyze the user's task and conversation history, brainstorm and then generate a detailed plan of action. This includes breaking down the main task into manageable subtasks, identifying dependencies, and specifying assignment criteria to ensure accurate and efficient completion. These subtask will then be given to LLM AI Agents for completion.

                ---

                **Objectives:**

                1. **Comprehend the Task Fully:**
                  - Carefully read the user's task and any conversation history to understand all requirements, including specific numbers or targets.
                  - **Do not add any information or assumptions that are not explicitly mentioned by the user.**

                2. **Generate Accurate Subtasks:**
                  - Decompose the main task into logical and manageable subtasks.
                  - Ensure that the subtasks collectively fulfill the user's overall goal.
                  - When dealing with large numerical targets (e.g., "Find 50 AI researchers"), break them into smaller chunks to maintain accuracy.
                  - **Avoid introducing any subtasks that are not directly derived from the user's instructions.**

                3. **Identify Dependencies:**
                  - Specify dependencies between subtasks to outline sequential or parallel workflows.
                  - Use the `deps` field to indicate which subtasks must be completed before others can begin.
                  - Use the `uploads` field to provide all the user uploaded documents or images the subtask will need if any.
                  - NEVER USE .pdf, .docx, etc extensions. ALWAYS use .md for documents as they are already converted to md for you. For images, use the proper extension.

                4. **Optimize for Accuracy and Efficiency:**
                  - Structure subtasks to minimize errors and avoid overwhelming any single process.
                  - Be mindful of the limitations of LLMs, especially regarding large datasets or extensive lists.

                5. **Define Clear Assignment Criteria:**
                  - Provide specific, measurable criteria for the successful completion of each subtask.
                  - Ensure that criteria align with the user's original requirements, including any quantitative goals.
                  - Include quality standards, such as the reliability of sources or the level of detail needed.
                  - **Do not include criteria that are not explicitly specified by the user.**

                ---
                
                **LLM AI Agents Available:**

                - **You have the following agents available to complete these tasks. You don't have to specify what agent you want to use but keep this information in the back of your mind to create better tasks that can be properly handled in the future:**
                - IMPORTANT: All Agents are MULTIMODAL. They can read and process images and files. All PDF, DOCX, CSV, etc will be converted to md. So, never use .pdf, .docx, etc. Always use upload_1.md (md extension)
                  1) **llm**: a standard language model for typical tasks (e.g., conversation, summarization, writing). Use it when you need to write something.
                  2) **reasoning**: a reasoning-based agent for complex math, logic, and advanced coding tasks.
                  3) **web_scraper**: an agent specialized in scraping the web and conducting research. Don't use when you need to write something. This mainly just fetches information and data from the internet.
                  4) **researcher**: an autonomous agent that performs iterative web searches, extracts and summarizes content from multiple sources, and compiles concise research reports.
                  5) **link_analysis**: an agent that processes links provided in the user message and subtask description.
                  6) **pdf_generator**: an agent that creates PDF documents based on research. Use this agent only when the subtask explicitly involves generating a PDF document. It takes research provided, plans the document like a report, literary review, research paper, article, biography, etc and then using this generated plan, writes the document and then produces the pdf. This means, we don't need to add a subtask to write the document. PDF generator can write the document and generate a pdf out of it. This means, just use pdf generator when creating the document. Never use for research. And give it all the research you can
                  7) **docx_generator**: an agent that creates DOCX documents based on research. Use this agent only when the subtask explicitly involves generating a DOCX document. It takes research provided, plans the document like a report, literary review, research paper, article, biography, etc and then using this generated plan, writes the document and then produces the docx document. This means, we don't need to add a subtask to write the document. DOCX generator can write the document and generate a docx render out of it. This means, just use pdf generator when creating the document. Never use for research. And give it all the research you can
                  8) **slides_generator**: an agent that creates slides PPTX documents based on research. Use this agent only when the subtask explicitly involves generating a slides PPTX document. It takes research provided, plans the document using this generated plan, writes the document and then produces the slides PPTX document. This means, we don't need to add a subtask to write the document. slides generator can write the document and generate a slides PPTX render out of it. This means, just use pdf generator when creating the document. Never use for research. And give it all the research you can.
                  9) **code_interpreter**: an agent that runs short Python snippets in a sandbox and returns stdout and stderr.
                  10) **subtask refiner**: an agent that goes through subtasks you created and breaks them into smaller subtasks if needed given information from other completed subtasks.
                  
                  
                ---

                **Output Format:**

                Please provide your response in the following JSON format:
                
                ```json
                {{
                  "subtasks": [
                    {{
                      "id": "1",
                      "desc": "Describe the first subtask here.",
                      "deps": [],
                      "uploads":[],
                      "status": "pending"
                    }},
                    {{
                      "id": "2",
                      "desc": "Describe the second subtask here.",
                      "deps": ["1"],
                      "uploads":[<name_of_the_image.extension>, <name_of_the_image.extension>],
                      "status": "pending"
                    }}
                  ],
                  "criteria": "Explain any criteria for the successful completion of the overall task."
                }}
                ```
                
                ---

                **Notes:**

                - Replace placeholder text with actual descriptions and criteria based on the user's task.
                - Ensure that the JSON structure is valid and properly formatted.
                - The `status` field should be set to `"pending"` for all subtasks initially.
                - Use the `dependencies` array to list any subtask IDs that must be completed before the current subtask can begin.
                - Don't include a final subtask that combines all results.
                - **Do not include any information or assumptions not explicitly provided by the user.**


                **Examples**
                {examples}

                ---

                # IMPORTANT NOTE TO ALWAYS REMEMBER:
                - Never include the final subtask that collects/combines/summarizes/ all results! This is because it is a manager agent's responsibility to combine all results, not the workers you are assigning tasks to.
                - When writing code like games and sufisticated logics, do think about everything and go a bit in detail. Many games need restart options, scoring, difficulty, and much more. The player should be able to play the game again and again. Every logic needs to be fine. Validation steps should be there to check the logics. This is important. 
                - Always give at least one subtask. Never give no subtasks.
                - Agents will have the user input given to them. Create the subtasks with this information in mind.
                - Efficiency while having detailed subtasks is key. Try to only use dependencies where necessary.
                - Exclusively for math problems, always only have one subtask.
                - Keep independent tasks like Subtask 1 with no dependencies manageable. This means don't make them complicated as they can't be later split into smaller subtasks in the runtime while the other subtasks that have dependencies can.
                - Try to keep dependencies to the minimum.
                - When writing a web or youtube link in the subtask, use the actual case sensitive text in strings. Don't write a short version of the link otherwise it can become a problem as you are providing false information. 
                - Don't use works like design the pdf if you want to create a pdf in the subtask. For pdf creation subtask, only use words like generate a pdf. Design the pdf will initiate a llm to plan and design the pdf, not generate one.
                
                # CONTEXT YOU WILL BE WORKING WITH:
                
                **User Uploaded Documents (if any)**
                {uploaded_docs_text}
                
                ---
                
                **User Uploaded Images (if any)**
                {uploaded_images_list}
                
                ---
                
                ## **Chat History:**
                {chat_history}
                
                ---
                
                ## **Task:**
                {user_input}
                
                ---
                
                OUTPUT: 
                """
            )

            # Create the chain using the defined prompt template and GPT-4 model.
            # pipe into our multimodal-ready llm
            chain = prompt | llm | StrOutputParser()

            # Prepare the input data for the chain.
            input_data = {
                "chat_history": history_text,
                "user_input": user_input,
                "uploaded_docs_text": uploaded_docs_text,
                "uploaded_images_list": "\n".join(uploaded_images),
                "examples": examples_text,
            }

            filled = prompt.format_prompt(**input_data).to_string()
            self.logger.info(f"Prompt with examples: {summarize(filled, 100000)}")
            in_tokens = count_tokens(filled)
            self.log_input_tokens(llm.model, in_tokens)

            # Generate the output asynchronously in chunks.
            chunks = []
            async for chunk in chain.astream(input_data):
                chunks.append(chunk)

            # Combine all chunks into a single string.
            full_response = "".join(chunks)

            out_tokens = count_tokens(full_response)
            self.log_output_tokens(llm.model, out_tokens)

            # Clean and parse the JSON response.
            cleaned_response = full_response.split("```json")[1].split("```")[0].strip()
            response_data = json.loads(cleaned_response)
            self.logger.info(
                f"Parsed GPT-4 Resp: {summarize(json.dumps(response_data))}"
            )

            return response_data

        except json.JSONDecodeError as e:
            self.logger.error(f"Failed to parse JSON response: {str(e)}")
            self.logger.error(f"Raw response: {summarize(chunks)}")
            raise

        except Exception as e:
            self.logger.error(
                f"Unexpected error in generate_subtasks_and_criteria: {str(e)}"
            )
            raise

    def generate_unique_task_id(self) -> str:
        """
        Generates a unique task ID based on the current timestamp.

        Returns:
            str: A unique task ID string in the format 'task_YYYYMMDDHHMMSS'.
        """
        from datetime import datetime

        return f"task_{datetime.utcnow().strftime('%Y%m%d%H%M%S')}"

    def handle_error(self, error: Exception):
        """
        Handles errors during task execution by logging the details.

        Args:
            error (Exception): The exception instance to be handled.
        """
        self.logger.error(f"Error occurred in ManagerBot: {str(error)}")
        # Optionally, an error message could be sent to another component.

    def report_results(self, results: Dict[str, Any]):
        """
        Logs and reports the results of the ManagerBot's execution.

        Args:
            results (Dict[str, Any]): Dictionary containing the results of the task.
        """
        self.logger.info(f"Reporting results: {results}")
