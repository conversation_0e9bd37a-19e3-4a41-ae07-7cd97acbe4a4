# app/agents/aggregator_agent.py

import logging

from langchain_core.output_parsers import StrOutputParser

from app.core.config import settings
from app.utils.task_status import read_task_status
from app.utils.constants import get_knowledge_base_dir
from langchain_google_genai import ChatGoogleGenerativeA<PERSON>
from langchain_core.prompts import ChatPromptTemplate
from app.utils.token_counter import count_tokens
from app.agents.base_agent import BaseAgent

logger = logging.getLogger(__name__)

# -------------------------------------------------------------------------
# 1) Single Gemini chain with max_output_tokens set to the Flash limit
# -------------------------------------------------------------------------
aggregator_model = ChatGoogleGenerativeAI(
    api_key=settings.GOOGLE_API_KEY, model="gemini-2.5-flash"
)

aggregator_prompt = ChatPromptTemplate.from_template(
    """
SYSTEM PROMPT:

You are an Aggregator. Your job is to understand the user's request, understand the research and additional information provided 
and generate a response for what the user wants. You are fun to talk to and give structured outputs.

Under *no circumstances* may you:

- Mention “Subtask” or list internal IDs.
- Prepend or append any header like “Here is the aggregated response.”
- Refer to yourself or the agent.
- Describe your process.


User’s Request:
{overall_criteria}


---

## All Subtask Criteria (contains all the data that was generated by the system dumped here.):
{combined_subtask_data}


---

### User query about the system:
If the user asks about the system, what you are, how the system works:
- SYSTEM NAME: Opus0 (you are a distrbuted general multi agent system)
- HOW THE SYSTEM WORKS: We employ managers and worker agnets to complete tasks in this system. Worker agent we have right now: thinking agent, reasoning agent, search tool, document creation agent. We are going to add many more soon. This is the closed beta to test the architecture right now.
- COMPANY: This product is made by synagi, it uses primarily google and openAI LLMs like 2.5 flash and pro, o3 for workflows. 


### Instruction
Produce a single, well-formatted output that:

- IMPORTANT! Presents any PDF links under a good alt text. Also, any web links, give them in proper alt text with full links.
- Meets the tone implied by the user's request. Give a detailed reponse only if asked. If the task was to create a PDF, give the PDF like and a small breif of what it contains.
- ALWAYS give links to PDF if that was a major task.
- When links present, always give them under a link alt text (usually the main site domain is the alt)
- Give outputs with elements like headings, bullet points, emojis, dividers and more where required but most of the time. Keep things very formatted.


Well Formatted Output:
"""
)

aggregator_chain = aggregator_prompt | aggregator_model | StrOutputParser()

# -------------------------------------------------------------------------
# 2) Pure helper functions
# -------------------------------------------------------------------------
import os
from typing import List, Dict, Any


def read_file(path: str) -> str:
    try:
        with open(path, "r", encoding="utf-8") as f:
            return f.read()
    except Exception as e:
        logger.warning(f"AggregatorAgent: failed to read {path}: {e}")
        return ""


def get_status(chat_id: str) -> Dict:
    return read_task_status(chat_id).get("subtasks", {})


def list_subtasks(status: Dict) -> List[Dict]:
    return status.get("subtasks", [])


def gather_subtask_data(subtasks: List[Dict], kb_path: str) -> str:
    sections = []
    for st in subtasks:
        sid = st.get("id", "unknown")
        desc = st.get("desc", "")
        out = st.get("output_file", "")
        content = ""
        if out:
            md_path = os.path.join(kb_path, os.path.basename(out))
            content = read_file(md_path)
        sections.append(
            f"--- Subtask {sid} ---\n" f"Description: {desc}\n\n" f"{content}"
        )
    return "\n\n".join(sections)


def build_prompt_inputs(chat_id: str) -> Dict[str, str]:
    status = get_status(chat_id)
    criteria = status.get("criteria", "")
    subs = list_subtasks(status)
    kb_path = get_knowledge_base_dir(chat_id)
    combined = gather_subtask_data(subs, kb_path)
    return {"overall_criteria": criteria, "combined_subtask_data": combined}


# -------------------------------------------------------------------------
# 3) AggregatorAgent: single entry-point
# -------------------------------------------------------------------------
class AggregatorAgent(BaseAgent):
    """
    After all subtasks finish, this agent reads *only* their listed output files
    and invokes Gemini 2.0 Flash once to get a merged Markdown response.
    """

    def __init__(self, chat_id: str, agent_id: str = "aggregator_agent"):
        super().__init__(agent_id, agent_type="aggregator")
        self.chat_id = chat_id

    async def execute_task(self, task_details: Dict[str, Any]):
        """Compatibility stub for BaseAgent."""
        self.logger.info(
            "AggregatorAgent.execute_task called; returning final response"
        )
        return self.generate_final_response()

    def generate_final_response(self) -> str:
        inputs = build_prompt_inputs(self.chat_id)

        # 1) render the full filled prompt
        filled = aggregator_prompt.format_prompt(**inputs).to_string()
        in_tokens = count_tokens(filled)
        self.log_input_tokens(aggregator_model.model, in_tokens)

        # 2) invoke the chain
        result: str = aggregator_chain.invoke(inputs)
        result = result.strip()

        # 3) count output tokens
        out_tokens = count_tokens(result)
        self.log_output_tokens(aggregator_model.model, out_tokens)

        return result

    async def generate_final_response_stream(self):
        # If you really want streaming, use astream:
        inputs = build_prompt_inputs(self.chat_id)
        # Count and log input tokens using the filled prompt
        filled = aggregator_prompt.format_prompt(**inputs).to_string()
        in_tokens = count_tokens(filled)
        self.log_input_tokens(aggregator_model.model, in_tokens)

        # Stream the response while accumulating for token counting
        chunks = []
        async for chunk in aggregator_chain.astream(inputs):
            chunks.append(chunk)
            yield chunk

        result = "".join(chunks).strip()

        # Count and log output tokens once complete
        out_tokens = count_tokens(result)
        self.log_output_tokens(aggregator_model.model, out_tokens)
