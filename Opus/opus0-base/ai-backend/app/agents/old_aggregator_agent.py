import os
import logging
import json
from typing import Optional, AsyncGenerator
import httpx  # For streaming usage

from openai import APIError
from langchain_openai import Chat<PERSON>penA<PERSON>
from langchain_google_genai import ChatGoogleGenerative<PERSON><PERSON>
from langchain_core.prompts import Chat<PERSON>romptTemplate
from langchain_core.output_parsers import StrOutputParser

from app.core.config import settings
from app.utils.task_status import read_task_status

logger = logging.getLogger(__name__)

# -------------------------------------------------------------------------
# 1) The aggregator ChatOpenAI + ChatPromptTemplate + StrOutputParser chain.
# -------------------------------------------------------------------------
aggregator_model = ChatGoogleGenerativeAI(
    api_key=settings.GOOGLE_API_KEY,
    model="gemini-2.0-flash",
    temperature=0.7
)

aggregator_prompt = ChatPromptTemplate.from_template("""
You are an Aggregator Agent. Your role is to:
 - Combine the user’s overall request and multiple subtask results
 - Provide a single output
 - Incorporate all details from the subtask `.md` files, merging them cohesively

User's Overall Criteria:
{overall_criteria}

Subtask Data:
{combined_subtask_data}

### Instruction:
Given the user request and subtask outputs above, produce a final, aggregated response.
Use all the context from the subtask data.

### The structure and tone will be according to the User's Overall Criteria. If it is normal, do normal. If it's research, be thorough. 
Response:
""")

aggregator_chain = aggregator_prompt | aggregator_model | StrOutputParser()


class AggregatorAgent:
    """
    Standalone aggregator agent that runs after all subtasks are completed.

    - If a subtask’s .md file is flagged as a "math solution" 
      (by containing a special marker or recognized string),
      then we skip aggregator GPT and return that math solution.
    - Otherwise, we produce an aggregated output (both sync and streamed).
    """

    def __init__(self, model: str = "o4-mini"):
        logger.info("AggregatorAgent: Initializing with model=%s", model)
        self.model = model

        aggregator_dir = os.path.dirname(__file__)
        self.knowledge_base_path = os.path.abspath(
            os.path.join(aggregator_dir, "..", "knowledge_base")
        )
        logger.info("AggregatorAgent: knowledge_base_path resolved to %s", self.knowledge_base_path)

    def generate_final_response(self) -> str:
        """
        Non-streamed aggregator method:
          1) Read subtask outputs, look for math solutions
          2) If found => Return them directly
          3) Else => aggregator LLM merges everything
        """
        logger.info("AggregatorAgent: Starting generate_final_response()")
        task_status = read_task_status()
        logger.debug("AggregatorAgent: Raw task_status => %s", task_status)

        subtasks_info = task_status.get("subtasks", {})
        overall_criteria = subtasks_info.get("criteria", "No user instructions found.")
        subtask_list = subtasks_info.get("subtasks", [])

        logger.info("AggregatorAgent: Found %d subtasks", len(subtask_list))

        math_solution_content = self._find_math_solutions(subtask_list)
        if math_solution_content:
            logger.info("AggregatorAgent: Math solution detected. Returning it directly.")
            return math_solution_content

        combined_subtask_data = self._gather_subtask_contents(subtask_list)
        input_data = {
            "overall_criteria": overall_criteria,
            "combined_subtask_data": combined_subtask_data
        }

        try:
            logger.info("AggregatorAgent: Calling aggregator_chain (one-shot).")
            final_text = aggregator_chain(input_data)
            return final_text.strip()
        except APIError as e:
            logger.error("AggregatorAgent: APIError => %s", str(e))
            return "Error: Unable to generate final aggregated response."
        except Exception as e:
            logger.error("AggregatorAgent: Unexpected error => %s", str(e))
            return "Error: Unable to generate final aggregated response."

    async def generate_final_response_stream(self) -> AsyncGenerator[str, None]:
        """
        Streamed aggregator method:
          1) If any subtask is math, yield that solution in one chunk.
          2) Otherwise, we stream from GPT.
        """
        logger.info("AggregatorAgent: Starting generate_final_response_stream()")
        task_status = read_task_status()
        logger.debug("AggregatorAgent: Raw task_status => %s", task_status)

        subtasks_info = task_status.get("subtasks", {})
        overall_criteria = subtasks_info.get("criteria", "No user instructions found.")
        subtask_list = subtasks_info.get("subtasks", [])

        logger.info("AggregatorAgent: Found %d subtasks", len(subtask_list))

        math_solution_content = self._find_math_solutions(subtask_list)
        if math_solution_content:
            logger.info("AggregatorAgent: Math solution detected. Streaming it.")
            yield math_solution_content
            return

        combined_subtask_data = self._gather_subtask_contents(subtask_list)
        aggregator_prompt_text = aggregator_prompt.format(
            overall_criteria=overall_criteria,
            combined_subtask_data=combined_subtask_data
        )

        headers = {
            "Authorization": f"Bearer {settings.OPENAI_API_KEY}",
            "Content-Type": "application/json",
        }
        payload = {
            "model": self.model,
            "messages": [{"role": "user", "content": aggregator_prompt_text}],
            "stream": True
        }

        try:
            logger.info("AggregatorAgent: Sending streaming request to OpenAI.")
            async with httpx.AsyncClient(timeout=None) as client:
                async with client.stream("POST", "https://api.openai.com/v1/chat/completions", headers=headers, json=payload) as r:
                    r.raise_for_status()
                    async for line in r.aiter_lines():
                        if not line:
                            continue
                        if line.startswith("data:"):
                            data = line[len("data:"):].strip()
                            if data == "[DONE]":
                                break
                            try:
                                json_data = json.loads(data)
                                if "choices" in json_data and len(json_data["choices"]) > 0:
                                    delta = json_data["choices"][0].get("delta", {})
                                    if "content" in delta:
                                        yield delta["content"]
                            except json.JSONDecodeError:
                                logger.warning("AggregatorAgent: JSON decode error in streaming chunk => %s", line)
                                yield "[ERROR] Malformed streaming data.\n"
        except APIError as e:
            logger.error("AggregatorAgent: APIError => %s", str(e))
            yield "[ERROR] Unable to generate aggregator response (APIError)\n"
        except Exception as e:
            logger.error("AggregatorAgent: Unexpected error => %s", str(e))
            yield "[ERROR] Something went wrong while streaming aggregator output.\n"

    def _gather_subtask_contents(self, subtask_list: list) -> str:
        """
        Reads each subtask’s .md file and merges them into one big string.
        We'll feed that into aggregator_prompt as 'combined_subtask_data'.
        """
        lines = []
        for st in subtask_list:
            st_id = st.get("id", "unknown")
            st_desc = st.get("desc", "No subtask description.")
            out_file = st.get("output_file")

            lines.append(f"--- Subtask {st_id} ---")
            lines.append(f"Description: {st_desc}")

            if out_file:
                md_path = os.path.join(self.knowledge_base_path, os.path.basename(out_file))
                if os.path.isfile(md_path):
                    try:
                        with open(md_path, "r", encoding="utf-8") as f:
                            md_content = f.read()
                        lines.append(f"Subtask Output:\n{md_content}")
                    except Exception as ex:
                        logger.error("AggregatorAgent: Error reading file => %s", str(ex))
                        lines.append("Error reading subtask’s .md file content.")
                else:
                    lines.append(f"No .md file found => {md_path}")
            else:
                lines.append("No output_file for this subtask.")

        return "\n".join(lines)

    def _find_math_solutions(self, subtask_list: list) -> Optional[str]:
        """
        Checks if any subtask .md file includes a clear 'math solution' marker or reasoner output.
        If found, returns that file content (as a single big string).
        Otherwise, return None.
        """
        math_lines = []
        for st in subtask_list:
            out_file = st.get("output_file")
            if not out_file:
                continue

            md_path = os.path.join(self.knowledge_base_path, os.path.basename(out_file))
            if os.path.isfile(md_path):
                try:
                    with open(md_path, "r", encoding="utf-8") as f:
                        content = f.read()
                    if "MATH SOLUTION" in content.upper():
                        math_lines.append(f"-----\nFile: {os.path.basename(out_file)}\n{content}")
                except Exception as ex:
                    logger.error("AggregatorAgent: Error reading potential math solution => %s", str(ex))
                    continue

        if math_lines:
            return "\n\n".join(math_lines)
        return None
