# app/agents/researcher_agent.py

import os
import logging
from datetime import datetime
from typing import Dict, Any
from gpt_researcher import <PERSON><PERSON><PERSON><PERSON><PERSON>
from app.agents.base_agent import BaseAgent
from app.utils.constants import get_knowledge_base_dir
from app.utils.communication import send_message

# Initialize logging
logger = logging.getLogger(__name__)

class ResearcherAgent(BaseAgent):
    """
    Researcher Agent that uses GPTResearcher to gather concise information and source links.
    Inherits from BaseAgent and implements task-specific logic.
    """

    def __init__(self, agent_id: str, chat_id: str):
        super().__init__(agent_id, agent_type="worker")
        self.chat_id = chat_id

    async def execute_task(self, task_details: Dict[str, Any]):
        """
        Executes the given research task. Gathers both a concise report and source links,
        then saves them to a Markdown (.md) file and reports the results to the manager.

        Expected task_details structure:
        {
            "subtask_id": "1",
            "subtask_description": "Identify reliable news sources that provide updates on current events happening today.",
            "user_message": "Find me the news from today",
            "parameters": {
                "report_type": "research_report",
                "tone": "concise",
                "max_subtopics": 3
            }
        }
        """
        try:
            
            
            # Step 1: Extract task details
            subtask_id = task_details.get("subtask_id", "no_subtask_id")
            subtask_description = task_details.get("subtask_description", "")
            user_query = task_details.get("user_message", "No query provided")

            # Optional parameters with defaults
            params = task_details.get("parameters", {})
            report_type = params.get("report_type", "research_report")  # Changed default to "research_report"
            tone = params.get("tone", "concise")
            max_subtopics = params.get("max_subtopics", 3)

            # Log the incoming request
            logger.info(f"[ResearcherAgent] Received subtask {subtask_id}")
            logger.info(f"[ResearcherAgent] Query: {user_query}, Report Type: {report_type}, Tone: {tone}, Max Subtopics: {max_subtopics}")

            # Step 2: Initialize GPTResearcher
            researcher = GPTResearcher(
                query=user_query,
                report_type=report_type,
                tone=tone,
                max_subtopics=max_subtopics
            )

            # Step 3: Conduct the research asynchronously
            logger.info("[ResearcherAgent] Starting GPTResearcher.conduct_research()")
            await researcher.conduct_research()

            # Step 4: Gather data
            logger.info("[ResearcherAgent] Gathering source URLs and costs")
            source_urls = researcher.get_source_urls()          # List of source URLs
            research_costs = researcher.get_costs()             # Token usage or cost
            research_images = researcher.get_research_images()  # List of image URLs or data

            # Optional: Generate a concise report
            logger.info("[ResearcherAgent] Generating research report")
            research_report = await researcher.write_report()

            # Step 5: Prepare Markdown output with report and links
            output_file_path = self.save_research_results_md(
                subtask_id=subtask_id,
                user_query=user_query,
                research_report=research_report,
                source_urls=source_urls,
                costs=research_costs,
                number_of_images=len(research_images)
            )

            # Step 6: Report completion to the manager
            self.report_results({
                "subtask_id": subtask_id,
                "output_file": output_file_path
            })

        except Exception as e:
            logger.error(f"[ResearcherAgent] Error during task execution: {str(e)}")
            self.handle_error(e)

    def save_research_results_md(self,
                                 subtask_id: str,
                                 user_query: str,
                                 research_report: str,
                                 source_urls: list,
                                 costs: float,
                                 number_of_images: int) -> str:
        """
        Saves the research report and source links to a Markdown (.md) file in the knowledge_base folder.

        :param subtask_id: Subtask ID.
        :param user_query: The user's research query.
        :param research_report: Generated research report.
        :param source_urls: List of source URLs obtained from GPTResearcher.
        :param costs: Token usage or cost associated with the research.
        :param number_of_images: Number of images retrieved during research.
        :return: Path to the saved Markdown file.
        """
        timestamp = datetime.utcnow().strftime('%Y%m%d%H%M%S')
        file_name = f"researcher_{subtask_id}_{timestamp}.md"  # Removed task_id and added timestamp for uniqueness
        kb_dir = get_knowledge_base_dir(self.chat_id)
        output_file_path = os.path.join(kb_dir, file_name)

        # Ensure the knowledge base directory exists
        os.makedirs(kb_dir, exist_ok=True)

        # Create Markdown content
        md_content = f"# Research Summary\n\n"
        md_content += f"**User Query**: {user_query}\n\n"
        md_content += f"## Research Report\n\n{research_report}\n\n"
        md_content += f"## Source URLs\n\n"
        for url in source_urls:
            md_content += f"- [{url}]({url})\n"
        md_content += f"\n**Research Costs (Tokens)**: {costs}\n\n"
        md_content += f"**Number of Sources**: {len(source_urls)}\n\n"
        md_content += f"**Number of Images**: {number_of_images}\n\n"
        md_content += f"**Notes**: Minimal data focusing on links and costs.\n"

        # Save the Markdown content to a file
        with open(output_file_path, "w", encoding="utf-8") as file:
            file.write(md_content)

        logger.info(f"[ResearcherAgent] Saved research data to {output_file_path}")
        return output_file_path

    def report_results(self, results: Dict[str, Any]):
        """
        Reports the results of the subtask execution to the manager agent.

        Expected results structure:
        {
            "subtask_id": "1",
            "output_file": "/path/to/researcher_1_20250107080940.md"
        }
        """
        subtask_id = results["subtask_id"]
        output_file = results["output_file"]

        message = {
            "subtask_id": subtask_id,
            "output_file": output_file
        }

        # Send the message to the manager
        send_message(
            self.chat_id,
            sender="worker",
            receiver="manager",
            message=message,
        )
        logger.info(f"[ResearcherAgent] Reported results for subtask {subtask_id}: {output_file}")
