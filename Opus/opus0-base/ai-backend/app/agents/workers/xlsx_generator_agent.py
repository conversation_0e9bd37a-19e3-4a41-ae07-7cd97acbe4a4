# app/agents/workers/xlsx_generator_agent.py
"""
Module: xlsx_generator_agent.py

This module defines the XLSXGeneratorAgent class, a worker agent that generates Excel (.xlsx) documents from LLM-generated JSON data.
It follows the same execution pattern as PDFGeneratorAgent and DOCXGeneratorAgent: generate outline, generate data, create Excel file,
and upload to R2 with presigned download links.
"""

import logging
import json
import re
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional
from pathlib import Path

import openpyxl
from openpyxl.styles import Font, PatternFill, Border, Side
from openpyxl.utils import get_column_letter

from app.agents.base_agent import BaseAgent
from app.core.config import settings
from app.utils.constants import (
    get_knowledge_base_dir,
    get_public_xlsx_dir,
)
from app.utils.communication import send_message
from app.utils.token_counter import count_tokens
from app.utils.example_rag import get_xlsx_examples_text, get_xlsx_section_examples_text

from langchain_google_genai import ChatGoogleGenerativeA<PERSON>
from langchain_core.prompts import Chat<PERSON>rom<PERSON><PERSON><PERSON>plate
from langchain_core.output_parsers import StrOutputParser


# Initialize module-level logger
logger = logging.getLogger(__name__)


#──────────────────────────────────────────────────
#                         LANGCHAIN MODELS & PROMPTS
#──────────────────────────────────────────────────


xlsx_outline_model = ChatGoogleGenerativeAI(
    api_key=settings.GOOGLE_API_KEY,
    model="gemini-2.5-flash-preview-04-17",
    temperature=0.0,
)

xlsx_outline_prompt = ChatPromptTemplate.from_template(
    """
## Role
You are an expert Excel analyst and data visualization specialist. Generate a comprehensive Excel workbook outline based on the user's requirements.

## Objective
Create a detailed JSON structure that defines the Excel workbook layout, including worksheets, data organization, and formatting specifications.

## Output Format
Return a valid JSON object with this exact structure:

```json
{{
  "title": "Workbook Title",
  "worksheets": [
    {{
      "name": "Sheet Name",
      "purpose": "Brief description of this worksheet's purpose",
      "charts": [
        {{
          "type": "bar|line|pie",
          "title": "Chart Title",
          "purpose": "Why this chart is useful for the data",
          "position": "D2",
          "style": 10,
          "options": {{
            "width": 15,
            "height": 10,
            "grouping": "clustered|stacked|percentStacked",
            "show_legend": true,
            "x_axis_title": "X Axis Label",
            "y_axis_title": "Y Axis Label"
          }}
        }}
      ]
    }}
  ],
  "formatting": {{
    "currency_format": "$#,##0.00",
    "percentage_format": "0.0%",
    "date_format": "mm/dd/yyyy",
    "header_style": {{
      "bold": true,
      "background_color": "CCCCCC",
      "font_color": "000000"
    }}
  }}
}}
```

## Chart Guidelines
- Use bar charts for comparisons between categories
- Use line charts for trends over time
- Use pie charts for parts of a whole (max 6-8 slices)
- Position charts to avoid overlapping with data tables
- Include meaningful titles and axis labels
- Consider chart grouping: clustered (side-by-side), stacked, or percentStacked

## Guidelines
- Create 1-4 worksheets maximum for clarity
- Use descriptive, professional worksheet names (max 31 characters)
- Each worksheet should have a clear, distinct purpose
- Consider data relationships between worksheets
- Include appropriate formatting specifications
- Add charts when data would benefit from visualization

## Examples
{examples}

## Context
**Subtask:** {subtask_desc}
**User Request:** {user_input}
**Additional Info:** {deps_info}

## Output
Generate the Excel outline JSON now:
"""
)

xlsx_outline_chain = xlsx_outline_prompt | xlsx_outline_model | StrOutputParser()


xlsx_data_model = ChatGoogleGenerativeAI(
    api_key=settings.GOOGLE_API_KEY,
    model="gemini-2.5-flash-preview-04-17",
    temperature=0.3,
)

xlsx_data_prompt = ChatPromptTemplate.from_template(
    """
## Role
You are an expert Excel data analyst. Generate complete worksheet data in JSON format based on the outline and user requirements.

## Objective
Create detailed JSON data for each worksheet, including headers, data rows, formulas, cell-specific formatting, and chart specifications.

## Output Format
Return a valid JSON object with this exact structure:

```json
{{
  "worksheets": [
    {{
      "name": "Sheet Name",
      "tables": [
        {{
          "headers": [
            {{"value": "Header 1", "data_type": "text"}},
            {{"value": "Header 2", "data_type": "currency"}}
          ],
          "rows": [
            [
              {{"value": "Data 1", "data_type": "text"}},
              {{"value": 1000, "data_type": "currency"}}
            ]
          ]
        }}
      ],
      "charts": [
        {{
          "type": "bar",
          "title": "Chart Title",
          "data_range": {{
            "categories": "A2:A5",
            "values": "B2:B5"
          }},
          "position": "D2",
          "style": 10,
          "options": {{
            "width": 15,
            "height": 10,
            "grouping": "clustered",
            "show_legend": true,
            "x_axis_title": "Categories",
            "y_axis_title": "Values"
          }}
        }}
      ]
    }}
  ]
}}
```

## Data Types
- "text": Plain text values
- "number": Numeric values
- "currency": Monetary values (will be formatted as currency)
- "percentage": Percentage values (will be formatted as %)
- "date": Date values
- "formula": Excel formulas (start with =)

## Chart Guidelines
- Specify data_range with exact cell references (e.g., "A2:A5" for categories, "B2:B5" for values)
- Ensure chart data ranges match the actual table data
- Position charts to avoid overlapping with tables (use columns D+ typically)
- Use meaningful chart titles and axis labels
- Choose appropriate chart types: bar for comparisons, line for trends, pie for parts of whole

## Guidelines
- Generate realistic, relevant data based on the user's request
- Use appropriate data types for each cell
- Include formulas for calculations (SUM, AVERAGE, etc.)
- Keep data sets manageable (10-50 rows typically)
- Ensure data consistency across related worksheets
- Include charts when specified in the outline

## Context
**Outline:** {outline}
**Subtask:** {subtask_desc}
**User Request:** {user_input}
**Additional Info:** {deps_info}

## Examples
{section_examples}

## Output
Generate the complete Excel data JSON now:
"""
)

xlsx_data_chain = xlsx_data_prompt | xlsx_data_model | StrOutputParser()


#──────────────────────────────────────────────────
#                         CLASS DEFINITION
#──────────────────────────────────────────────────


class XLSXGeneratorAgent(BaseAgent):
    """
    Worker agent capable of generating Excel (.xlsx) documents from JSON data.

    Inherits from BaseAgent and implements task-specific logic to:
      1. Parse the task details (subtask_id, subtask_description, user_message, deps_info).
      2. Generate an Excel outline based on the subtask details (using GPT).
      3. Save that exact JSON outline in a .json file.
      4. Generate complete Excel data in JSON format.
      5. Create Excel file using openpyxl with proper formatting.
      6. Upload Excel file to R2 and generate presigned download link.
      7. Report completion to the manager.
    """

    def __init__(self, agent_id: str, chat_id: str):
        """
        Initializes the XLSXGeneratorAgent with a unique agent ID.

        Args:
            agent_id (str): Unique identifier for the Excel generator agent.
            chat_id (str): Chat session identifier.
        """
        super().__init__(agent_id, agent_type="Document Generator Tool")
        self.chat_id = chat_id

    async def _invoke_chain(
        self, chain: Any, payload: Dict[str, Any], *, stream: bool = True
    ) -> str:
        """Run ``chain`` with ``payload`` and return the combined result.

        When ``stream`` is ``True`` runtime log events are emitted for each
        chunk received from ``chain``.
        """
        logger.info(
            "[xlsx_generator] LLM input payload:\n%s", json.dumps(payload, indent=2)
        )

        chunks: List[str] = []
        if stream:
            self.runtime_log_stream("stream")

        async for chunk in chain.astream(payload):
            text = chunk if isinstance(chunk, str) else str(chunk)
            if stream:
                self.runtime_log_stream(text)
            chunks.append(text)

        if stream:
            self.runtime_log_stream("stream")

        return "".join(chunks).strip()

    async def execute_task(self, task_details: Dict[str, Any]):
        """
        Steps:
        1) Generate an Excel outline in JSON.
        2) Save it to a .json file.
        3) Generate complete Excel data in JSON format.
        4) Save Excel data to another .json file.
        5) Create Excel file using openpyxl.
        6) Upload Excel file to R2 and generate presigned download link.
        7) Report completion to the manager.
        """
        try:
            self.start_timer("xlsx agent")
            
            # Extract task details
            subtask_id = task_details.get("subtask_id", "unknown")
            subtask_description = task_details.get("subtask_description", "")
            user_input = task_details.get("user_message", "")
            deps_info = task_details.get("deps_info", "")

            # Store for fallback use
            self.deps_info = deps_info

            self.logger.info(f"[XLSXGenerator] Starting Excel generation for subtask: {subtask_id}")

            # ----------------------------
            # (1) Generate & Save Outline
            # ----------------------------
            self.runtime_log("Generating Outline")
            outline_plan = await self.determine_xlsx_outline(
                subtask_desc=subtask_description,
                user_input=user_input,
                deps_info=deps_info,
            )
            
            # Store full outline text for later data prompts
            self.full_outline_text = json.dumps(
                outline_plan, ensure_ascii=False, indent=2
            )
            self.logger.info("[XLSXGenerator] Outline generated successfully")
            self.save_outline_to_json_file(subtask_id, outline_plan)

            # ----------------------------
            # (2) Generate Excel Data
            # ----------------------------
            self.runtime_log("Generating Data")
            excel_data = await self.generate_xlsx_data(
                outline_plan=outline_plan,
                subtask_desc=subtask_description,
                user_input=user_input,
                deps_info=deps_info,
            )
            
            self.logger.info("[XLSXGenerator] Excel data generated successfully")
            self.save_data_to_json_file(subtask_id, excel_data)

            # ----------------------------
            # (3) Create Excel File
            # ----------------------------
            self.runtime_log("Creating Excel File")
            timestamp = datetime.now(timezone.utc).strftime("%Y%m%d%H%M%S")
            xlsx_name = f"xlsx_result_{subtask_id}_{timestamp}.xlsx"
            xlsx_dir = get_public_xlsx_dir(self.chat_id)
            xlsx_dir.mkdir(parents=True, exist_ok=True)
            xlsx_path = xlsx_dir / xlsx_name

            # Create Excel file from JSON data
            self.create_xlsx_from_json(excel_data, outline_plan, xlsx_path)
            self.logger.info(f"[XLSXGenerator] Excel creation completed: {xlsx_path}")

            # ----------------------------
            # (4) Upload to R2 and Report
            # ----------------------------
            self.runtime_log("Uploading Document")
            from app.utils.r2_client import upload_file_to_r2

            xlsx_url = upload_file_to_r2(
                xlsx_path,
                content_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                expires_in=60 * 60 * 24,  # valid for 24 h
            )

            doc_url = f"{settings.BASE_URL}/docs/{xlsx_path.name}"
            self.runtime_log(f"Generated XLSX: {doc_url}")

            # ----------------------------
            # (5) Create Final Report
            # ----------------------------
            combined_json = {
                "title": "Your Excel Report is Ready",
                "download_url": xlsx_url,
                "local_url": doc_url,
                "outline": outline_plan,
                "data": excel_data,
                "message": f"You can always download your Excel file at {doc_url}",
                "important": "Always give the xlsx url link to the user in your final message."
            }
            
            combined_file_path = self.save_combined_report_to_json_file(
                subtask_id, combined_json
            )

            # ----------------------------
            # (6) Report Results
            # ----------------------------
            self.stop_timer("xlsx agent")
            self.report_results({
                "subtask_id": subtask_id,
                "output_file": str(combined_file_path),
            })

        except Exception as e:
            self.logger.error(f"[XLSXGenerator] Error in execute_task: {e}", exc_info=True)
            self.runtime_log(f"Error: {str(e)}")
            raise

    #──────────────────────────────────────────────────
    #                         OUTLINE GENERATION
    #──────────────────────────────────────────────────

    async def determine_xlsx_outline(
        self, subtask_desc: str, user_input: str, deps_info: str
    ) -> Dict[str, Any]:
        """
        Asks GPT to produce a JSON outline for Excel generation. If it fails, we fallback to a default.
        """

        # Get relevant examples from RAG system
        examples_text = get_xlsx_examples_text(user_input, k=3)

        input_data = {
            "subtask_desc": subtask_desc,
            "user_input": user_input,
            "deps_info": deps_info,
            "examples": examples_text
        }

        try:
            raw_outline = await self._invoke_chain(xlsx_outline_chain, input_data, stream=False)

            # Try to parse JSON from the response
            outline_plan = self._extract_json_from_response(raw_outline)

            if not outline_plan:
                raise ValueError("Could not extract valid JSON from outline response")

            # Validate required fields
            if not isinstance(outline_plan, dict) or "title" not in outline_plan or "worksheets" not in outline_plan:
                raise ValueError("Invalid outline structure")

            self.logger.info("[XLSXGenerator] Successfully generated outline")
            return outline_plan

        except Exception as e:
            self.logger.warning(f"[XLSXGenerator] Outline generation failed: {e}, using fallback")
            return self._get_fallback_outline(user_input)

    def _extract_json_from_response(self, response: str) -> Optional[Dict[str, Any]]:
        """Extract JSON from LLM response, handling code blocks and other formatting."""
        try:
            # First try direct parsing
            return json.loads(response.strip())
        except json.JSONDecodeError:
            pass

        # Try to find JSON in code blocks
        json_patterns = [
            r'```json\s*(\{.*?\})\s*```',
            r'```\s*(\{.*?\})\s*```',
            r'(\{.*?\})',
        ]

        for pattern in json_patterns:
            matches = re.findall(pattern, response, re.DOTALL)
            for match in matches:
                try:
                    return json.loads(match.strip())
                except json.JSONDecodeError:
                    continue

        return None

    def _get_fallback_outline(self, user_input: str) -> Dict[str, Any]:
        """Generate a simple fallback outline when LLM generation fails."""
        return {
            "title": "Data Report",
            "worksheets": [
                {
                    "name": "Data",
                    "purpose": "Main data worksheet"
                },
                {
                    "name": "Summary",
                    "purpose": "Summary and analysis"
                }
            ],
            "formatting": {
                "currency_format": "$#,##0.00",
                "percentage_format": "0.0%",
                "date_format": "mm/dd/yyyy",
                "header_style": {
                    "bold": True,
                    "background_color": "CCCCCC",
                    "font_color": "000000"
                }
            }
        }

    #──────────────────────────────────────────────────
    #                         DATA GENERATION
    #──────────────────────────────────────────────────

    async def generate_xlsx_data(
        self, outline_plan: Dict[str, Any], subtask_desc: str, user_input: str, deps_info: str
    ) -> Dict[str, Any]:
        """
        Generate complete Excel data based on the outline plan.
        """

        # Get relevant section examples from RAG system
        section_examples_text = get_xlsx_section_examples_text(
            f"{user_input} {subtask_desc}", k=3
        )

        input_data = {
            "outline": self.full_outline_text,
            "subtask_desc": subtask_desc,
            "user_input": user_input,
            "deps_info": deps_info,
            "section_examples": section_examples_text,
        }

        try:
            raw_data = await self._invoke_chain(xlsx_data_chain, input_data, stream=False)

            # Try to parse JSON from the response
            excel_data = self._extract_json_from_response(raw_data)

            if not excel_data:
                raise ValueError("Could not extract valid JSON from data response")

            # Validate required fields
            if not isinstance(excel_data, dict) or "worksheets" not in excel_data:
                raise ValueError("Invalid data structure")

            self.logger.info("[XLSXGenerator] Successfully generated Excel data")
            return excel_data

        except Exception as e:
            self.logger.warning(f"[XLSXGenerator] Data generation failed: {e}, using fallback")
            return self._get_fallback_data(outline_plan)

    def _get_fallback_data(self, outline_plan: Dict[str, Any]) -> Dict[str, Any]:
        """Generate fallback data when LLM generation fails, using deps_info if available."""
        worksheets = []

        # Try to extract data from deps_info if available
        deps_data = getattr(self, 'deps_info', '')

        for ws_plan in outline_plan.get("worksheets", []):
            worksheet_name = ws_plan["name"]

            # Create appropriate fallback data based on worksheet name
            if "sales" in worksheet_name.lower() or "data" in worksheet_name.lower():
                worksheet_data = {
                    "name": worksheet_name,
                    "tables": [
                        {
                            "headers": [
                                {"value": "Quarter", "data_type": "text"},
                                {"value": "Product Line", "data_type": "text"},
                                {"value": "Revenue", "data_type": "currency"},
                                {"value": "Growth %", "data_type": "formula"}
                            ],
                            "rows": [
                                [
                                    {"value": "Q1 2024", "data_type": "text"},
                                    {"value": "Software", "data_type": "text"},
                                    {"value": 1500000, "data_type": "currency"},
                                    {"value": "=C2/C2", "data_type": "formula"}
                                ],
                                [
                                    {"value": "Q1 2024", "data_type": "text"},
                                    {"value": "Hardware", "data_type": "text"},
                                    {"value": 800000, "data_type": "currency"},
                                    {"value": "=C3/C3", "data_type": "formula"}
                                ],
                                [
                                    {"value": "Q1 2024", "data_type": "text"},
                                    {"value": "Services", "data_type": "text"},
                                    {"value": 500000, "data_type": "currency"},
                                    {"value": "=C4/C4", "data_type": "formula"}
                                ]
                            ]
                        }
                    ],
                    "charts": [
                        {
                            "type": "bar",
                            "title": "Revenue by Product Line",
                            "data_range": {
                                "categories": "B2:B4",
                                "values": "C2:C4"
                            },
                            "position": "E2",
                            "style": 10,
                            "options": {
                                "width": 15,
                                "height": 10,
                                "grouping": "clustered",
                                "show_legend": True,
                                "x_axis_title": "Product Line",
                                "y_axis_title": "Revenue"
                            }
                        }
                    ]
                }
            elif "expense" in worksheet_name.lower():
                worksheet_data = {
                    "name": worksheet_name,
                    "tables": [
                        {
                            "headers": [
                                {"value": "Month", "data_type": "text"},
                                {"value": "Department", "data_type": "text"},
                                {"value": "Amount", "data_type": "currency"}
                            ],
                            "rows": [
                                [
                                    {"value": "Jan-24", "data_type": "text"},
                                    {"value": "Marketing", "data_type": "text"},
                                    {"value": 150000, "data_type": "currency"}
                                ],
                                [
                                    {"value": "Jan-24", "data_type": "text"},
                                    {"value": "R&D", "data_type": "text"},
                                    {"value": 220000, "data_type": "currency"}
                                ]
                            ]
                        }
                    ]
                }
            elif "summary" in worksheet_name.lower() or "executive" in worksheet_name.lower():
                worksheet_data = {
                    "name": worksheet_name,
                    "tables": [
                        {
                            "headers": [
                                {"value": "Metric", "data_type": "text"},
                                {"value": "Value", "data_type": "mixed"}
                            ],
                            "rows": [
                                [
                                    {"value": "Total Revenue", "data_type": "text"},
                                    {"value": "=SUM(Data!C:C)", "data_type": "formula"}
                                ],
                                [
                                    {"value": "Growth Rate", "data_type": "text"},
                                    {"value": 0.15, "data_type": "percentage"}
                                ]
                            ]
                        }
                    ]
                }
            else:
                # Generic fallback
                worksheet_data = {
                    "name": worksheet_name,
                    "tables": [
                        {
                            "headers": [
                                {"value": "Item", "data_type": "text"},
                                {"value": "Value", "data_type": "number"},
                                {"value": "Total", "data_type": "formula"}
                            ],
                            "rows": [
                                [
                                    {"value": "Sample Data 1", "data_type": "text"},
                                    {"value": 100, "data_type": "number"},
                                    {"value": "=B2*1", "data_type": "formula"}
                                ],
                                [
                                    {"value": "Sample Data 2", "data_type": "text"},
                                    {"value": 200, "data_type": "number"},
                                    {"value": "=B3*1", "data_type": "formula"}
                                ]
                            ]
                        }
                    ]
                }

            worksheets.append(worksheet_data)

        return {"worksheets": worksheets}

    def _validate_chart_data(self, chart_data: Dict[str, Any], worksheet_data: Dict[str, Any]) -> bool:
        """
        Validate chart specifications against available data.
        """
        from app.utils.xlsx_chart_handler import validate_chart_data
        return validate_chart_data(chart_data, worksheet_data, self.logger)

    def _resolve_chart_position_conflicts(self, charts: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Resolve chart position conflicts by adjusting positions when charts overlap.
        """
        from app.utils.xlsx_chart_handler import resolve_chart_position_conflicts
        return resolve_chart_position_conflicts(charts, self.logger)

    #──────────────────────────────────────────────────
    #                         EXCEL FILE CREATION
    #──────────────────────────────────────────────────

    def create_xlsx_from_json(
        self, excel_data: Dict[str, Any], outline_plan: Dict[str, Any], xlsx_path: Path
    ):
        """
        Create an Excel file from JSON data using openpyxl.
        """
        try:
            # Create new workbook
            workbook = openpyxl.Workbook()

            # Remove default sheet
            if workbook.worksheets:
                workbook.remove(workbook.active)

            # Get formatting configuration
            formatting_config = outline_plan.get("formatting", {})

            # Create worksheets
            for worksheet_data in excel_data.get("worksheets", []):
                self._create_excel_worksheet(workbook, worksheet_data, formatting_config)

            # Save workbook
            workbook.save(xlsx_path)
            self.logger.info(f"[XLSXGenerator] Excel file saved successfully: {xlsx_path}")

        except Exception as e:
            self.logger.error(f"[XLSXGenerator] Error creating Excel file: {e}", exc_info=True)
            raise

    def _create_excel_worksheet(
        self, workbook: openpyxl.Workbook, worksheet_data: Dict[str, Any], formatting_config: Dict[str, Any]
    ):
        """
        Create a single worksheet with data and formatting.
        """
        worksheet_name = worksheet_data.get("name", "Sheet1")

        # Create worksheet (truncate name if too long)
        if len(worksheet_name) > 31:
            worksheet_name = worksheet_name[:31]

        ws = workbook.create_sheet(title=worksheet_name)

        # Process tables in the worksheet
        current_row = 1
        for table in worksheet_data.get("tables", []):
            current_row = self._add_table_to_worksheet(ws, table, current_row, formatting_config)
            current_row += 2  # Add spacing between tables

        # Process charts in the worksheet
        charts = worksheet_data.get("charts", [])

        # Only create charts if they were explicitly specified
        if charts:
            # Ensure chart positions don't conflict
            charts = self._resolve_chart_position_conflicts(charts)

            for chart_data in charts:
                if self._validate_chart_data(chart_data, worksheet_data):
                    self._add_chart_to_worksheet(ws, chart_data)
                else:
                    self.logger.warning(f"[XLSXGenerator] Skipping invalid chart: {chart_data.get('title', 'Untitled')}")
        else:
            self.logger.info("[XLSXGenerator] No charts specified for this worksheet")

        # Auto-size columns
        self._auto_size_columns(ws)

    def _add_table_to_worksheet(
        self, ws: openpyxl.worksheet.worksheet.Worksheet, table: Dict[str, Any],
        start_row: int, formatting_config: Dict[str, Any]
    ) -> int:
        """
        Add a table to the worksheet and return the next available row.
        """
        current_row = start_row

        # Add headers
        headers = table.get("headers", [])
        if headers:
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=current_row, column=col)
                self._set_cell_value_and_format(cell, header, formatting_config, is_header=True)
            current_row += 1

        # Add data rows
        rows = table.get("rows", [])
        for row_data in rows:
            for col, cell_data in enumerate(row_data, 1):
                cell = ws.cell(row=current_row, column=col)
                self._set_cell_value_and_format(cell, cell_data, formatting_config, is_header=False)
            current_row += 1

        return current_row

    def _set_cell_value_and_format(
        self, cell: openpyxl.cell.cell.Cell, cell_data: Dict[str, Any],
        formatting_config: Dict[str, Any], is_header: bool = False
    ):
        """
        Set cell value and apply appropriate formatting based on data type.
        """
        value = cell_data.get("value", "")
        data_type = cell_data.get("data_type", "text")
        cell_style = cell_data.get("style", {})

        # Set cell value
        if data_type == "formula" and isinstance(value, str) and value.startswith("="):
            cell.value = value
        elif data_type in ["number", "currency"]:
            try:
                cell.value = float(value) if value != "" else 0
            except (ValueError, TypeError):
                cell.value = value
        else:
            cell.value = value

        # Apply number formatting
        if data_type == "currency":
            cell.number_format = formatting_config.get("currency_format", "$#,##0.00")
        elif data_type == "percentage":
            cell.number_format = formatting_config.get("percentage_format", "0.0%")
        elif data_type == "date":
            cell.number_format = formatting_config.get("date_format", "mm/dd/yyyy")

        # Apply header formatting
        if is_header:
            header_style = formatting_config.get("header_style", {})
            if header_style.get("bold", True):
                cell.font = Font(bold=True, color=header_style.get("font_color", "000000"))

            bg_color = header_style.get("background_color", "CCCCCC")
            if bg_color:
                cell.fill = PatternFill(start_color=bg_color, end_color=bg_color, fill_type="solid")

        # Apply cell-specific styling
        if cell_style.get("bold"):
            current_font = cell.font
            cell.font = Font(
                bold=True,
                color=current_font.color,
                size=current_font.size,
                name=current_font.name
            )

        # Add borders for better readability
        thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        cell.border = thin_border

    def _auto_size_columns(self, ws: openpyxl.worksheet.worksheet.Worksheet):
        """
        Auto-size columns based on content.
        """
        for column in ws.columns:
            max_length = 0
            column_letter = get_column_letter(column[0].column)

            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass

            # Set column width with some padding, but cap at reasonable maximum
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width

    #──────────────────────────────────────────────────
    #                         CHART CREATION
    #──────────────────────────────────────────────────

    def _add_chart_to_worksheet(self, ws: openpyxl.worksheet.worksheet.Worksheet, chart_data: Dict[str, Any]):
        """
        Create and add a chart to the worksheet.
        """
        from app.utils.xlsx_chart_handler import add_chart_to_worksheet
        add_chart_to_worksheet(ws, chart_data, self.logger)

    # Chart methods moved to xlsx_chart_handler module

    #──────────────────────────────────────────────────
    #                         FILE OPERATIONS
    #──────────────────────────────────────────────────

    def save_outline_to_json_file(self, subtask_id: str, outline_plan: Dict[str, Any]) -> Path:
        """
        Save the Excel outline plan to a JSON file in the knowledge base directory.
        """
        try:
            timestamp = datetime.now(timezone.utc).strftime("%Y%m%d%H%M%S")
            filename = f"xlsx_outline_{subtask_id}_{timestamp}.json"

            kb_dir = get_knowledge_base_dir(self.chat_id)
            kb_dir.mkdir(parents=True, exist_ok=True)
            file_path = kb_dir / filename

            with file_path.open("w", encoding="utf-8") as f:
                json.dump(outline_plan, f, ensure_ascii=False, indent=2)

            self.logger.info(f"[XLSXGenerator] Outline saved to: {file_path}")
            return file_path

        except Exception as e:
            self.logger.error(f"[XLSXGenerator] Error saving outline: {e}", exc_info=True)
            raise

    def save_data_to_json_file(self, subtask_id: str, excel_data: Dict[str, Any]) -> Path:
        """
        Save the Excel data to a JSON file in the knowledge base directory.
        """
        try:
            timestamp = datetime.now(timezone.utc).strftime("%Y%m%d%H%M%S")
            filename = f"xlsx_data_{subtask_id}_{timestamp}.json"

            kb_dir = get_knowledge_base_dir(self.chat_id)
            kb_dir.mkdir(parents=True, exist_ok=True)
            file_path = kb_dir / filename

            with file_path.open("w", encoding="utf-8") as f:
                json.dump(excel_data, f, ensure_ascii=False, indent=2)

            self.logger.info(f"[XLSXGenerator] Data saved to: {file_path}")
            return file_path

        except Exception as e:
            self.logger.error(f"[XLSXGenerator] Error saving data: {e}", exc_info=True)
            raise

    def save_combined_report_to_json_file(self, subtask_id: str, combined_data: Dict[str, Any]) -> Path:
        """
        Save the combined report (outline + data + URLs) to a JSON file.
        """
        try:
            timestamp = datetime.now(timezone.utc).strftime("%Y%m%d%H%M%S")
            filename = f"xlsx_combined_{subtask_id}_{timestamp}.json"

            kb_dir = get_knowledge_base_dir(self.chat_id)
            kb_dir.mkdir(parents=True, exist_ok=True)
            file_path = kb_dir / filename

            with file_path.open("w", encoding="utf-8") as f:
                json.dump(combined_data, f, ensure_ascii=False, indent=2)

            self.logger.info(f"[XLSXGenerator] Combined report saved to: {file_path}")
            return file_path

        except Exception as e:
            self.logger.error(f"[XLSXGenerator] Error saving combined report: {e}", exc_info=True)
            raise

    def report_results(self, results: Dict[str, Any]):
        """
        Reports the results of the subtask execution to the manager.

        Sends a message to the manager containing the subtask ID and the output file path.

        Args:
            results (Dict[str, Any]): Dictionary containing:
                - "subtask_id": Unique identifier for the subtask.
                - "output_file": Path to the saved JSON file.
        """
        subtask_id = results["subtask_id"]
        output_file = results["output_file"]
        send_message(
            self.chat_id,
            sender="worker",
            receiver="manager",
            message={"subtask_id": subtask_id, "output_file": output_file},
        )
        self.logger.info(f"Reported {subtask_id}: {output_file}")
