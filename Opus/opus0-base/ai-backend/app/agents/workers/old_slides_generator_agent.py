# app/agents/workers/slides_generator_agent.py
"""
Module: slides_generator_agent.py

This module defines the SlidesGeneratorAgent class, a worker agent responsible for generating
PowerPoint presentations (.pptx files) from LLM-generated structured data. It converts Markdown
content into styled slides using python-pptx.
"""

import asyncio
import json
import logging
import re
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.enum.text import PP_ALIGN
from pptx.dml.color import RGBColor

from app.agents.base_agent import BaseAgent
from app.core.config import settings
from app.utils.constants import (
    get_knowledge_base_dir,
    get_public_slides_dir,
    get_user_uploads_dir,
)
from app.utils.communication import send_message
from app.db.files import upload_pdf
from pathlib import Path
from app.utils.token_counter import count_tokens
from app.utils.example_rag import get_slides_examples_text, get_slides_content_examples_text

from langchain_openai import Cha<PERSON><PERSON><PERSON><PERSON><PERSON>
from langchain_google_genai import Chat<PERSON>oogleGenerative<PERSON><PERSON>

from langchain_core.prompts import Chat<PERSON>rom<PERSON><PERSON><PERSON>plate
from langchain_core.output_parsers import <PERSON>rOutputParser


# Initialize module-level logger
logger = logging.getLogger(__name__)


#──────────────────────────────────────────────────
#                         PROMPT TEMPLATES
#──────────────────────────────────────────────────


slides_outline_model = ChatGoogleGenerativeAI(
    api_key=settings.GOOGLE_API_KEY,
    model="gemini-2.5-flash-preview-04-17",
    temperature=0.3,
    top_p=0.9,
)

slides_outline_prompt = ChatPromptTemplate.from_template(
    """
## Role
You're a professional presentation designer and content strategist. Generate a **structured JSON outline** for a PowerPoint presentation based on the given requirements.

---

## Objective
Create a comprehensive presentation outline that defines the slide structure, layout types, and content themes. The outline will be used to generate detailed slide content.

---

## Input Context
**Subtask Description:** {subtask_desc}
**User Input:** {user_input}
**Dependencies Info:** {deps_info}

---

## Examples
{examples}

---

## Output Requirements
Generate a **valid JSON object** with this exact structure:

```json
{{
  "title": "Presentation Title",
  "subtitle": "Optional subtitle or tagline",
  "total_slides": 8,
  "theme": {{
    "primary_color": "#1f4e79",
    "secondary_color": "#ffffff", 
    "accent_color": "#ff6b35",
    "font_title": "Calibri",
    "font_body": "Calibri"
  }},
  "slides": [
    {{
      "slide_number": 1,
      "layout_type": "title_slide",
      "title": "Slide Title",
      "content_type": "title_and_subtitle"
    }},
    {{
      "slide_number": 2,
      "layout_type": "title_content",
      "title": "Slide Title",
      "content_type": "bullet_points"
    }}
  ]
}}
```

## Layout Types Available
- **title_slide**: Title and subtitle with optional background
- **title_content**: Title with bullet points or paragraphs  
- **bullet_points**: Multi-level bullet point lists
- **section_header**: Section divider with large title

## Content Types Available
- **title_and_subtitle**: For title slides
- **bullet_points**: For bulleted lists
- **paragraphs**: For text content
- **section_break**: For section dividers

---

## Instructions
1. Analyze the user requirements and create an appropriate slide structure
2. Choose suitable layout types for each slide based on content needs
3. Ensure logical flow and progression through the presentation
4. Include 6-12 slides for optimal presentation length
5. Use professional color schemes and fonts
6. Return **only** the JSON object, no additional text or formatting
"""
)


slides_content_model = ChatGoogleGenerativeAI(
    api_key=settings.GOOGLE_API_KEY,
    model="gemini-2.5-flash-preview-04-17",
    temperature=0.5,
    top_p=0.9,
)

slides_content_prompt = ChatPromptTemplate.from_template(
    """
## Role
You're a professional presentation writer and content creator. Generate **complete slide content** in Markdown format based on the provided outline.

---

## Objective
Create detailed, engaging slide content that follows the outline structure and uses proper Markdown formatting for PowerPoint conversion.

---

## Input Context
**Subtask Description:** {subtask_desc}
**User Input:** {user_input}
**Dependencies Info:** {deps_info}
**Outline Plan:** {outline_plan}

---

## Examples
{examples}

---

## Markdown Format Requirements

### Slide Separators
Use `---` to separate slides

### Slide Structure
```markdown
# Slide 1: Title Slide
## Main Title
### Subtitle or tagline

---

# Slide 2: Content Slide  
## Slide Title
- Bullet point 1
- Bullet point 2
  - Sub-bullet point
- Bullet point 3

---

# Slide 3: Section Header
## Section Title
```

### Text Formatting
- `#` for slide identifiers (Slide 1:, Slide 2:, etc.)
- `##` for slide titles
- `###` for subtitles
- `-` for bullet points
- `  -` for sub-bullet points (2 spaces indent)
- `**text**` for bold formatting (use for key terms, important concepts)
- `*text*` for italic formatting (use for emphasis)
- Plain text for paragraphs

### Speaker Notes (Optional)
Add speaker notes as comments:
```markdown
<!-- Speaker notes: Key points to remember when presenting this slide -->
```

---

## Content Guidelines
1. Create engaging, professional content that matches the outline
2. Use clear, concise language appropriate for presentations
3. Include relevant details from the dependencies info
4. Ensure each slide has focused, digestible content
5. Add speaker notes for complex slides when helpful
6. Follow the exact slide count and structure from the outline

## CRITICAL FORMATTING RULES
- **ALWAYS** put each bullet point on a separate line
- **NEVER** concatenate multiple bullet points on the same line
- **ALWAYS** use proper line breaks between content elements
- **ENSURE** each `- ` bullet point starts on its own line
- **VERIFY** titles and bullet points are properly separated

### Example of CORRECT formatting:
```markdown
## Slide Title
- **Key Term:** Important concept explanation
- *Emphasized point:* Additional details
- Regular bullet point without formatting
```

### Example of INCORRECT formatting (DO NOT DO THIS):
```markdown
## Slide Title- First bullet point- Second bullet point
```

---

## Output Format
Return **only** the Markdown content with proper slide separators and formatting. No additional text or explanations.
"""
)


#──────────────────────────────────────────────────
#                         CLASS DEFINITION
#──────────────────────────────────────────────────

class SlidesGeneratorAgent(BaseAgent):
    """
    Worker agent capable of generating PowerPoint presentations from structured data.

    Inherits from BaseAgent and implements task-specific logic to:
      1. Parse the task details (subtask_id, subtask_description, user_message, deps_info).
      2. Generate a slides outline based on the subtask details (using LLM).
      3. Save that JSON outline in a markdown (.md) file.
      4. Generate detailed slide content in Markdown format.
      5. Convert Markdown to PowerPoint (.pptx) using python-pptx.
      6. Upload to R2 and provide download link.
      7. Report completion to the manager.
    """

    def __init__(self, chat_id: str):
        super().__init__(chat_id, "slides_generator")
        self.chat_id = chat_id
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        self.full_outline_text = ""
        self.uploaded_images = []

    async def execute_task(self, task_details: Dict[str, Any]):
        """
        Steps:
        1) Generate a slides outline in JSON (raw).
        2) Save it as-is to a .md file.
        3) Generate detailed slide content in Markdown format.
        4) Save combined content to another .md file.
        5) Convert Markdown to PowerPoint (.pptx) using python-pptx.
        6) Upload to R2 and get presigned URL.
        7) Report completion to manager.
        """
        try:
            self.start_timer("slides agent")

            # Extract task details
            subtask_id = task_details.get("subtask_id", "unknown")
            subtask_description = task_details.get("subtask_description", "")
            user_input = task_details.get("user_input", "")
            deps_info = task_details.get("deps_info", "")

            self.logger.info(
                f"[SlidesGenerator] Starting task execution for subtask_id='{subtask_id}'"
            )

            # ----------------------------
            # (1) Generate & Save Outline
            # ----------------------------
            self.logger.info(
                f"[SlidesGenerator] Outline generation input => subtask_desc='{subtask_description}'"
            )
            outline_plan = await self.determine_slides_outline(
                subtask_desc=subtask_description,
                user_input=user_input,
                deps_info=deps_info,
            )
            # Store full outline text for later content prompts
            self.full_outline_text = json.dumps(
                outline_plan, ensure_ascii=False, indent=2
            )
            self.logger.info("[SlidesGenerator] Outline generated successfully.")
            self.save_outline_to_md_file(subtask_id, outline_plan)

            # ----------------------------
            # (2) Generate Slide Content
            # ----------------------------
            self.runtime_log("Generating slide content")
            slides_content = await self.generate_slides_content(
                outline_plan=outline_plan,
                subtask_desc=subtask_description,
                user_input=user_input,
                deps_info=deps_info,
            )
            self.logger.info("[SlidesGenerator] Slide content generated successfully.")
            self.save_content_to_md_file(subtask_id, slides_content)

            # ----------------------------
            # (3) Generate PowerPoint File
            # ----------------------------
            self.runtime_log("Creating PowerPoint presentation")
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            pptx_name = f"slides_result_{subtask_id}_{timestamp}.pptx"
            slides_dir = get_public_slides_dir(self.chat_id)
            slides_dir.mkdir(parents=True, exist_ok=True)
            pptx_path = slides_dir / pptx_name

            # Convert Markdown to PPTX
            self.create_pptx_from_markdown(slides_content, pptx_path, outline_plan)
            self.logger.info(f"[SlidesGenerator] PowerPoint created at {pptx_path}")

            # ----------------------------
            # (4) Upload to R2 and get URL
            # ----------------------------
            from app.utils.r2_client import upload_file_to_r2

            pptx_url = upload_file_to_r2(
                pptx_path,
                content_type="application/vnd.openxmlformats-presentationml.presentation",
                expires_in=60 * 60 * 24,  # valid for 24 h
            )

            doc_url = f"{settings.BASE_URL}/docs/{pptx_path.name}"
            self.runtime_log(f"Generated PPTX: {doc_url}")

            # ----------------------------
            # (5) Create combined report
            # ----------------------------
            combined_markdown = (
                f"# Your PowerPoint presentation is ready\n\n"
                f"**Download PPTX:** [{pptx_name}]({doc_url})\n\n"
                f"---\n\n"
                f"## Generated Slides Content:\n\n{slides_content}\n\n"
                f"You can always download your presentation at {doc_url}\n\n"
                f"#IMPORTANT: Always give the pptx url link to the user in your final message."
            )
            combined_file_path = self.save_combined_content_to_md_file(
                subtask_id, combined_markdown
            )
            self.logger.info(
                f"[SlidesGenerator] Combined content file ⇒ {combined_file_path}"
            )

            # ----------------------------
            # (6) Report completion to manager
            # ----------------------------
            self.report_results(
                {"subtask_id": subtask_id, "output_file": combined_file_path}
            )

            self.logger.info(
                f"[SlidesGenerator] Subtask {subtask_id} completed with final PPTX and combined file reported."
            )

            self.stop_timer("slides agent")

        except Exception as e:
            self.logger.error(f"[SlidesGenerator] Task execution error: {str(e)}")
            self.handle_error(e)

    async def determine_slides_outline(
        self, subtask_desc: str, user_input: str, deps_info: str
    ) -> Dict[str, Any]:
        """
        Asks LLM to produce a JSON outline for the slides. If it fails, we fallback to a default.
        """

        # Get relevant examples from RAG system
        examples_text = get_slides_examples_text(user_input, k=3)

        input_data = {
            "subtask_desc": subtask_desc,
            "user_input": user_input,
            "deps_info": deps_info,
            "examples": examples_text
        }

        self.logger.info(f"[SlidesGenerator] Outline input tokens: {count_tokens(str(input_data))}")

        try:
            chain = slides_outline_prompt | slides_outline_model | StrOutputParser()
            raw_response = await self._invoke_chain(chain, input_data)

            self.logger.info(f"[SlidesGenerator] Raw outline response: {raw_response[:200]}...")

            # Try to parse JSON from the response
            json_match = re.search(r'\{.*\}', raw_response, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                outline_plan = json.loads(json_str)
                self.logger.info("[SlidesGenerator] Successfully parsed JSON outline")
                return outline_plan
            else:
                raise ValueError("No JSON found in response")

        except Exception as e:
            self.logger.warning(f"[SlidesGenerator] Outline generation failed: {e}")
            # Fallback outline
            return {
                "title": "Presentation",
                "subtitle": "Generated from user requirements",
                "total_slides": 6,
                "theme": {
                    "primary_color": "#1f4e79",
                    "secondary_color": "#ffffff",
                    "accent_color": "#ff6b35",
                    "font_title": "Calibri",
                    "font_body": "Calibri"
                },
                "slides": [
                    {"slide_number": 1, "layout_type": "title_slide", "title": "Title Slide", "content_type": "title_and_subtitle"},
                    {"slide_number": 2, "layout_type": "title_content", "title": "Overview", "content_type": "bullet_points"},
                    {"slide_number": 3, "layout_type": "title_content", "title": "Main Content", "content_type": "bullet_points"},
                    {"slide_number": 4, "layout_type": "title_content", "title": "Details", "content_type": "bullet_points"},
                    {"slide_number": 5, "layout_type": "title_content", "title": "Summary", "content_type": "bullet_points"},
                    {"slide_number": 6, "layout_type": "section_header", "title": "Thank You", "content_type": "section_break"}
                ]
            }

    async def generate_slides_content(
        self, outline_plan: Dict[str, Any], subtask_desc: str, user_input: str, deps_info: str
    ) -> str:
        """
        Generate detailed slide content in Markdown format based on the outline.
        """

        # Get relevant examples from RAG system
        examples_text = get_slides_content_examples_text(user_input, k=3)

        input_data = {
            "subtask_desc": subtask_desc,
            "user_input": user_input,
            "deps_info": deps_info,
            "outline_plan": self.full_outline_text,
            "examples": examples_text
        }

        self.logger.info(f"[SlidesGenerator] Content input tokens: {count_tokens(str(input_data))}")

        try:
            chain = slides_content_prompt | slides_content_model | StrOutputParser()
            content = await self._invoke_chain(chain, input_data)

            self.logger.info(f"[SlidesGenerator] Generated content length: {len(content)} chars")

            # Clean up and validate the markdown content
            cleaned_content = self._clean_markdown_content(content)
            self.logger.info(f"[SlidesGenerator] Cleaned content length: {len(cleaned_content)} chars")

            return cleaned_content

        except Exception as e:
            self.logger.warning(f"[SlidesGenerator] Content generation failed: {e}")
            # Fallback content based on outline
            slides = outline_plan.get("slides", [])
            fallback_content = []

            for i, slide in enumerate(slides, 1):
                slide_title = slide.get("title", f"Slide {i}")
                layout_type = slide.get("layout_type", "title_content")

                if layout_type == "title_slide":
                    fallback_content.append(f"# Slide {i}: Title Slide")
                    fallback_content.append(f"## {outline_plan.get('title', 'Presentation Title')}")
                    fallback_content.append(f"### {outline_plan.get('subtitle', 'Subtitle')}")
                elif layout_type == "section_header":
                    fallback_content.append(f"# Slide {i}: Section Header")
                    fallback_content.append(f"## {slide_title}")
                else:
                    fallback_content.append(f"# Slide {i}: Content Slide")
                    fallback_content.append(f"## {slide_title}")
                    fallback_content.append("- Key point 1")
                    fallback_content.append("- Key point 2")
                    fallback_content.append("- Key point 3")

                if i < len(slides):
                    fallback_content.append("\n---\n")

            return "\n".join(fallback_content)

    def _clean_markdown_content(self, content: str) -> str:
        """
        Clean and validate markdown content to fix common formatting issues.

        Extend with new cleaning rules by adding methods to the cleaning_rules list.
        """
        try:
            lines = content.split('\n')
            cleaned_lines = []

            # Define cleaning rules
            cleaning_rules = [
                self._fix_concatenated_bullets,
                self._fix_title_bullet_concatenation,
                self._fix_multiple_bullets_per_line,
                # Future cleaning rules can be added here:
                # self._fix_image_references,
                # self._fix_table_formatting,
            ]

            # Apply cleaning rules line by line
            for line in lines:
                processed_line = line

                for rule in cleaning_rules:
                    result = rule(processed_line)
                    if isinstance(result, list):
                        # Rule returned multiple lines
                        cleaned_lines.extend(result)
                        processed_line = None
                        break
                    else:
                        processed_line = result

                if processed_line is not None:
                    cleaned_lines.append(processed_line)

            # Apply global fixes
            cleaned_content = '\n'.join(cleaned_lines)
            cleaned_content = self._fix_slide_separators(cleaned_content)
            cleaned_content = self._remove_excessive_blank_lines(cleaned_content)

            self.logger.debug("[SlidesGenerator] Markdown content cleaned successfully")
            return cleaned_content.strip()

        except Exception as e:
            self.logger.warning(f"[SlidesGenerator] Markdown cleaning failed: {e}")
            return content

    def _fix_concatenated_bullets(self, line: str):
        """Fix bullet points that are concatenated without line breaks."""
        if '- **' in line and line.count('- **') > 1:
            parts = line.split('- **')
            result = []

            if parts[0].strip():
                result.append(parts[0].strip())

            for part in parts[1:]:
                if part.strip():
                    result.append(f"- **{part}")

            return result
        return line

    def _fix_title_bullet_concatenation(self, line: str):
        """Fix titles that are concatenated with bullet points."""
        if line.startswith('## ') and '- **' in line:
            title_part = line.split('- **')[0].strip()
            bullet_parts = line.split('- **')[1:]

            result = [title_part]
            for part in bullet_parts:
                if part.strip():
                    result.append(f"- **{part}")

            return result
        return line

    def _fix_multiple_bullets_per_line(self, line: str):
        """Fix multiple bullet points on the same line."""
        if line.startswith('- ') and line.count('- **') > 1:
            first_bullet = line.split('- **')[0]
            remaining_bullets = line.split('- **')[1:]

            result = [first_bullet]
            for bullet in remaining_bullets:
                if bullet.strip():
                    result.append(f"- **{bullet}")

            return result
        return line

    def _fix_slide_separators(self, content: str) -> str:
        """Ensure slide separators have proper spacing."""
        return re.sub(r'\n*---\n*', '\n\n---\n\n', content)

    def _remove_excessive_blank_lines(self, content: str) -> str:
        """Remove excessive blank lines."""
        return re.sub(r'\n{3,}', '\n\n', content)

    #──────────────────────────────────────────────────
    #                    CONTENT PROCESSING
    #──────────────────────────────────────────────────

    def _parse_markdown_formatting(self, text: str) -> List[Dict[str, Any]]:
        """
        Parse markdown formatting in text and return a list of text runs with formatting.

        Currently supports:
        - **bold** formatting
        - *italic* formatting

        Extend with new formatting types by adding patterns to the regex.
        """
        runs = []
        current_pos = 0

        # Define formatting patterns
        # Future: add patterns for underline, strikethrough, colors, etc.
        pattern = r'(\*\*([^*]+)\*\*|\*([^*]+)\*)'

        for match in re.finditer(pattern, text):
            # Add text before the match
            if match.start() > current_pos:
                runs.append(self._create_text_run(
                    text[current_pos:match.start()]
                ))

            # Add the formatted text
            if match.group(0).startswith('**'):
                # Bold text
                runs.append(self._create_text_run(
                    match.group(2), bold=True
                ))
            else:
                # Italic text
                runs.append(self._create_text_run(
                    match.group(3), italic=True
                ))

            current_pos = match.end()

        # Add remaining text
        if current_pos < len(text):
            runs.append(self._create_text_run(text[current_pos:]))

        # If no formatting found, return the whole text as a single run
        if not runs:
            runs.append(self._create_text_run(text))

        return runs

    def _create_text_run(self, text: str, bold: bool = False, italic: bool = False) -> Dict[str, Any]:
        """
        Create a text run with formatting properties.

        Extend with new formatting properties:
        - underline, strikethrough, color, font size, etc.
        """
        return {
            "text": text,
            "bold": bold,
            "italic": italic,
            # Future formatting properties can be added here:
            # "underline": False,
            # "color": None,
            # "font_size": None,
        }

    #──────────────────────────────────────────────────
    #                    PPTX GENERATION
    #──────────────────────────────────────────────────

    def create_pptx_from_markdown(self, markdown_content: str, pptx_path: Path, outline_plan: Dict[str, Any]):
        """
        Convert Markdown content to PowerPoint presentation using python-pptx.
        """
        try:
            # Parse markdown into slide data
            slides_data = self._parse_markdown_slides(markdown_content)
            self.logger.info(f"[SlidesGenerator] Parsed {len(slides_data)} slides from markdown")

            # Create presentation
            presentation = Presentation()

            # Apply theme styling
            theme_config = outline_plan.get("theme", {})
            self._apply_theme_styling(presentation, theme_config)

            # Create slides with error handling for each slide
            for i, slide_data in enumerate(slides_data):
                try:
                    self._create_slide_from_data(presentation, slide_data, theme_config)
                    self.logger.debug(f"[SlidesGenerator] Created slide {i+1}: {slide_data.get('title', 'Untitled')}")
                except Exception as slide_error:
                    self.logger.warning(f"[SlidesGenerator] Failed to create slide {i+1}: {slide_error}")
                    # Create a simple fallback slide
                    self._create_fallback_slide(presentation, slide_data, i+1)

            # Ensure we have at least one slide
            if len(presentation.slides) == 0:
                self.logger.warning("[SlidesGenerator] No slides created, adding fallback slide")
                self._create_fallback_slide(presentation, {"title": "Presentation", "content": []}, 1)

            # Save presentation
            presentation.save(str(pptx_path))
            self.logger.info(f"[SlidesGenerator] PPTX saved to {pptx_path} with {len(presentation.slides)} slides")

            # Validate the created file
            if not pptx_path.exists():
                raise FileNotFoundError(f"PPTX file was not created at {pptx_path}")

            file_size = pptx_path.stat().st_size
            if file_size < 1000:  # Less than 1KB is likely corrupted
                raise ValueError(f"PPTX file appears to be corrupted (size: {file_size} bytes)")

            self.logger.info(f"[SlidesGenerator] PPTX validation successful (size: {file_size} bytes)")

        except Exception as e:
            self.logger.error(f"[SlidesGenerator] PPTX creation failed: {e}")
            raise

    def _parse_markdown_slides(self, markdown_content: str) -> List[Dict[str, Any]]:
        """
        Parse Markdown content into structured slide data.
        """
        slides = []
        slide_sections = markdown_content.split("---")

        for i, section in enumerate(slide_sections):
            section = section.strip()
            if not section:
                continue

            slide_data = {
                "slide_number": i + 1,
                "title": "",
                "subtitle": "",
                "content": [],
                "layout_type": "title_content",
                "speaker_notes": ""
            }

            lines = section.split("\n")
            current_content = []

            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # Extract speaker notes
                if line.startswith("<!--") and line.endswith("-->"):
                    note = line[4:-3].strip()
                    if note.startswith("Speaker notes:"):
                        slide_data["speaker_notes"] = note[14:].strip()
                    continue

                # Parse slide identifier and determine layout
                if line.startswith("# Slide"):
                    if "Title Slide" in line:
                        slide_data["layout_type"] = "title_slide"
                    elif "Section Header" in line:
                        slide_data["layout_type"] = "section_header"
                    else:
                        slide_data["layout_type"] = "title_content"
                    continue

                # Parse titles and subtitles
                if line.startswith("## "):
                    slide_data["title"] = line[3:].strip()
                elif line.startswith("### "):
                    slide_data["subtitle"] = line[4:].strip()
                elif line.startswith("- "):
                    # Bullet point
                    current_content.append({"type": "bullet", "text": line[2:].strip(), "level": 1})
                elif line.startswith("  - "):
                    # Sub-bullet point
                    current_content.append({"type": "bullet", "text": line[4:].strip(), "level": 2})
                else:
                    # Regular paragraph text
                    if line:
                        current_content.append({"type": "paragraph", "text": line})

            slide_data["content"] = current_content
            slides.append(slide_data)

        return slides

    def _create_fallback_slide(self, presentation: Presentation, slide_data: Dict[str, Any], slide_number: int):
        """
        Create a simple fallback slide when normal slide creation fails.
        """
        try:
            # Use the simplest layout (title and content)
            slide_layout = presentation.slide_layouts[1]
            slide = presentation.slides.add_slide(slide_layout)

            # Set title safely
            if slide.shapes.title:
                title = slide_data.get("title", f"Slide {slide_number}")
                slide.shapes.title.text = title

            # Add simple content if available
            content_items = slide_data.get("content", [])
            if content_items and len(slide.placeholders) > 1:
                content_placeholder = slide.placeholders[1]
                if hasattr(content_placeholder, 'text_frame'):
                    text_frame = content_placeholder.text_frame
                    text_frame.clear()

                    # Add first few content items as simple text
                    for i, item in enumerate(content_items[:5]):  # Limit to 5 items
                        if i == 0:
                            p = text_frame.paragraphs[0]
                        else:
                            p = text_frame.add_paragraph()

                        text = item.get("text", str(item))

                        # Set bullet level if it's a bullet point
                        if item.get("type") == "bullet":
                            p.level = min(item.get("level", 1) - 1, 4)

                        # Apply formatted text
                        self._apply_formatted_text_to_paragraph(p, text, {}, is_body=True)

            self.logger.info(f"[SlidesGenerator] Created fallback slide {slide_number}")

        except Exception as e:
            self.logger.error(f"[SlidesGenerator] Even fallback slide creation failed: {e}")
            # Create the most basic slide possible
            try:
                slide_layout = presentation.slide_layouts[0]  # Title slide
                slide = presentation.slides.add_slide(slide_layout)
                if slide.shapes.title:
                    slide.shapes.title.text = f"Slide {slide_number}"
            except Exception as final_error:
                self.logger.error(f"[SlidesGenerator] Final fallback failed: {final_error}")

    def _create_slide_from_data(self, presentation: Presentation, slide_data: Dict[str, Any], theme_config: Dict[str, Any]):
        """
        Create a slide in the presentation based on slide data.

        Extend with new layout types by adding cases to the layout creators.
        """
        layout_type = slide_data.get("layout_type", "title_content")

        # Define layout creators
        layout_creators = {
            "title_slide": self._create_title_slide,
            "title_content": self._create_title_content_slide,
            "section_header": self._create_section_header_slide,
            # Future layout types can be added here:
            # "two_column": self._create_two_column_slide,
            # "image_content": self._create_image_content_slide,
        }

        # Get the appropriate creator
        creator = layout_creators.get(layout_type, self._create_title_content_slide)
        return creator(presentation, slide_data, theme_config)

    def _create_title_slide(self, presentation: Presentation, slide_data: Dict[str, Any], theme_config: Dict[str, Any]):
        """Create a title slide."""
        slide_layout = presentation.slide_layouts[0]
        slide = presentation.slides.add_slide(slide_layout)

        # Set title
        if slide.shapes.title:
            title_text = slide_data.get("title", "")
            self._apply_formatted_text_to_paragraph(
                slide.shapes.title.text_frame.paragraphs[0],
                title_text,
                theme_config,
                is_title=True
            )

        # Set subtitle
        if len(slide.placeholders) > 1:
            subtitle_placeholder = slide.placeholders[1]
            subtitle_text = slide_data.get("subtitle", "")
            if subtitle_text:
                self._apply_formatted_text_to_paragraph(
                    subtitle_placeholder.text_frame.paragraphs[0],
                    subtitle_text,
                    theme_config,
                    is_subtitle=True
                )

        return slide

    def _create_title_content_slide(self, presentation: Presentation, slide_data: Dict[str, Any], theme_config: Dict[str, Any]):
        """Create a title and content slide."""
        slide_layout = presentation.slide_layouts[1]
        slide = presentation.slides.add_slide(slide_layout)

        # Set title
        if slide.shapes.title:
            title_text = slide_data.get("title", "")
            self._apply_formatted_text_to_paragraph(
                slide.shapes.title.text_frame.paragraphs[0],
                title_text,
                theme_config,
                is_title=True
            )

        # Add content
        content_items = slide_data.get("content", [])
        if content_items and len(slide.placeholders) > 1:
            content_placeholder = slide.placeholders[1]
            self._add_content_to_placeholder(content_placeholder, content_items, theme_config)

        # Add speaker notes
        self._add_speaker_notes(slide, slide_data.get("speaker_notes", ""))

        return slide

    def _create_section_header_slide(self, presentation: Presentation, slide_data: Dict[str, Any], theme_config: Dict[str, Any]):
        """Create a section header slide."""
        try:
            slide_layout = presentation.slide_layouts[2]
        except IndexError:
            # Fallback to title layout if section header layout doesn't exist
            slide_layout = presentation.slide_layouts[1]

        slide = presentation.slides.add_slide(slide_layout)

        # Set title
        if slide.shapes.title:
            title_text = slide_data.get("title", "")
            self._apply_formatted_text_to_paragraph(
                slide.shapes.title.text_frame.paragraphs[0],
                title_text,
                theme_config,
                is_title=True
            )

        return slide

    def _add_speaker_notes(self, slide, notes_text: str):
        """Add speaker notes to a slide."""
        if notes_text:
            try:
                notes_slide = slide.notes_slide
                notes_slide.notes_text_frame.text = notes_text
            except Exception:
                pass  # Fail silently if notes can't be added

    def _add_content_to_placeholder(self, placeholder, content_items: List[Dict[str, Any]], theme_config: Dict[str, Any]):
        """
        Add content items (bullets, paragraphs) to a placeholder.
        """
        if not content_items:
            return

        text_frame = placeholder.text_frame
        text_frame.clear()

        for i, item in enumerate(content_items):
            if i == 0:
                # Use the first paragraph that already exists
                p = text_frame.paragraphs[0]
            else:
                # Add new paragraphs for subsequent items
                p = text_frame.add_paragraph()

            item_type = item.get("type", "paragraph")
            text = item.get("text", "")

            if item_type == "bullet":
                level = item.get("level", 1) - 1  # Convert to 0-based
                p.level = min(level, 4)  # Max 5 levels (0-4)

                # Parse markdown formatting and apply to paragraph
                self._apply_formatted_text_to_paragraph(p, text, theme_config, is_body=True)
            else:
                # Paragraph text
                self._apply_formatted_text_to_paragraph(p, text, theme_config, is_body=True)

    def _apply_text_formatting(self, text_element, theme_config: Dict[str, Any], is_title: bool = False, is_subtitle: bool = False, is_body: bool = False):
        """
        Apply theme-based formatting to text elements.
        """
        try:
            if hasattr(text_element, 'text_frame') and text_element.text_frame:
                # This is a shape with text_frame
                for paragraph in text_element.text_frame.paragraphs:
                    self._format_paragraph(paragraph, theme_config, is_title, is_subtitle, is_body)
            elif hasattr(text_element, 'font'):
                # This is a paragraph
                self._format_paragraph(text_element, theme_config, is_title, is_subtitle, is_body)
        except Exception as e:
            self.logger.warning(f"[SlidesGenerator] Text formatting failed: {e}")

    def _apply_formatted_text_to_paragraph(self, paragraph, text: str, theme_config: Dict[str, Any], is_title: bool = False, is_subtitle: bool = False, is_body: bool = False):
        """
        Apply formatted text to a paragraph, parsing markdown and creating text runs.
        """
        try:
            # Parse markdown formatting
            text_runs = self._parse_markdown_formatting(text)

            # Clear existing text
            paragraph.clear()

            # Add text runs with formatting
            for i, run_data in enumerate(text_runs):
                if i == 0:
                    # Use the existing paragraph for the first run
                    run = paragraph.runs[0] if paragraph.runs else paragraph.add_run()
                else:
                    # Add new runs for subsequent text
                    run = paragraph.add_run()

                run.text = run_data["text"]

                # Apply formatting
                if run_data["bold"]:
                    run.font.bold = True
                if run_data["italic"]:
                    run.font.italic = True

                # Apply theme formatting
                self._apply_run_formatting(run, theme_config, is_title, is_subtitle, is_body)

        except Exception as e:
            self.logger.warning(f"[SlidesGenerator] Formatted text application failed: {e}")
            # Fallback to plain text
            paragraph.text = text

    def _apply_run_formatting(self, run, theme_config: Dict[str, Any], is_title: bool = False, is_subtitle: bool = False, is_body: bool = False):
        """
        Apply theme formatting to a text run.
        """
        try:
            font = run.font

            # Set font family and size
            if is_title:
                font.name = theme_config.get("font_title", "Calibri")
                font.size = Pt(44)
            elif is_subtitle:
                font.name = theme_config.get("font_title", "Calibri")
                font.size = Pt(32)
            else:
                font.name = theme_config.get("font_body", "Calibri")
                font.size = Pt(24)

            # Set color
            primary_color = theme_config.get("primary_color", "#1f4e79")
            if primary_color.startswith("#") and len(primary_color) == 7:
                hex_color = primary_color[1:]
                rgb = tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
                font.color.rgb = RGBColor(*rgb)

        except Exception as e:
            self.logger.debug(f"[SlidesGenerator] Run formatting failed: {e}")

    def _format_paragraph(self, paragraph, theme_config: Dict[str, Any], is_title: bool, is_subtitle: bool, is_body: bool):
        """
        Format a single paragraph based on theme configuration.
        """
        try:
            # Get font object safely
            font = None
            if hasattr(paragraph, 'font'):
                font = paragraph.font
            elif hasattr(paragraph, 'runs') and paragraph.runs:
                font = paragraph.runs[0].font

            if not font:
                return

            # Set font family safely
            try:
                if is_title:
                    font.name = theme_config.get("font_title", "Calibri")
                    if hasattr(font, 'size'):
                        font.size = Pt(44)
                    if hasattr(font, 'bold'):
                        font.bold = True
                elif is_subtitle:
                    font.name = theme_config.get("font_title", "Calibri")
                    if hasattr(font, 'size'):
                        font.size = Pt(32)
                    if hasattr(font, 'bold'):
                        font.bold = False
                else:
                    font.name = theme_config.get("font_body", "Calibri")
                    if hasattr(font, 'size'):
                        font.size = Pt(24)
                    if hasattr(font, 'bold'):
                        font.bold = False
            except Exception as font_error:
                self.logger.debug(f"[SlidesGenerator] Font setting failed: {font_error}")

            # Set color safely
            try:
                primary_color = theme_config.get("primary_color", "#1f4e79")
                if primary_color.startswith("#") and len(primary_color) == 7:
                    # Convert hex to RGB
                    hex_color = primary_color[1:]
                    rgb = tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
                    if hasattr(font, 'color') and hasattr(font.color, 'rgb'):
                        font.color.rgb = RGBColor(*rgb)
            except Exception as color_error:
                self.logger.debug(f"[SlidesGenerator] Color setting failed: {color_error}")

        except Exception as e:
            self.logger.warning(f"[SlidesGenerator] Paragraph formatting failed: {e}")

    def _apply_theme_styling(self, presentation: Presentation, theme_config: Dict[str, Any]):
        """
        Apply global theme styling to the presentation.
        """
        try:
            # Note: python-pptx has limited theme customization capabilities
            # Most styling is applied per-slide and per-element
            pass
        except Exception as e:
            self.logger.warning(f"[SlidesGenerator] Theme styling failed: {e}")

    async def _invoke_chain(self, chain, input_data: Dict[str, Any]) -> str:
        """
        Invoke a LangChain chain with proper error handling and streaming support.
        """
        try:
            if hasattr(chain, 'astream'):
                # Use streaming if available
                chunks = []
                async for chunk in chain.astream(input_data):
                    chunks.append(chunk)
                return "".join(chunks)
            else:
                # Fallback to regular invoke
                return await chain.ainvoke(input_data)
        except Exception as e:
            self.logger.error(f"[SlidesGenerator] Chain invocation failed: {e}")
            raise


#──────────────────────────────────────────────────
#                         UTILITY METHODS
#──────────────────────────────────────────────────


    def save_outline_to_md_file(self, subtask_id: str, outline_plan: Dict[str, Any]) -> Path:
        """
        Save the JSON outline to a markdown file in the knowledge base.
        """
        try:
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            filename = f"slides_outline_{subtask_id}_{timestamp}.md"
            kb_dir = get_knowledge_base_dir(self.chat_id)
            kb_dir.mkdir(parents=True, exist_ok=True)
            file_path = kb_dir / filename

            # Format as markdown with JSON content
            content = f"# Slides Outline for Subtask {subtask_id}\n\n"
            content += f"Generated at: {datetime.now().isoformat()}\n\n"
            content += "```json\n"
            content += json.dumps(outline_plan, ensure_ascii=False, indent=2)
            content += "\n```\n"

            file_path.write_text(content, encoding="utf-8")
            self.logger.info(f"[SlidesGenerator] Outline saved to {file_path}")
            return file_path

        except Exception as e:
            self.logger.error(f"[SlidesGenerator] Failed to save outline: {e}")
            raise

    def save_content_to_md_file(self, subtask_id: str, content: str) -> Path:
        """
        Save the generated slide content to a markdown file in the knowledge base.
        """
        try:
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            filename = f"slides_content_{subtask_id}_{timestamp}.md"
            kb_dir = get_knowledge_base_dir(self.chat_id)
            kb_dir.mkdir(parents=True, exist_ok=True)
            file_path = kb_dir / filename

            # Format as markdown
            markdown_content = f"# Slides Content for Subtask {subtask_id}\n\n"
            markdown_content += f"Generated at: {datetime.now().isoformat()}\n\n"
            markdown_content += "---\n\n"
            markdown_content += content

            file_path.write_text(markdown_content, encoding="utf-8")
            self.logger.info(f"[SlidesGenerator] Content saved to {file_path}")
            return file_path

        except Exception as e:
            self.logger.error(f"[SlidesGenerator] Failed to save content: {e}")
            raise

    def save_combined_content_to_md_file(self, subtask_id: str, combined_content: str) -> Path:
        """
        Save the final combined content (with download link) to a markdown file.
        """
        try:
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            filename = f"slides_combined_{subtask_id}_{timestamp}.md"
            kb_dir = get_knowledge_base_dir(self.chat_id)
            kb_dir.mkdir(parents=True, exist_ok=True)
            file_path = kb_dir / filename

            file_path.write_text(combined_content, encoding="utf-8")
            self.logger.info(f"[SlidesGenerator] Combined content saved to {file_path}")
            return file_path

        except Exception as e:
            self.logger.error(f"[SlidesGenerator] Failed to save combined content: {e}")
            raise

    def report_results(self, results: Dict[str, Any]):
        """
        Report task completion results to the manager.
        """
        try:
            subtask_id = results.get("subtask_id", "unknown")
            output_file = results.get("output_file", "")

            self.logger.info(f"[SlidesGenerator] Reporting results for subtask {subtask_id}")

            # Send completion message to manager
            send_message(
                chat_id=self.chat_id,
                sender="worker",
                receiver="manager",
                message={
                    "subtask_id": subtask_id,
                    "output_file": str(output_file)
                }
            )

            self.logger.info(f"[SlidesGenerator] Results reported successfully for subtask {subtask_id}")

        except Exception as e:
            self.logger.error(f"[SlidesGenerator] Failed to report results: {e}")
            raise
