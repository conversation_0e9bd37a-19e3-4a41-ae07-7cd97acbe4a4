# app/agents/workers/researcher_agent/tavily_client.py
"""
Module: tavily_client.py

Thin async wrapper around <PERSON><PERSON>'s /search, /extract and /crawl endpoints.
Logs request/response details to aid debugging.
"""

import aiohttp
import asyncio
import json
import logging
from typing import Any, Dict, List, Tuple

from app.core.config import settings

TAVILY_BASE = "https://api.tavily.com"
SEM = asyncio.Semaphore(5)  # global concurrency limit

logger = logging.getLogger(__name__)

if not settings.TAVILY_API_KEY:
    logger.critical("TAVILY_API_KEY environment variable not set")


async def _post(endpoint: str, payload: Dict[str, Any]) -> Dict[str, Any]:
    async with SEM:
        if not settings.TAVILY_API_KEY:
            err = "TAVILY_API_KEY is required for <PERSON><PERSON> requests"
            raise RuntimeError(err)

        headers = {
            "Authorization": f"Bearer {settings.TAVILY_API_KEY}",
            "Content-Type": "application/json",
        }

        async with aiohttp.ClientSession(headers=headers) as ses:
            logger.debug("[tavily] POST %s payload=%s", endpoint, payload)
            async with ses.post(
                f"{TAVILY_BASE}{endpoint}",
                json=payload,
                timeout=30,
            ) as r:
                text = await r.text()
                if r.status >= 400:
                    logger.error(
                        "[tavily] %s %s: %s",
                        endpoint,
                        r.status,
                        text,
                    )
                    r.raise_for_status()
                logger.debug("[tavily] %s response: %s", endpoint, text)
                return json.loads(text)


# ----------------------------- public API --------------------------------- #
async def search_urls(query: str, *, k: int = 10) -> List[str]:
    """
    Returns a list of up-to-k URLs deemed relevant by Tavily Search.
    """
    body = {"query": query, "search_depth": "basic", "max_results": k}
    data = await _post("/search", body)
    return [item["url"] for item in data.get("results", [])][:k]


async def extract_url(url: str, depth: str = "basic") -> str:
    """
    Fetch and return markdown content of a single URL.
    """
    data = await _post(
        "/extract", {"urls": url, "extract_depth": depth, "format": "markdown"}
    )
    if data.get("results"):
        return data["results"][0]["raw_content"]
    return ""


async def crawl_site(
    base_url: str, *, max_depth: int = 1, limit: int = 30
) -> List[Tuple[str, str]]:
    """
    Crawl starting at base_url; return list[(url, markdown_content)].
    """
    body = {
        "url": base_url,
        "max_depth": max_depth,
        "limit": limit,
        "extract_depth": "basic",
        "format": "markdown",
        "allow_external": False,
    }
    data = await _post("/crawl", body)
    results = data.get("results", [])
    return [(item["url"], item["raw_content"]) for item in results]
