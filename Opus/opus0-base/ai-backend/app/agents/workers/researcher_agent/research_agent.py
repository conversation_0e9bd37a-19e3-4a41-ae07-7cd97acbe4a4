# app/agents/workers/researcher_agent/researcher_agent.py
"""
Module: research_agent.py

Simplified research worker performing a shallow search and crawl loop.
"""

# ──────────────────────────────────────────────────
#                         IMPORTS
# ──────────────────────────────────────────────────
import logging
import os
from datetime import datetime
from typing import Any, Dict, List, Set, Optional

from crawl4ai import AsyncWebCrawler
from crawl4ai.async_configs import BrowserConfig

from app.agents.base_agent import BaseAgent
from app.utils.constants import get_knowledge_base_dir
from app.utils.communication import send_message

from .utils import (
    looks_like_document,
    looks_like_image,
    filter_new_links,
)
from .crawl_agent import crawl_site
from .content_handlers import convert_doc_to_text, vision_describe_image
from .query_generator import generate_query
from .brave_client import search_urls
from .evaluate import evaluate_content
from .feedback import get_additional_queries

logger = logging.getLogger(__name__)

MAX_DEPTH = 3


# ──────────────────────────────────────────────────
#                         HELPERS
# ──────────────────────────────────────────────────
def clean_text(text: str) -> str:
    """Collapse whitespace in ``text``."""

    return " ".join(text.split())


# ──────────────────────────────────────────────────
#                       AGENT CLASS
# ──────────────────────────────────────────────────
class ResearcherAgent(BaseAgent):  # type: ignore
    """Single-query research loop using Brave search and Crawl4AI."""

    def __init__(self, agent_id: str, chat_id: str) -> None:
        super().__init__(agent_id, agent_type="Research Agent")
        self.chat_id = chat_id
        self._crawler: Optional[AsyncWebCrawler] = None
        self._crawler_started = False
        self.scraped_links: Set[str] = set()

    async def _ensure_crawler(self) -> None:
        if not self._crawler_started:
            self._crawler = AsyncWebCrawler(
                config=BrowserConfig(headless=True, text_mode=True)
            )
            await self._crawler.__aenter__()
            self._crawler_started = True

    async def _close_crawler(self) -> None:
        if self._crawler_started and self._crawler is not None:
            await self._crawler.__aexit__(None, None, None)
            self._crawler_started = False
            self._crawler = None

    async def _process_link(
        self,
        url: str,
        depth: int,
        knowledge: Dict[str, str],
        visited: Set[str],
        subtask_desc: str,
    ) -> None:
        self.runtime_log_stream(
            f"check {url} depth={depth} scraped_links={list(self.scraped_links)}\n"
        )

        if depth > MAX_DEPTH or url in visited or url in self.scraped_links:
            if url in self.scraped_links:
                self.runtime_log_stream(
                    f"skip duplicate {url}; scraped_links={list(self.scraped_links)}\n"
                )
            elif url in visited:
                self.runtime_log_stream(f"already visited {url}\n")
            else:
                self.runtime_log_stream(f"max depth for {url}\n")
            return
        visited.add(url)
        self.runtime_log(f"Crawling {url}")
        try:
            if looks_like_document(url):
                text = await convert_doc_to_text(url)
                links = []
            elif looks_like_image(url):
                text = await vision_describe_image(url)
                links = []
            else:
                pages, links = await crawl_site(
                    url,
                    max_depth=0,
                    wait_for="css:body",
                    scan_full_page=True,
                    collect_links=True,
                    crawler=self._crawler,
                )
                text = pages[0][1] if pages else ""
        except Exception as exc:  # pragma: no cover - best effort
            logger.warning("[researcher] failed to scrape %s: %s", url, exc)
            return

        text = clean_text(text)
        existing = "\n\n".join(knowledge.values())
        relevant, extra_links = await evaluate_content(
            subtask_desc,
            existing,
            text,
        )
        self.runtime_log_stream(
            f"relevant={relevant} links={extra_links} for {url}\n",
        )
        self.scraped_links.add(url)
        self.runtime_log_stream(
            f"stored {url}; scraped_links={list(self.scraped_links)}\n"
        )
        follow_links = []
        if relevant:
            self.runtime_log_stream(
                f"filter links using scraped_links={list(self.scraped_links)}\n"
            )
            follow_links = filter_new_links(extra_links, self.scraped_links)
        if relevant:
            knowledge[url] = text
        self.runtime_log_stream(f"new_links={follow_links}\n")

        for link in follow_links:
            self.runtime_log_stream(f"follow {link}\n")
            await self._process_link(
                link,
                depth + 1,
                knowledge,
                visited,
                subtask_desc,
            )

        self.runtime_log_stream(
            f"end process {url}; scraped_links={list(self.scraped_links)}\n"
        )

    # ──────────────────────────────────────────────────
    #                       PUBLIC API
    # ──────────────────────────────────────────────────
    async def execute_task(self, task_details: Dict[str, Any]) -> None:
        self.current_task = task_details
        sub_id = task_details.get("subtask_id", "")
        sub_desc = task_details.get("subtask_description", "")
        await self._ensure_crawler()

        self.scraped_links.clear()
        self.runtime_log_stream(f"reset scraped links {list(self.scraped_links)}\n")

        knowledge: Dict[str, str] = {}
        visited: Set[str] = set()

        query = await generate_query(task_details)
        self.runtime_log(f"Query: {query}")
        urls = await search_urls(query, k=1)
        if urls:
            self.runtime_log_stream(
                f"filter links using scraped_links={list(self.scraped_links)}\n"
            )
            urls = filter_new_links(urls, self.scraped_links)
            if urls:
                url = urls[0]
                self.runtime_log(f"Initial link: {url}")
                await self._process_link(url, 1, knowledge, visited, sub_desc)
            else:
                self.runtime_log("Initial result already scraped")
        else:
            self.runtime_log("No results from Brave search")

        knowledge_text = "\n\n".join(knowledge.values())
        extra_queries = await get_additional_queries(
            knowledge_text,
            task_details,
        )
        if not extra_queries:
            self.runtime_log("Feedback: no additional queries")
        else:
            for q in extra_queries:
                self.runtime_log(f"Feedback query: {q}")
                new_urls = await search_urls(q, k=1)
                if not new_urls:
                    continue
                self.runtime_log_stream(
                    f"filter links using scraped_links={list(self.scraped_links)}\n"
                )
                new_urls = filter_new_links(new_urls, self.scraped_links)
                if not new_urls:
                    self.runtime_log_stream("feedback link already scraped\n")
                    continue
                link = new_urls[0]
                self.runtime_log(f"Feedback link: {link}")
                await self._process_link(link, 1, knowledge, visited, sub_desc)

        path = self._save_results(
            sub_id, task_details.get("user_message", ""), knowledge
        )
        self.report_results({"subtask_id": sub_id, "output_file": path})
        await self._close_crawler()

    # ──────────────────────────────────────────────────
    #                      INTERNALS
    # ──────────────────────────────────────────────────
    def _save_results(
        self,
        subtask_id: str,
        objective: str,
        kb: Dict[str, str],
    ) -> str:
        """Persist collected sources into knowledge_base markdown."""
        ts = datetime.utcnow().strftime("%Y%m%d%H%M%S")
        name = f"{subtask_id}_{ts}.md"
        out_dir = get_knowledge_base_dir(self.chat_id)
        os.makedirs(out_dir, exist_ok=True)
        fp = os.path.join(out_dir, name)

        body: List[str] = [
            f"# Subtask {subtask_id} – Research Report",
            "",
            f"## Objective\n\n{objective}",
            "",
            "## Sources",
        ]
        for url, content in kb.items():
            body.append(f"- **{url}**  (≈{len(content)//5} tokens kept)")
        body.append("\n---\n\n## Appendix (full text per source)\n")
        for url, content in kb.items():
            body.append(f"### {url}\n\n```\n{content}\n```\n")
        with open(fp, "w", encoding="utf-8") as f:
            f.write("\n".join(body))
        logger.info("[researcher] saved report → %s", fp)
        self.runtime_log_stream(f"saved report to {fp}\n")
        return fp

    def report_results(self, results: Dict[str, Any]) -> None:  # type: ignore
        send_message(
            self.chat_id,
            sender="worker",
            receiver="manager",
            message=results,
        )
        self.logger.info("[researcher] Reported %s", results.get("subtask_id"))
