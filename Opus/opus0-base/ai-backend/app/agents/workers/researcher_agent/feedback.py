# app/agents/workers/researcher_agent/feedback.py
"""
Module: feedback.py

Obtain follow-up search queries if current knowledge is insufficient.
"""

# ──────────────────────────────────────────────────
#                         IMPORTS
# ──────────────────────────────────────────────────
import json
import logging
from typing import Any, Dict, List

from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.messages import HumanMessage, SystemMessage

from app.core.config import settings

logger = logging.getLogger(__name__)

_model = ChatGoogleGenerativeAI(
    api_key=settings.GOOGLE_API_KEY,
    model="gemini-2.5-flash-preview-04-17",
    temperature=0.2,
)


# ──────────────────────────────────────────────────
#                         PUBLIC API
# ──────────────────────────────────────────────────
async def get_additional_queries(
    knowledge: str,
    task_ctx: Dict[str, Any],
) -> List[str]:
    """Return extra search queries or ``[]`` if nothing else is needed."""

    prompt = SystemMessage(
        "You review collected research notes. "
        "If they fully satisfy the subtask, "
        "reply with ['none']. Otherwise return a JSON list of search queries "
        "that would fill missing details."
    )
    human = HumanMessage(
        content=(
            f"User message: {task_ctx.get('user_message', '')}\n"
            f"Subtask: {task_ctx.get('subtask_description', '')}\n"
            f"Current knowledge:\n{knowledge[:4000]}"
        )
    )
    log_lines = [
        f"{type(m).__name__}: {getattr(m, 'content', m)}"
        for m in [
            prompt,
            human,
        ]
    ]
    message_log = "\n".join(log_lines)
    logger.info("[researcher.feedback] LLM input messages:\n%s", message_log)
    raw = await _model.ainvoke([prompt, human])
    reply = raw.content.strip()
    if reply.startswith("```"):
        reply = reply.strip("` \n")
    if reply.lower().startswith("json"):
        reply = reply[4:].lstrip()
    try:
        data = json.loads(reply)
        if data == ["none"]:
            return []
        if isinstance(data, list):
            return [str(q) for q in data if isinstance(q, str)]
        return []
    except Exception:  # pragma: no cover - best effort
        logger.warning("[researcher.feedback] invalid response: %s", reply)
        return []
