# app/agents/workers/researcher_agent/content_handlers.py
"""
Convert docs, images, etc. into plain markdown strings.
"""

import io
import httpx
import base64
import logging
from app.utils.document_reader import (
    parse_pdf,
    parse_docx,
    parse_pptx,
    parse_csv,
)
from app.core.config import settings
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.messages import HumanMessage


logger = logging.getLogger(__name__)


async def convert_doc_to_text(url: str) -> str:
    """Download document at ``url`` and return markdown."""
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            resp = await client.get(url)
    except Exception as exc:  # pragma: no cover - best effort
        logger.warning(
            "[researcher.content_handlers] fetch %s failed: %s",
            url,
            exc,
        )
        return ""

    resp.raise_for_status()
    suffix = url.split("?")[0].split("#")[0].split(".")[-1].lower()
    buf = io.BytesIO(resp.content)

    if suffix == "pdf":
        return parse_pdf(buf)  # PyMuPDF can read bytes-like
    if suffix in ("doc", "docx"):
        return parse_docx(buf)
    if suffix in ("ppt", "pptx"):
        return parse_pptx(buf)
    if suffix == "csv":
        return parse_csv(buf)

    # fallback raw
    return resp.text


async def vision_describe_image(url: str) -> str:
    """
    Call Gemini Vision (2.5-flash) to describe an image. Returns markdown.
    """
    model = ChatGoogleGenerativeAI(
        model="gemini-2.5-flash-preview-04-17", api_key=settings.GOOGLE_API_KEY
    )
    img_bytes = httpx.get(url, timeout=15.0).content
    data_uri = "data:image/jpeg;base64," + base64.b64encode(img_bytes).decode()
    msg = HumanMessage(
        content=[
            {"type": "text", "text": "Please describe this image in detail."},
            {"type": "image_url", "image_url": {"url": data_uri}},
        ]
    )
    logger.info(
        "[researcher.content_handlers] LLM input messages:\n%s",
        f"HumanMessage: {msg.content}",
    )
    return model.invoke([msg]).content.strip()
