# app/agents/workers/researcher_agent/brave_client.py
"""
Module: brave_client.py

Async wrapper for the Brave Search API used by ResearcherAgent.
"""

from __future__ import annotations

import asyncio
import logging
from typing import List

import httpx

from app.core.config import settings

logger = logging.getLogger(__name__)

BASE_URL = "https://api.search.brave.com/res/v1/web/search"


SEM = asyncio.Semaphore(5)


async def search_urls(query: str, *, k: int = 10, retries: int = 3) -> List[str]:
    """Return up to ``k`` URLs from Brave Search with basic retry logic."""
    if not settings.BRAVE_SEARCH_API:
        err = "BRAVE_SEARCH_API key is required for Brave Search"
        logger.error(err)
        raise RuntimeError(err)

    headers = {
        "Accept": "application/json",
        "Accept-Encoding": "gzip",
        "X-Subscription-Token": settings.BRAVE_SEARCH_API,
    }
    params = {
        "q": query,
        "count": k,
        "safesearch": "off",
        "result_filter": "web",
    }

    async with SEM, httpx.AsyncClient(timeout=15.0) as client:
        for attempt in range(retries):
            try:
                resp = await client.get(BASE_URL, headers=headers, params=params)
                logger.debug("[brave] %s -> %s", query, resp.status_code)
                if resp.status_code == 429 and attempt < retries - 1:
                    await asyncio.sleep(2**attempt)
                    continue
                resp.raise_for_status()
                data = resp.json()
                break
            except (httpx.HTTPError, ValueError) as exc:
                if attempt < retries - 1:
                    await asyncio.sleep(2**attempt)
                    continue
                logger.error("[brave] request failed: %s", exc)
                return []

    results = data.get("web", {}).get("results", [])
    urls = [item.get("url") for item in results if isinstance(item.get("url"), str)]
    return urls[:k]
