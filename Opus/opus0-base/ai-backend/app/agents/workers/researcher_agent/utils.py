# app/agents/workers/researcher_agent/utils.py
"""
Common helpers: paths, semaphores, tiny text utilities.
"""

import asyncio
import re
from pathlib import Path
from typing import Iterable, List, Dict, Any, Awaitable, List as TList, TypeVar, Set
from app.utils.constants import DATA_DIR
from app.utils.constants import get_session_dir  # ↔ existing helper


# ---------- path helpers -------------------------------------------------- #
def get_temp_dir(chat_id: str) -> Path:
    """Return …/data/sessions/<chatID>/temp  (mkdir if needed)."""
    temp = get_session_dir(chat_id) / "temp"
    temp.mkdir(parents=True, exist_ok=True)
    return temp


# ---------- relevance / cleanup helpers ----------------------------------- #
def looks_like_document(url: str) -> bool:
    return re.search(r"\.(pdf|docx?|pptx?|csv)(?:\?|$)", url, re.I) is not None


def looks_like_image(url: str) -> bool:
    return re.search(r"\.(png|jpe?g|gif|webp)(?:\?|$)", url, re.I) is not None


def dedupe(seq: Iterable[str]) -> List[str]:
    seen: Dict[str, None] = {}
    return [x for x in seq if not (x in seen or seen.setdefault(x, None))]


# ---------- async helpers --------------------------------------------------- #
T = TypeVar("T")


async def gather_in_batches(
    tasks: Iterable[Awaitable[T]], batch_size: int = 5
) -> TList[T]:
    """Run awaitables in batches with a maximum concurrency of ``batch_size``."""

    results: TList[T] = []
    task_list = list(tasks)
    for i in range(0, len(task_list), batch_size):
        batch = task_list[i : i + batch_size]
        results.extend(await asyncio.gather(*batch))
    return results


def filter_new_links(links: Iterable[str], scraped: Set[str]) -> List[str]:
    """Return only URLs from ``links`` not present in ``scraped``."""

    return [url for url in links if url not in scraped]
