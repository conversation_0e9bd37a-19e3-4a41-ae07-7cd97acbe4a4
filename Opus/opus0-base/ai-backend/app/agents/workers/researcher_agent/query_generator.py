# app/agents/workers/researcher_agent/query_generator.py
"""
Module: query_generator.py

Generate a single web search query from subtask context.
"""

# ──────────────────────────────────────────────────
#                         IMPORTS
# ──────────────────────────────────────────────────
import logging
from typing import Any, Dict

from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.messages import HumanMessage, SystemMessage

from app.core.config import settings

logger = logging.getLogger(__name__)

_model = ChatGoogleGenerativeAI(
    api_key=settings.GOOGLE_API_KEY,
    model="gemini-2.5-flash-preview-04-17",
    temperature=0.3,
)


# ──────────────────────────────────────────────────
#                         PUBLIC API
# ──────────────────────────────────────────────────
async def generate_query(task_ctx: Dict[str, Any]) -> str:
    """Return one concise search query for the current subtask."""

    prompt = SystemMessage(
        "You are an expert researcher. "
        "Craft ONE web-search query for the task. "
        "Respond only with the plain query string without additional text."
    )
    human = HumanMessage(
        (
            f"User message: {task_ctx.get('user_message', '')}\n"
            f"Subtask: {task_ctx.get('subtask_description', '')}\n"
            f"Additional info: {task_ctx.get('deps_info', '(none)')}\n"
            f"History: {task_ctx.get('history', '(none)')}"
        )
    )
    logger.info(
        "[researcher.query_generator] LLM input messages:\n%s",
        "\n".join(
            f"{type(m).__name__}: {getattr(m, 'content', m)}" for m in [prompt, human]
        ),
    )
    raw = await _model.ainvoke([prompt, human])
    query = raw.content.strip().strip("` ")
    # If output has multiple lines, take first
    return query.splitlines()[0] if query else ""
