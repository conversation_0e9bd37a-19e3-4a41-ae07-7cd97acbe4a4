# app/agents/workers/researcher_agent/crawl_agent.py
"""Simple Crawl4AI wrapper used by the ResearcherAgent."""

from __future__ import annotations

import logging
from typing import List, Optional, Tuple

from crawl4ai import AsyncWebCrawler
from crawl4ai.async_configs import <PERSON><PERSON>er<PERSON>onfig, CrawlerRunConfig, CacheMode
from crawl4ai.markdown_generation_strategy import DefaultMarkdownGenerator
from crawl4ai.content_filter_strategy import PruningContentFilter
from crawl4ai.deep_crawling import BestFirstCrawlingStrategy
from crawl4ai.deep_crawling.scorers import KeywordRelevanceScorer

logger = logging.getLogger(__name__)


async def crawl_site(
    url: str,
    *,
    keywords: Optional[List[str]] = None,
    max_depth: int = 1,
    max_pages: int = 20,
    wait_for: Optional[str] = "css:body",
    js_code: Optional[str] = None,
    scan_full_page: bool = False,
    filter_threshold: float = 0.45,
    collect_links: bool = False,
    crawler: Optional[AsyncWebCrawler] = None,
) -> Tuple[List[Tuple[str, str]], List[str]]:
    """Crawl a site and return ``[(url, markdown)]`` tuples and discovered links.

    Parameters
    ----------
    crawler : Optional[AsyncWebCrawler]
        Existing crawler to reuse. If ``None`` a temporary crawler is created.
    """

    browser = BrowserConfig(headless=True, text_mode=True)
    md_generator = DefaultMarkdownGenerator(
        content_filter=PruningContentFilter(threshold=filter_threshold),
        options={"ignore_links": False},
    )
    strategy = BestFirstCrawlingStrategy(
        max_depth=max_depth,
        include_external=False,
        max_pages=max_pages,
        url_scorer=KeywordRelevanceScorer(keywords or []),
    )
    run_cfg = CrawlerRunConfig(
        markdown_generator=md_generator,
        deep_crawl_strategy=strategy,
        cache_mode=CacheMode.BYPASS,
        stream=False,
        verbose=False,
        wait_for=wait_for,
        js_code=js_code,
        scan_full_page=scan_full_page,
    )

    if crawler is None:
        async with AsyncWebCrawler(config=browser) as _crawler:
            results = await _crawler.arun(url, config=run_cfg)
    else:
        results = await crawler.arun(url, config=run_cfg)

    cleaned: List[Tuple[str, str]] = []
    links: List[str] = []
    for res in results:
        if not res.success:
            logger.debug("crawl failed for %s", res.url)
            continue
        text = ""
        if res.markdown:
            text = res.markdown.fit_markdown or res.markdown.raw_markdown
        cleaned.append((res.url, text))
        if collect_links and res.links:
            links.extend(l.get("href", l) for l in res.links.get("internal", []) if l)
    return cleaned, links


async def scrape_url(
    url: str,
    *,
    wait_for: Optional[str] = "css:body",
    js_code: Optional[str] = None,
    scan_full_page: bool = False,
    filter_threshold: float = 0.45,
    crawler: Optional[AsyncWebCrawler] = None,
) -> str:
    """Return markdown from a single page using Crawl4AI.

    Parameters
    ----------
    crawler : Optional[AsyncWebCrawler]
        Existing crawler to reuse. If ``None`` a temporary crawler is created.
    """
    pages, _ = await crawl_site(
        url,
        max_depth=0,
        wait_for=wait_for,
        js_code=js_code,
        scan_full_page=scan_full_page,
        filter_threshold=filter_threshold,
        crawler=crawler,
    )
    if not pages:
        return ""
    return pages[0][1]
