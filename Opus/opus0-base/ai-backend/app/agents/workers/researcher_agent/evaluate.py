# app/agents/workers/researcher_agent/evaluate.py
"""
Module: evaluate.py

LLM-based assessment of scraped content for relevance and follow-up links.
"""

# ──────────────────────────────────────────────────
#                         IMPORTS
# ──────────────────────────────────────────────────
import json
import logging
from typing import List, Tuple

from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.messages import HumanMessage, SystemMessage

from app.core.config import settings

logger = logging.getLogger(__name__)

_model = ChatGoogleGenerativeAI(
    api_key=settings.GOOGLE_API_KEY,
    model="gemini-2.5-flash-preview-04-17",
    temperature=0.2,
)


# ──────────────────────────────────────────────────
#                         PUBLIC API
# ──────────────────────────────────────────────────
async def evaluate_content(
    subtask_desc: str,
    previous: str,
    text: str,
) -> Tuple[bool, List[str]]:
    """Return ``(relevant, links)`` for given ``text``.

    The LLM analyses ``text`` in the context of ``previous`` knowledge and the
    ``subtask_desc``. It responds whether the scrape contains new useful
    information and lists any follow-up links worth crawling.
    """

    MAX_CHARS = 1_250_000  # approx 250k tokens

    prev_text = previous if len(previous) <= MAX_CHARS else previous[:MAX_CHARS]
    curr_text = text if len(text) <= MAX_CHARS else text[:MAX_CHARS]

    messages = [
        SystemMessage(
            "You examine crawled web content. Respond only with JSON: "
            "[yes_or_no, <links>...] where yes_or_no is 'yes' or 'no'. "
            "Answer 'yes' only if the current scrape adds new details "
            "for the subtask. Provide follow-up links only if they seem "
            "important to explore based on the subtask description and "
            "the provided page text."
        ),
        HumanMessage(
            content=(
                f"Subtask description:\n{subtask_desc}\n\n"
                f"Existing notes:\n{prev_text}\n\n"
                f"Current scrape:\n{curr_text}"
            )
        ),
    ]

    message_log = "\n".join(
        f"{type(m).__name__}: {getattr(m, 'content', m)}" for m in messages
    )
    logger.info("[researcher.evaluate] LLM input messages:\n%s", message_log)
    raw = await _model.ainvoke(messages)
    reply = raw.content.strip()
    if reply.startswith("```"):
        reply = reply.strip("` \n")
    if reply.lower().startswith("json"):
        reply = reply[4:].lstrip()
    try:
        data = json.loads(reply)
        if not isinstance(data, list):
            raise ValueError("invalid format")
        answer = str(data[0]).lower() if data else "no"
        links = [str(u) for u in data[1:] if isinstance(u, str)]
        return answer.startswith("y"), links
    except Exception:  # pragma: no cover - best effort
        logger.warning("[researcher.evaluate] invalid response: %s", reply)
        return False, []
