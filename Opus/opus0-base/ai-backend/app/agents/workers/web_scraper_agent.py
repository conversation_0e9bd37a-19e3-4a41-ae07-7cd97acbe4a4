# app/agents/workers/web_scraper_agent.py
"""
Module: web_scraper_agent.py

This module defines the WebScraperAgent class, which generates and executes Tavily search queries
in parallel, aggregates the results, writes them to a Markdown file, and reports completion to the manager.
The agent uses <PERSON><PERSON><PERSON><PERSON> to generate search specs and Tavily for efficient AI-optimized search.
"""

# ──────────────────────────────────────────────────
#                         IMPORTS
# ──────────────────────────────────────────────────
import os
import json
import asyncio
import logging
import re
import base64
from pathlib import Path
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple

from rich.console import Console
from tavily import TavilyClient
from langchain_google_genai import ChatGoogleGenerativeA<PERSON>
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser
from langchain_core.messages import SystemMessage, HumanMessage
from app.utils.token_counter import count_tokens

from app.agents.base_agent import BaseAgent
from app.core.config import settings
from app.utils.constants import get_knowledge_base_dir
from app.utils.communication import send_message

# ──────────────────────────────────────────────────
#                         CONSTANTS & CLIENTS
# ──────────────────────────────────────────────────
logger = logging.getLogger(__name__)
console = Console()

# Tavily client (API key loaded from .env via settings)
tavily_client = TavilyClient(api_key=settings.TAVILY_API_KEY)

# ──────────────────────────────────────────────────
#                      PROMPT CHAIN
# ──────────────────────────────────────────────────
query_model = ChatGoogleGenerativeAI(
    api_key=settings.GOOGLE_API_KEY,
    model="gemini-2.0-flash",
    temperature=0.0,
)

# We added {history} and {uploaded_images} placeholders here:
query_prompt = ChatPromptTemplate.from_template(
    """
You are a query generator for Tavily Search. Given the user's chat history, subtask description,
context (including any uploaded images), and dependencies, produce between 1 and 5 search entries.
Basic search is 1 query, complex research requires 5 queries. Each basic query costs 1 credit and
advanced queries cost 2 credits. We are looking for a balance between cost and output quality.
Each entry is a list with:
  [query_string, search_depth, topic, time_range, days, include_images, include_answer]

Valid options:
  - search_depth: "basic" or "advanced"
  - topic: "general" or "news"
  - time_range: one of null, "day", "week", "month", "year"
  - days: integer ≥1 (only if time_range is not null)
  - include_images, include_answer: boolean

Output **only** valid JSON. Example:
[
  ["latest AI breakthroughs", "basic", "general", null, 7, false, false],
  ["AI news today", "advanced", "news", "day", 1, false, true]
]

### Chat History:
{history}

### Uploaded Images:
{uploaded_images}

### Subtask Description:
{subtask_desc}

### Dependencies Info:
{deps_info}

OUTPUT:
"""
)

query_chain = query_prompt | query_model | StrOutputParser()


# ──────────────────────────────────────────────────
#                     AGENT CLASS
# ──────────────────────────────────────────────────
class WebScraperAgent(BaseAgent):  # type: ignore
    """
    Uses Tavily to search multiple generated queries in parallel,
    then writes all results to a single Markdown file and reports it.
    """

    def __init__(self, agent_id: str, chat_id: str) -> None:
        super().__init__(agent_id, agent_type="Search tool")
        self.chat_id = chat_id
        os.makedirs(get_knowledge_base_dir(chat_id), exist_ok=True)
        self._latest_results: List[Dict[str, Any]] = []

    def _build_messages(
        self,
        *,
        history: str,
        user_input: str,
        subtask_description: str,
        deps_info: str,
        uploaded_images: List[str],
    ) -> List[Any]:
        """Construct prompt messages for query generation."""
        system_lines = [
            "You are a query generator for Tavily Search.",
            "Use the provided context to craft between 1 and 5 search entries.",
            "Each entry: [query, search_depth, topic, time_range, days, include_images, include_answer]",
            "",
            "Chat History:",
            history or "(none)",
            "",
            f"Main Task: {user_input}",
            f"Subtask: {subtask_description}",
            "",
            f"Additional info: {deps_info}",
            "",
            f"Uploaded images: {', '.join(uploaded_images) if uploaded_images else '(none)'}",
            "",
            "Respond only with the JSON array.",
        ]
        system_msg = SystemMessage(content="\n".join(system_lines).strip())
        human_msg = HumanMessage(content="Provide the JSON array of query specs")
        return [system_msg, human_msg]

    async def _invoke_stream(self, messages: List[Any], *, stream: bool = True) -> str:
        """Send ``messages`` to the query model and accumulate the response."""
        logger.info(
            "[web_scraper] LLM input messages:\n%s",
            "\n".join(
                f"{type(m).__name__}: {getattr(m, 'content', m)}" for m in messages
            ),
        )

        chunks: List[str] = []
        if stream:
            self.runtime_log_stream("stream")

        async for chunk in query_model.astream(messages):
            text = chunk.content if hasattr(chunk, "content") else str(chunk)
            if stream:
                self.runtime_log_stream(text)
            chunks.append(text)

        if stream:
            self.runtime_log_stream("stream")

        return "".join(chunks).strip()

    async def _generate_base_response(self, base_messages: List[Any]) -> str:
        """Generate search results and return them as Markdown."""

        last = base_messages[-1].content if base_messages else ""
        improvement = ""
        if "Improvements to apply:" in last:
            improvement = last.split("Improvements to apply:")[-1]
            improvement = improvement.split("Revised response:")[0].strip()

        td = self.current_task or {}
        desc = td.get("subtask_description", "")
        if improvement:
            desc = f"{desc}\n{improvement}"
        deps = td.get("deps_info", "")
        uploads = self.normalize_uploads(td.get("uploaded_images", []))
        uploads = [u for u in uploads if not u.lower().endswith(".md")]
        history = td.get("history", "")

        specs = await self._generate_queries(desc, deps, uploads, history)

        semaphore = asyncio.Semaphore(5)

        async def _run(q: List[Any]) -> Dict[str, Any]:
            async with semaphore:
                return await self._run_single_search(q)

        results: List[Dict[str, Any]] = await asyncio.gather(*(_run(q) for q in specs))
        self._latest_results = results

        md = self._format_results_markdown(td.get("subtask_id", ""), desc, results)
        out_tokens = count_tokens(md)
        self.log_output_tokens(query_model.model, out_tokens)
        return md

    async def _feedback_loop(
        self,
        *,
        base_messages: List[Any],
        subtask_description: str,
        initial_response: str,
        max_rounds: int = 5,
    ) -> Tuple[str, List[Dict[str, str]]]:
        """Iteratively refine ``initial_response`` up to ``max_rounds``.

        Returns the final response and a list of refinement dictionaries with
        keys ``agent``, ``instruction`` and ``output``.
        """

        response = initial_response
        refinements: List[Dict[str, str]] = []

        for round_no in range(1, max_rounds + 1):
            review_msgs = [
                SystemMessage(
                    "You are the feedback reviewer for the Web Search agent. "
                    "Return a JSON array of two strings. The first string must "
                    "be 'web'. The second string describes new details the web "
                    "agent should search for to enhance the results. Use short "
                    "phrases like a missing fact or updated statistics. If no "
                    "refinement or extra search is required, reply with ['web', 'none']. "
                    "Respond only with the JSON array."
                    "IMPORTANT: Doing an extra search is costly. Only do refinements when necessary like in case of incomplete information/not meeting the task"
                ),
                HumanMessage(
                    content=(
                        f"Past history and information provided for context: {base_messages}\n\n---\n\n"
                        f"Again, this is the main subtask we need to focus on: {subtask_description}\n\n---\n\n"
                        f"Current Output (output given by the current agent for the subtask):\n{response}\n"
                        "Provide the JSON array:"
                    )
                ),
            ]
            logger.info("[web_scraper] review round %d", round_no)
            review = await self._invoke_stream(review_msgs, stream=False)
            suggestions = review.strip()
            logger.info("[web_scraper] review response: %s", suggestions)

            clean = re.sub(r"^```(?:json)?\s*", "", suggestions)
            clean = re.sub(r"\s*```$", "", clean).strip()
            try:
                agent_choice, improvement = json.loads(clean)
            except Exception:
                logger.warning("[web_scraper] invalid feedback format: %s", suggestions)
                break
            if not isinstance(agent_choice, str) or not isinstance(improvement, str):
                logger.warning("[web_scraper] malformed feedback: %s", suggestions)
                break
            self.runtime_log_stream(f"\n\n---Ref:{improvement}---\n\n")

            if improvement.lower() in {"none", "none.", "'none'", '"none"'}:
                break

            # Feedback for the web agent always triggers another web search
            target = WebScraperAgent(f"{self.agent_id}_web_fb", self.chat_id)
            target.current_task = self.current_task

            response = await self.apply_feedback(
                target_agent=target,
                feedback=improvement,
                previous_output=response,
                task_details=self.current_task or {},
                past_refinements=refinements,
            )
            refinements.append(
                {
                    "agent": agent_choice,
                    "instruction": improvement,
                    "output": response,
                }
            )
            logger.info(
                "[web_scraper] round %d refined output: %s",
                round_no,
                response[:200],
            )
            out_tokens = count_tokens(response)
            self.log_output_tokens(query_model.model, out_tokens)

        return response, refinements

    def _format_results_markdown(
        self, subtask_id: str, desc: str, results: List[Dict[str, Any]]
    ) -> str:
        """Return search ``results`` formatted as Markdown."""

        lines = [f"# Subtask {subtask_id}", "", f"**Description:** {desc}", ""]
        for entry in results:
            spec = entry.get("spec", [])
            resp = entry.get("response", {})
            q = spec[0] if spec else ""
            lines.append("---")
            lines.append(f"## Query: {q}")
            if len(spec) > 2:
                lines.append(f"- Depth: {spec[1]}, Topic: {spec[2]}")
            if len(spec) > 3 and spec[3]:
                lines.append(f"- Time range: {spec[3]} last {spec[4]} days")
            if len(spec) > 5 and spec[5]:
                lines.append("- Included images")
            if len(spec) > 6 and spec[6]:
                lines.append("- Included answer")
            lines.append("\n### Results:")
            for r in resp.get("results", []):
                title = r.get("title", "")
                url = r.get("url", "")
                cont = r.get("content", "")
                lines.append(f"- [{title}]({url}) — {cont}")
            if resp.get("answer"):
                lines.append(f"\n**Tavily answer:** {resp['answer']}")
            lines.append("")

        return "\n".join(lines)

    async def execute_task(self, task_details: Dict[str, Any]) -> None:
        """Generate queries, run searches, and report results."""
        try:
            self.start_timer("web agent")
            self.current_task = task_details

            subtask_id: str = task_details.get("subtask_id")  # type: ignore
            desc: str = task_details.get("subtask_description", "")
            user_input: str = task_details.get("user_message", "")
            deps: str = task_details.get("deps_info", "")

            uploads_raw = task_details.get("uploaded_images", [])
            all_uploads = self.normalize_uploads(uploads_raw)
            uploaded_images = [f for f in all_uploads if not f.lower().endswith(".md")]
            if len(uploaded_images) != len(all_uploads):
                logger.info(
                    f"[WebScraperAgent] Dropped .md from uploads; remaining images={uploaded_images}"
                )

            history_str: str = task_details.get("history", "")

            messages = self._build_messages(
                history=history_str,
                user_input=user_input,
                subtask_description=desc,
                deps_info=deps,
                uploaded_images=uploaded_images,
            )

            print_stage("WebScraperAgent: Start")

            base_output = await self._generate_base_response(messages)

            response, refinements = await self._feedback_loop(
                base_messages=messages,
                subtask_description=desc,
                initial_response=base_output,
            )

            out_path: str = self._save_to_markdown(
                subtask_id, desc, base_output, refinements
            )
            logger.info(f"[WebScraperAgent] Results saved to {out_path}")

            print_stage("WebScraperAgent: Done")
            send_message(
                self.chat_id,
                sender="web_scraper",
                receiver="manager",
                message={"subtask_id": subtask_id, "output_file": out_path},
            )
            logger.info(f"[WebScraperAgent] Reported results for subtask {subtask_id}")

            self.stop_timer("web agent")

        except Exception as err:
            logger.error(f"[WebScraperAgent] Execution failed: {err}", exc_info=True)
            self.handle_error(err)

    # ──────────────────────────────────────────────────
    #                     HELPERS
    # ──────────────────────────────────────────────────
    async def _generate_queries(
        self,
        desc: str,
        deps: str,
        uploaded_images: List[str],
        history: str,
    ) -> List[List[Any]]:
        """
        Generate search specs via LLM chain and parse JSON output.

        Now includes `history` and `uploaded_images` when formatting the prompt.

        Returns:
            specs: List of query parameter lists.
        """
        raw: Optional[str] = None
        clean: Optional[str] = None

        try:
            if not uploaded_images:
                # Text‐only chain: pass history and empty images string
                filled = query_prompt.format_prompt(
                    history=history,
                    uploaded_images="",
                    subtask_desc=desc,
                    deps_info=deps,
                ).to_string()
                in_toks = count_tokens(filled)
                self.log_input_tokens(query_model.model, in_toks)

                raw = await query_chain.ainvoke(
                    {
                        "history": history,
                        "uploaded_images": "",
                        "subtask_desc": desc,
                        "deps_info": deps,
                    }
                )

            else:
                # ── Multimodal branch (images‐only since .md were dropped earlier) ──
                try:
                    # Include history and image filenames in the SystemMessage
                    system = SystemMessage(
                        content=f"""
You are a query generator for Tavily Search. Given the user's subtask description, context,
and chat history, produce between 1 and 5 search entries. Basic search is 1 query, complex research requires 5 queries.
Each basic query costs 1 credit and advanced queries cost 2 credits. We are looking for a balance between cost and output quality.

Chat History:
{history}

Uploaded Images: {', '.join(uploaded_images)}

Each entry is a list with:
[query_string, search_depth, topic, time_range, days, include_images, include_answer]

Valid options:
- search_depth: "basic" or "advanced"
- topic: "general" or "news"
- time_range: one of null, "day", "week", "month", "year"
- days: integer ≥1 (only if time_range is not null)
- include_images, include_answer: boolean

Output **only** valid JSON. Example:
[
  ["latest AI breakthroughs", "basic", "general", null, 7, false, false],
  ["AI news today", "advanced", "news", "day", 1, false, true]
]

### Subtask Description:
{desc}

### Dependencies Info:
{deps}

OUTPUT:
""".strip()
                    )

                    # Build a HumanMessage that reminds it to output JSON
                    human_imgs = self.build_image_message(uploaded_images)
                    human_text = HumanMessage(
                        content="""
Finally, output **only** a JSON array of query specs, for example:
[
  ["my query", "basic", "general", null, 7, false, false]
]
""".strip()
                    )

                    # Combine into a small two‐message chain
                    prompt = ChatPromptTemplate.from_messages(
                        [system, human_imgs, human_text]
                    )
                    raw = await (prompt | query_model | StrOutputParser()).ainvoke({})

                except Exception as multimodal_err:
                    logger.error(
                        f"[WebScraperAgent] multimodal branch failed: {multimodal_err}"
                    )
                    return []

            # Strip backticks if any, then count tokens & parse JSON
            stripped = re.sub(r"^```(?:json)?\s*|\s*```$", "", raw or "").strip()
            out_toks = count_tokens(stripped)
            self.log_output_tokens(query_model.model, out_toks)

            clean = re.sub(r"^```(?:json)?\s*", "", raw or "")
            clean = re.sub(r"\s*```$", "", clean or "").strip()
            specs: List[List[Any]] = json.loads(clean)
            return specs

        except json.JSONDecodeError as e:
            logger.error(
                f"[WebScraperAgent] JSON parse error in query specs: {clean or raw}, error: {e}"
            )
            return []
        except Exception as e:
            logger.error(
                f"[WebScraperAgent] Unexpected error generating queries: {e}",
                exc_info=True,
            )
            return []

    async def _run_single_search(self, qspec: List[Any]) -> Dict[str, Any]:
        """
        Execute a single Tavily search with validated parameters.

        Args:
            qspec: [query, depth, topic, time_range, days, include_images, include_answer]

        Returns:
            Dict containing spec and response dict.
        """
        try:
            query, depth, topic, time_range, days, include_images, include_answer = qspec  # type: ignore

            # Validate
            if depth not in ("basic", "advanced"):
                logger.warning(
                    f"[WebScraperAgent] Invalid search_depth '{depth}', defaulting to 'basic'"
                )
                depth = "basic"

            if topic not in ("general", "news"):
                logger.warning(
                    f"[WebScraperAgent] Invalid topic '{topic}', defaulting to 'general'"
                )
                topic = "general"

            valid_ranges = {None, "day", "week", "month", "year"}
            if time_range not in valid_ranges or (
                time_range and (not isinstance(days, int) or days < 1)
            ):
                logger.warning(
                    f"[WebScraperAgent] Invalid time_range '{time_range}' or days '{days}', ignoring"
                )
                time_range = None
                days = None

            params: Dict[str, Any] = {
                "query": query,
                "search_depth": depth,
                "topic": topic,
                "max_results": 5,
                "include_images": bool(include_images),
                "include_answer": bool(include_answer),
            }
            if time_range:
                params["time_range"] = time_range  # type: ignore
                params["days"] = days  # type: ignore

            self.runtime_log(f"Searching web for “{query}”")
            logger.debug(f"[WebScraperAgent] Tavily search params: {params}")

            loop = asyncio.get_event_loop()
            response: Dict[str, Any] = await loop.run_in_executor(
                None, lambda: tavily_client.search(**params)
            )
            logger.debug(
                f"[WebScraperAgent] Received {len(response.get('results', []))} results for '{query}'"
            )
            return {"spec": qspec, "response": response}

        except Exception as e:
            logger.error(
                f"[WebScraperAgent] Error during search for spec {qspec}: {e}",
                exc_info=True,
            )
            return {"spec": qspec, "response": {"results": []}}

    def _save_to_markdown(
        self,
        subtask_id: str,
        desc: str,
        initial_output: str,
        refinements: List[Dict[str, str]],
    ) -> str:
        """
        Persist the web search results and all refinements to a Markdown file.

        Returns:
            Path to output Markdown file.
        """
        timestamp = datetime.utcnow().strftime("%Y%m%d%H%M%S")
        filename = f"web_scraper_{subtask_id}_{timestamp}.md"
        kb_dir = get_knowledge_base_dir(self.chat_id)
        path = os.path.join(kb_dir, filename)
        try:
            with open(path, "w", encoding="utf-8") as md:
                md.write("---\n\n")
                md.write(f"SUBTASK {subtask_id} STARTS\n\n")
                md.write("---\n")
                md.write(f"# Subtask {subtask_id}: {desc}\n")
                md.write("## Output:\n")
                md.write(f"{initial_output}\n")
                for ref in refinements:
                    md.write("## Refinement:\n")
                    md.write(
                        f"### {ref.get('agent', '')} - {ref.get('instruction', '')}\n"
                    )
                    md.write(f"{ref.get('output', '')}\n")
                md.write("---\n\n")
                md.write(f"SUBTASK {subtask_id} COMPLETED\n\n")
                md.write("---")
            logger.info(f"[WebScraperAgent] Markdown file created at {path}")
        except Exception as e:
            logger.error(
                f"[WebScraperAgent] Failed to write markdown file {path}: {e}",
                exc_info=True,
            )
        return path


# ──────────────────────────────────────────────────
#                         UTILS
# ──────────────────────────────────────────────────
def print_stage(stage: str) -> None:
    """Notify frontend of a new inference stage."""
    console.print(f"[bold green]{stage}[/]")
