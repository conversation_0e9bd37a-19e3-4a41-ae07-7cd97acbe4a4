# app/agents/workers/reasoning_worker_agent.py
"""
Module: reasoning_worker_agent.py

This module defines the ReasoningWorkerAgent class, a worker agent that processes subtasks requiring high-order
thinking, logical reasoning, and mathematical skills using Gemini (ChatGoogleGenerativeAI). The agent generates a
response based on the subtask description, full chat history (user+assistant), and any additional dependency
information, saves the result as a markdown (.md) file in the knowledge base, and reports task completion back to
the manager.
"""

import mimetypes
import os
import re
import logging
import json
from datetime import datetime
from typing import Dict, Any, List, TYPE_CHECKING, Tuple
import base64
from pathlib import Path

from langchain_core.messages import SystemMessage, HumanMessage
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.output_parsers import StrOutputParser

from app.utils.constants import get_knowledge_base_dir, get_user_uploads_dir
from app.agents.base_agent import BaseAgent
from app.core.config import settings
from app.utils.communication import send_message
from app.utils.token_counter import count_tokens

from app.agents.workers.llm_worker_agent import LLMWorkerAgent

if TYPE_CHECKING:  # avoid circular import during module load
    from app.agents.workers.llm_worker_agent import LLMWorkerAgent
    from app.agents.workers.web_scraper_agent import WebScraperAgent

# Initialize module-level logger
logger = logging.getLogger(__name__)

# Instantiate Gemini models once at module load.
gpt4_pro_model = ChatGoogleGenerativeAI(
    api_key=settings.GOOGLE_API_KEY,
    model="gemini-2.5-pro-preview-06-05",
    temperature=0.2,  # slightly lower temperature for reasoning
)

# Separate flash model used for the feedback loop
gpt4_flash_model = ChatGoogleGenerativeAI(
    api_key=settings.GOOGLE_API_KEY,
    model="gemini-2.5-flash-preview-04-17",
    temperature=0.2,
)


class ReasoningWorkerAgent(BaseAgent):
    """
    Worker agent capable of processing subtasks that require reasoning and logical analysis using Gemini.

    Inherits from BaseAgent and implements task‐specific logic to:
      1. Generate a response for a given reasoning subtask (with or without images).
      2. Save the generated response to a markdown file in the knowledge base.
      3. Report task completion back to the manager.
    """

    def __init__(self, agent_id: str, chat_id: str):
        """
        Initializes the ReasoningWorkerAgent with a unique agent ID.

        Args:
            agent_id (str): Unique identifier for the reasoning worker agent.
        """
        super().__init__(agent_id, agent_type="Reasoning Tool")
        self.chat_id = chat_id

    async def execute_task(self, task_details: Dict[str, Any]):
        """
        Executes the given subtask by generating a response using Gemini and saving the result.

        Steps:
          1. Extract subtask details from the provided task_details dict.
          2. Build a pair of messages (SystemMessage + HumanMessage) via `_build_messages(...)`.
          3. Send those messages to Gemini (await `_invoke_stream(...)`).
          4. Save the result as a markdown file.
          5. Report the subtask ID + output file back to the manager.

        Args:
            task_details (Dict[str, Any]): Dictionary containing:
                - "subtask_id": Unique subtask identifier.
                - "subtask_description": Description of the subtask.
                - "user_message": The main user input.
                - "deps_info": (Optional) Additional dependency information.
                - "uploaded_images": list of filenames (strings).
                - "history": a single string of "role: content" lines (user+assistant).
        """
        try:
            self.start_timer("reasoning agent")
            self.runtime_log("")

            subtask_id = task_details["subtask_id"]
            subtask_description = task_details["subtask_description"]
            user_input = task_details["user_message"]
            deps_info = task_details.get("deps_info", "")
            raw_uploads = task_details.get("uploaded_images", [])
            uploaded_images = self.normalize_uploads(raw_uploads)
            history_str = task_details.get("history", "")

            # Log full payload (for debugging)
            logger.info(f"Task details received: {json.dumps(task_details, indent=2)}")
            logger.info(f"Process subtask {subtask_id}")
            logger.info(f"  Desc: {subtask_description}")

            # Build messages (System + Human), then count input tokens
            messages = self._build_messages(
                history=history_str,
                user_input=user_input,
                subtask_description=subtask_description,
                deps_info=deps_info,
                uploaded_images=uploaded_images,
            )

            # Count input tokens by concatenating only the string‐typed contents of each message
            concatenated = "\n".join(
                m.content if isinstance(m.content, str) else "" for m in messages
            )
            in_tokens = count_tokens(concatenated)
            self.log_input_tokens(gpt4_pro_model.model, in_tokens)

            # Step 1: Generate the initial response
            base_output = await self._generate_base_response(messages)

            # Step 2: Run the feedback loop for refinements
            response, refinements = await self._feedback_loop(
                base_messages=messages,
                subtask_description=subtask_description,
                initial_response=base_output,
            )

            # Step 3: Save to markdown
            output_file_path = self.save_response_to_md_file(
                subtask_id,
                subtask_description,
                base_output,
                refinements,
            )

            # Step 4: Report back to manager
            self.report_results(
                {"subtask_id": subtask_id, "output_file": output_file_path}
            )

            self.stop_timer("reasoning agent")

        except Exception as e:
            logger.error(f"Exec error: {e}")
            self.handle_error(e)

    def _build_messages(
        self,
        history: str,
        user_input: str,
        subtask_description: str,
        deps_info: str,
        uploaded_images: List[str],
    ) -> List[Any]:
        """
        Construct the list of BaseMessage objects (SystemMessage + HumanMessage) for Gemini reasoning.

        - Always emit a single SystemMessage containing:
            • “You are an intelligent assistant completing a single reasoning subtask…”
            • the full Chat History (user+assistant) if any,
            • “Main Task: …”, “Subtask: …”, “Additional info: …”
        - If any valid image files appear (non-.md), append one HumanMessage whose content is a list of parts:
            - first a text part “Here are the images to reference:”
            - then one {"type": "image_url", "image_url": {...}} block per file, base64-encoded.
        - Otherwise, append a plain-text HumanMessage:
            “Subtask: …\nAdditional info: …\n\nResponse:”
        """
        # 1) SYSTEM MESSAGE text: include full history + instructions
        system_lines = [
            "You are an intelligent assistant completing a single reasoning subtask. Your focus is on solving the subtask accurately using math, logic, and advanced reasoning skills.",
            "",
            "Chat History:",
            history or "(no prior chat)",
            "",
            f"Main Task: {user_input}",
            "",
            "Please answer below:",
        ]
        system_content = "\n".join(system_lines).strip()
        system_msg = SystemMessage(content=system_content)

        # 2) HUMAN MESSAGE: either embed images or plain text
        image_files = [fn for fn in uploaded_images if not fn.lower().endswith(".md")]
        if image_files:
            # Build a “parts” list: text part + one image_url per file
            parts: List[dict] = [
                {"type": "text", "text": "Here are the images to reference:"}
            ]
            UP = get_user_uploads_dir(self.chat_id)
            for fn in image_files:
                p = UP / fn
                if not p.is_file():
                    self.logger.warning(f"[reasoning_worker] upload missing: {p}")
                    continue
                raw = p.read_bytes()
                b64 = base64.b64encode(raw).decode("utf-8")
                mime, _ = mimetypes.guess_type(p.name)
                if not mime:
                    ext = p.suffix.lstrip(".").lower()
                    if ext == "jpg":
                        ext = "jpeg"
                    mime = f"image/{ext}"
                parts.append(
                    {
                        "type": "image_url",
                        "image_url": {"url": f"data:{mime};base64,{b64}"},
                    }
                )
            human_msg = HumanMessage(content=parts)
        else:
            # No valid images → plain-text HumanMessage
            human_text_lines = [
                f"Subtask: {subtask_description}",
                f"Additional info: {deps_info or '(none)'}",
                "",
                "Response:",
            ]
            human_msg = HumanMessage(content="\n".join(human_text_lines))

        return [system_msg, human_msg]

    async def _invoke_stream(
        self,
        messages: List[Any],
        *,
        model: ChatGoogleGenerativeAI = gpt4_pro_model,
        stream: bool = True,
    ) -> str:
        """Send ``messages`` to Gemini and return the accumulated response."""
        logger.info(
            "[reasoning_worker] LLM input messages:\n%s",
            "\n".join(
                f"{type(m).__name__}: {getattr(m, 'content', m)}" for m in messages
            ),
        )

        chunks: List[str] = []
        if stream:
            self.runtime_log_stream("stream")

        async for chunk in model.astream(messages):
            text = chunk.content if hasattr(chunk, "content") else str(chunk)
            if stream:
                self.runtime_log_stream(text)
            chunks.append(text)

        if stream:
            self.runtime_log_stream("stream")

        return "".join(chunks).strip()

    async def _generate_base_response(self, base_messages: List[Any]) -> str:
        """Generate the initial response using ``base_messages``."""

        response = await self._invoke_stream(
            base_messages, model=gpt4_pro_model, stream=True
        )
        self.runtime_log_stream("\n\n")
        logger.info("[reasoning_worker] initial output: %s", response[:200])
        out_tokens = count_tokens(response)
        self.log_output_tokens(gpt4_pro_model.model, out_tokens)
        return response

    async def _feedback_loop(
        self,
        *,
        base_messages: List[Any],
        subtask_description: str,
        initial_response: str,
        max_rounds: int = 5,
    ) -> Tuple[str, List[Dict[str, str]]]:
        """Iteratively refine ``initial_response`` up to ``max_rounds``.

        Returns the final response and a list of refinement dictionaries with
        keys ``agent``, ``instruction`` and ``output``.
        """

        response = initial_response
        refinements: List[Dict[str, str]] = []

        for round_no in range(1, max_rounds + 1):
            review_msgs = [
                SystemMessage(
                    "You are the feedback reviewer for the Reasoning agent. "
                    "Return a JSON array of two strings: the agent to run next "
                    "('reasoning', 'llm', or 'web') and a short instruction "
                    "describing the improvement. Use 'reasoning' when deeper "
                    "analysis or step-by-step logic is required. Select 'llm' "
                    "for stylistic fixes or general generation. Choose 'web' "
                    "when external facts are missing. If the current output is "
                    "sufficient, reply with ['reasoning', 'none']. Respond only "
                    "with the JSON array."
                    "IMPORTANT: Doing a refinement or fix is costly. Only do refinements when necessary like in case of incomplete information/not meeting the task"
                ),
                HumanMessage(
                    content=(
                        f"Past history and information provided for context: {base_messages}\n\n---\n\n"
                        f"Again, this is the main subtask we need to focus on: {subtask_description}\n\n---\n\n"
                        f"Current Output (output given by the current agent for the subtask):\n{response}\n"
                        "Provide the JSON array:"
                    )
                ),
            ]
            logger.info("[reasoning_worker] review round %d", round_no)
            review = await self._invoke_stream(
                review_msgs, model=gpt4_flash_model, stream=False
            )
            suggestions = review.strip()
            logger.info("[reasoning_worker] review response: %s", suggestions)

            clean = re.sub(r"^```(?:json)?\s*", "", suggestions)
            clean = re.sub(r"\s*```$", "", clean).strip()
            try:
                agent_choice, improvement = json.loads(clean)
            except Exception:
                logger.warning(
                    "[reasoning_worker] invalid feedback format: %s", suggestions
                )
                break
            if not isinstance(agent_choice, str) or not isinstance(improvement, str):
                logger.warning("[reasoning_worker] malformed feedback: %s", suggestions)
                break

            self.runtime_log_stream(f"\n\n---Ref:{improvement}---\n\n")
            if improvement.lower() in {"none", "none.", "'none'", '"none"'}:
                break

            if agent_choice.lower().startswith("reason"):
                target = ReasoningWorkerAgent(
                    f"{self.agent_id}_reason_fb", self.chat_id
                )
            elif agent_choice.lower().startswith("web"):
                from app.agents.workers.web_scraper_agent import (
                    WebScraperAgent,
                )  # noqa: WPS433

                target = WebScraperAgent(f"{self.agent_id}_web_fb", self.chat_id)
            else:

                # Local import avoids circular dependency
                from app.agents.workers.llm_worker_agent import (
                    LLMWorkerAgent,
                )  # noqa: WPS433

                target = LLMWorkerAgent(f"{self.agent_id}_llm_fb", self.chat_id)
            target.current_task = self.current_task

            response = await self.apply_feedback(
                target_agent=target,
                feedback=improvement,
                previous_output=response,
                task_details=self.current_task or {},
                past_refinements=refinements,
            )
            refinements.append(
                {
                    "agent": agent_choice,
                    "instruction": improvement,
                    "output": response,
                }
            )
            logger.info(
                "[reasoning_worker] round %d refined output: %s",
                round_no,
                response[:200],
            )
            out_tokens = count_tokens(response)
            self.log_output_tokens(gpt4_flash_model.model, out_tokens)

        return response, refinements

    def save_response_to_md_file(
        self,
        subtask_id: str,
        subtask_description: str,
        initial_output: str,
        refinements: List[Dict[str, str]],
    ) -> str:
        """
        Saves the generated response to a markdown (.md) file in the knowledge base.

        The file name is generated using the subtask ID and the current UTC timestamp.

        Returns:
            str: The file path of the saved md file.
        """
        timestamp = datetime.utcnow().strftime("%Y%m%d%H%M%S")
        output_file_name = f"{subtask_id}_{timestamp}.md"
        kb_dir = get_knowledge_base_dir(self.chat_id)
        output_file_path = os.path.join(kb_dir, output_file_name)
        os.makedirs(kb_dir, exist_ok=True)

        with open(output_file_path, "w", encoding="utf-8") as file:
            file.write("---\n\n")
            file.write(f"SUBTASK {subtask_id} STARTS\n\n")
            file.write("---\n")
            file.write(f"# Subtask {subtask_id}: {subtask_description}\n")
            file.write("## Output:\n")
            file.write(f"{initial_output}\n")
            for ref in refinements:
                file.write("## Refinement:\n")
                file.write(
                    f"### {ref.get('agent', '')} - {ref.get('instruction', '')}\n"
                )
                file.write(f"{ref.get('output', '')}\n")
            file.write("---\n\n")
            file.write(f"SUBTASK {subtask_id} COMPLETED\n\n")
            file.write("---")

        logger.info(f"[reasoning_worker] Saved: {output_file_path}")
        return output_file_path

    def report_results(self, results: Dict[str, Any]):
        """
        Reports the results of the subtask execution to the manager.

        Sends a message to the manager containing the subtask ID and the output file path.
        """
        subtask_id = results["subtask_id"]
        output_file = results["output_file"]
        send_message(
            self.chat_id,
            sender="worker",
            receiver="manager",
            message={"subtask_id": subtask_id, "output_file": output_file},
        )
        self.logger.info(f"[reasoning_worker] Reported {subtask_id}: {output_file}")
