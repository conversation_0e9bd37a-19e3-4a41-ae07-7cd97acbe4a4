# Concise System Design – `PPTXGeneratorAgent`

## 1. Goal

Develop a worker agent that generates a complete PowerPoint presentation (`.pptx`) from scratch using the **Opus0** architecture.

It must:

- Produce a JSON outline of slides and bullet-point content.
- Immediately generate a JSON style template (fonts, colours, layout hints) that mirrors the outline’s structure.
- Build each slide programmatically with `python-pptx`, applying the template suggestions (no images or animations in v0).
- Return a presigned download link just like the PDF & DOCX agents.

---

## 2. Why JSON Outline → JSON Template → `python-pptx`?

| Need                             | Pure slide-JSON from LLM | Two-step JSON (outline → template) |
|----------------------------------|---------------------------|--------------------------------------|
| Consistent styling               | LLM must mix structure & style in one go | ✅ Clear separation: first content, then style plan |
| LLM prompt simplicity            | Large schema, harder to validate | ✅ Each JSON is tiny & validated independently |
| Fine-grained slide control       | Limited unless post-processed | ✅ Template → direct `python-pptx` styling |

> This chain keeps prompts small and gives deterministic, reusable style data to the builder loop.

---

## 3. File & Class

**Path:**  
`app/agents/workers/pptx_generator_agent.py`

**Class:**  
```python
class PPTXGeneratorAgent(BaseAgent)
```

- Follow the same coding conventions (headers, separators, logging).

---

## 4. Execution Flow

1. **Generate outline JSON** (LLM) ⟶ `outline.json`  
2. **Generate style template** (LLM, sequential) ⟶ `template.json` (same keys as outline)  
3. Save both JSON files in `knowledge-base/` folder for audit  
4. For each slide in outline:
    - Create slide with `python-pptx`
    - Insert title & bullets from outline
    - Apply fonts/colours/layout from template
    - *(Speaker-notes placeholder optional)*
5. Save to:  
   `/sessions/{chat_id}/slides/pptx_result_<id>.pptx`
6. Upload to R2 → generate presigned 24h link  
7. `send_message()` results to manager  

---

## 5. MVP Scope

**Supported:**

- Titles
- Bullet & numbered lists
- Template-driven fonts & colours
- Basic title+content layouts

**Deferred:**

- Images
- Page/element animations
- Advanced layouts
- Charts

---

## 6. Future Road-map

- Image insertion & sizing
- Page-level transitions
- Element animations
- Enrich RAG layout examples
- Performance & stability tuning for large decks

---

## 7. Deliverables

- `pptx_generator_agent.py` – production-ready, fully logged & error-handled
- Unit test: outline+template fixture → slide count & style checks
- CI job for linting + tests
