# app/agents/workers/code_interpreter_agent.py
"""
Module: code_interpreter_agent.py

Worker agent that generates small Python snippets using Gemini and executes
them in an E2B Sandbox. The resulting stdout/stderr are saved to the knowledge
base as a markdown file.
"""

from __future__ import annotations

import logging
import re
from datetime import datetime
from typing import Any, Dict

from e2b_code_interpreter import Sandbox
from langchain_core.prompts import Chat<PERSON>romptTemplate
from langchain_core.output_parsers import Str<PERSON>utputParser
from langchain_google_genai import ChatGoogleGenerativeAI

from app.agents.base_agent import BaseAgent
from app.core.config import settings
from app.utils.communication import send_message
from app.utils.constants import get_knowledge_base_dir
from app.utils.token_counter import count_tokens

logger = logging.getLogger(__name__)

# Gemini model for code generation
code_model = ChatGoogleGenerativeAI(
    api_key=settings.GOOGLE_API_KEY,
    model="gemini-2.5-flash-preview-04-17",
    temperature=0.2,
)

code_prompt = ChatPromptTemplate.from_template(
    (
        "You are a helpful coding assistant. "
        "Write valid Python 3 code for the request below and "
        "return only the code.\n"
        "{input}"
    )
)

code_generation_chain = code_prompt | code_model | StrOutputParser()


class CodeInterpreterAgent(BaseAgent):
    """Generate and execute Python code snippets."""

    def __init__(self, agent_id: str, chat_id: str) -> None:
        super().__init__(agent_id, agent_type="Code Interpreter")
        self.chat_id = chat_id
        self.sandbox = Sandbox(api_key=settings.E2B_API_KEY)

    def _extract_code(self, raw_code: str) -> str:
        """Return the code block without Markdown fences."""
        match = re.search(r"```(?:python)?\n(.*?)```", raw_code, re.DOTALL)
        return match.group(1).strip() if match else raw_code.strip()

    def _save_results_to_md(
        self,
        subtask_id: str,
        prompt: str,
        code: str,
        stdout: str,
        stderr: str,
        error: str | None,
    ) -> str:
        """Save execution details to a Markdown file and return its path."""
        ts = datetime.utcnow().strftime("%Y%m%d%H%M%S")
        kb_dir = get_knowledge_base_dir(self.chat_id)
        kb_dir.mkdir(parents=True, exist_ok=True)
        file_path = kb_dir / f"{subtask_id}_{ts}.md"

        with open(file_path, "w", encoding="utf-8") as fh:
            fh.write("---\n\n")
            fh.write(f"SUBTASK {subtask_id} STARTS\n\n")
            fh.write("---\n")
            fh.write(f"# Subtask {subtask_id}\n")
            fh.write("## Prompt:\n")
            fh.write(f"{prompt}\n")
            fh.write("## Code:\n")
            fh.write("```python\n")
            fh.write(f"{code}\n")
            fh.write("```\n")
            if stdout:
                fh.write("## Stdout:\n")
                fh.write(f"{stdout}\n")
            if stderr:
                fh.write("## Stderr:\n")
                fh.write(f"{stderr}\n")
            if error:
                fh.write("## Error:\n")
                fh.write(f"{error}\n")
            fh.write("---\n\n")
            fh.write(f"SUBTASK {subtask_id} COMPLETED\n\n")
            fh.write("---")
        logger.info("Saved results: %s", file_path)
        return str(file_path)

    def _build_messages(
        self,
        history: str,
        user_input: str,
        subtask_description: str,
        deps_info: str,
    ) -> str:
        """Compose the prompt text for code generation."""
        lines = [
            "Chat History:",
            history or "(none)",
            "",
            f"Main Task: {user_input}",
            f"Subtask: {subtask_description}",
            f"Additional info: {deps_info or '(none)'}",
            "",
            "Python code:",
        ]
        return "\n".join(lines).strip()

    async def execute_task(self, task_details: Dict[str, Any]):
        """Generate, run, and record Python code for a subtask."""
        try:
            subtask_id = task_details["subtask_id"]
            user_input = task_details["user_message"]
            subtask_description = task_details["subtask_description"]
            deps_info = task_details.get("deps_info", "")
            history = task_details.get("history", "")

            prompt_text = self._build_messages(
                history=history,
                user_input=user_input,
                subtask_description=subtask_description,
                deps_info=deps_info,
            )

            in_tokens = count_tokens(prompt_text)
            self.log_input_tokens(code_model.model, in_tokens)

            chunks = []
            async for chunk in code_generation_chain.astream(
                {"input": prompt_text},
            ):
                text = chunk
                self.runtime_log_stream(text)
                chunks.append(text)
            self.runtime_log_stream("\n\n")
            code = self._extract_code("".join(chunks))
            out_tokens = count_tokens(code)
            self.log_output_tokens(code_model.model, out_tokens)
            logger.info("Generated code: %s", code[:200])

            execution = self.sandbox.run_code(code, language="python")
            stdout = getattr(execution.logs, "stdout", "")
            stderr = getattr(execution.logs, "stderr", "")
            error = (
                str(execution.error)
                if getattr(
                    execution,
                    "error",
                    None,
                )
                else None
            )
            file_path = self._save_results_to_md(
                subtask_id,
                prompt_text,
                code,
                stdout,
                stderr,
                error,
            )

            send_message(
                self.chat_id,
                sender="worker",
                receiver="manager",
                message={
                    "subtask_id": subtask_id,
                    "output_file": str(file_path),
                },
            )
        except Exception as exc:  # pragma: no cover - runtime logging
            logger.error("CodeInterpreterAgent error: %s", exc)
            self.handle_error(exc)
