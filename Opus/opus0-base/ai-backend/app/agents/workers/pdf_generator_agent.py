# app/agents/workers/pdf_generator_agent.py
"""
Module: pdf_generator_agent.py

This module defines the PDFGeneratorAgent class, a worker agent that plans an outline for a future PDF document.
It references the LLMWorkerAgent style: parse task details, generate an outline (in place of GPT output), and save
the outline to a markdown (.md) file for now. We do not yet create an actual PDF; that comes later.
"""

import asyncio
import os
import logging
import json
import httpx
import re
from io import BytesIO
from urllib.parse import urlparse
from typing import Dict, Any, List
from PIL import Image
import boto3
from datetime import datetime
from typing import Dict, Any, List
from playwright.async_api import async_playwright


from app.agents.base_agent import BaseAgent
from app.core.config import settings
from app.utils.constants import (
    get_knowledge_base_dir,
    get_public_pdf_dir,
    get_user_uploads_dir,
)
from app.utils.communication import send_message
from app.db.files import upload_pdf
from pathlib import Path
from app.utils.token_counter import count_tokens
from app.utils.example_rag import get_pdf_examples_text, get_pdf_section_examples_text, get_pdf_head_examples_text

from langchain_openai import ChatOpenAI
from langchain_google_genai import ChatGoogleGenerativeAI

from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser


# Initialize module-level logger
logger = logging.getLogger(__name__)

# ─── Trusted domains for replacement images ────────────────────────────────
TRUSTED_IMAGE_DOMAINS = {
    # Free stock photo libraries
    "unsplash.com",
    "pexels.com",
    "pixabay.com",
    "stocksnap.io",
    "burst.shopify.com",
    "kaboompics.com",
    "picjumbo.com",
    "rawpixel.com",
    "picspree.com",
    "freeimages.com",
    "stockvault.net",
    "freestocks.org",
    "gratisography.com",
    "reshot.com",
    "lifeofpix.com",
    # Commercial-grade (often CC-licensed) collections
    "flickr.com",
    "500px.com",
    "istockphoto.com",
    "gettyimages.com",
    "shutterstock.com",
    "adobe.com",
    "depositphotos.com",
    # Encyclopedic / open-license data & graphs
    "wikimedia.org",
    "wikipedia.org",
    "data.gov",
    "worldbank.org",
    "imf.org",
    "oecd.org",
    "who.int",
    # Travel / guide sites
    "lonelyplanet.com",
    "tripadvisor.com",
    "nationalgeographic.com",
    "roughguides.com",
    "travelchannel.com",
}

# -------------------------------------------------------------------------
# Define a GPT model and prompt chain for generating a PDF outline plan
# (same as before, unmodified — but slightly tweaked prompt instructions
# to avoid generic headings like "Introduction" or "Conclusion".)
# -------------------------------------------------------------------------
pdf_outline_model = ChatGoogleGenerativeAI(
    api_key=settings.GOOGLE_API_KEY,
    model="gemini-2.5-flash-preview-04-17",
    temperature=0.0,
)
pdf_outline_prompt = ChatPromptTemplate.from_template(
    """
You are a specialized assistant that creates a concise PDF outline in JSON format. You are part of the PDF Generator team.

We have the following inputs:
- Subtask Description (The subtask we need to do): {subtask_desc}
- User Message (Global Context of what the user wants in total): {user_input}
- Additional Information: {deps_info}

The Subtask Description is a specific breakdown of the overall user task, while Additional Information contains research details or data relevant to this subtask. Your goal is to produce a blueprint for the final PDF document that captures the essential sections and key points to include.

Your job:
1) Analyze the Subtask Description, User Message, and Additional Information carefully.
2) Develop a PDF outline blueprint according to the task given.
3) If your subtask is converting a pdf in a different language, you will be provided with the pdf outline and data in the deps info. Use the exact same outline word for word. This is very crucial and important.
6) Output only the JSON object in the format specified below, with no extra text or explanation.
7) For small documents like posters, resumes and more, never make multiple sections.

The JSON schema should follow this format:
{{
  "title": "string",
  "sections": [
    {{
      "Heading1": "Concise points for Heading1",
      "Heading2": "Concise points for Heading2",
      ...
    }}
  ]
}}

Below are some examples:

{examples}

IMPORTANT!!: Only make these multiple sections for long reports/articles/research papers. NOT for posters/leaflets/resumes and more.

Please generate the JSON outline only, with no additional text or explanation.
"""
)
pdf_outline_chain = pdf_outline_prompt | pdf_outline_model | StrOutputParser()

# -------------------------------------------------------------------------
# ADDITIONAL PROMPT/CHAIN FOR STEP-BY-STEP SECTION GENERATION
# We'll keep it very simple: the user wants to feed in the entire "history"
# plus the new heading + content from the outline, then get a final text chunk.
# -------------------------------------------------------------------------

pdf_section_model = ChatGoogleGenerativeAI(
    api_key=settings.GOOGLE_API_KEY,
    model="gemini-2.5-flash-preview-04-17",
    temperature=0.5,
    top_p=0.9,
)
pdf_section_prompt = ChatPromptTemplate.from_template(
    """ 
## Role  
You're a professional writer and frontend developer. Generate **static** PDF content as HTML `<body>` elements, using TailwindCSS for styling.

---

## Objective  
Produce production-ready HTML (no `<html>`, `<head>`, or `<body>` wrappers) that will be rendered into a PDF. Follow provided examples closely.

---

## Requirements  
- **Static only**: No scripts, buttons, forms, hover/animation effects, or interactive UI.  
- **Layout**:  
  - Base text size: `text-lg`  
  - When writing articles, documents, add horizontal padding in the main section divs usually: `px-20`  
  - **Image placement**: by default, images should not be floated or wrapped alongside text. Only use multi-column or side-by-side image layouts if the user explicitly requests it.  
  - IMPORTANT! Never leave placeholder text for visuals—wherever you intend an image, include an `<img>` tag with appropriate `alt` text that can easily be searched on the web. Don't use too specific alt text. 
  - If image provided, only add it to the pdf if the user wants. Don't include every image provided to a pdf.
  - **Uploaded images**: whenever you reference an image the user provided, insert exactly:
    ```html
    <img src="user_uploads/<filename.extension>" alt="…"/>
    ```
  - If image link provided in additional information, insert exactly:
    ```html
    <img src="link" alt="…"/>
    ```
  - If no image provided and you're making your own image for the image section, give exactly:
    ```html
    <img src="placeholder_image" alt="…"/>
    ```
- **Styling**:  
  - Aim for a high-quality, extremely modern design language.  
  - Never prefer 2-column text-image content. ONLY use single column until specified otherwise. Never give an image a full column in the pdf.
  - Main containers: `max-w-auto`.  
  - Use images or infographics where they add impact, via `<img>` tags.  
  - Maintain consistent styling throughout the document. 
- **Tone & Quality**:  
  - Match the user's specified style while using provided examples. 
  - IMPORTANT! Provide final content only—no comments, explanations or placeholder text. Only the final product.  
  - Make every section beautiful and fully featured—worthy of production. High level of structure.
  - Don't use Cards, border shadows for divs usually.

---

## EXAMPLES (some amazing looking documents that you can use as reference):

{section_examples}

---



## Context you are given:

- **Head Styles & Resources:** (Reference only – don’t emit it.)
    ```
    {head_html}
    ```
- **Existing Content (History):** (The HTML already written. Do **not** repeat—only append new content.)  
    ```
    {history}
    ```
- **Existing Outline (All Headings):** (The full table of contents for the PDF. Use this to avoid duplicating sections and to see what comes next.)  
    ```
    {full_outline}
    ```
- **Subtask Description** (The piece of the task you’re working on.)  
    ```
    {subtask_desc}
    ```   
- **User Message** (The user’s original request. Use to match your tone and scope to this.)  
    ```
    {user_input}
    ```    
  - **Researched Information** (Any supporting data, links, or media you should incorporate.)  
    ```
    {deps_info}
    ```  

- **Section Heading:** (The current section title plus its brief description. Write only the content for this heading.)  
```
{heading}:{placeholder_content}
```

---

## SUPER IMPORTANT!: DON'T repeat any information from the history in your response. Only return BODY code.
## OUTPUT: 

"""
)
pdf_section_chain = pdf_section_prompt | pdf_section_model | StrOutputParser()

# ───────────────────────── HEAD-GENERATION LLM ──────────────────────────
pdf_head_model = ChatGoogleGenerativeAI(
    api_key=settings.GOOGLE_API_KEY,
    model="gemini-2.5-flash-preview-04-17",
    temperature=0.3,
    top_p=0.7,
)

pdf_head_prompt = ChatPromptTemplate.from_template(
    """
### ROLE
Front-end architect for high-fidelity, print-ready PDFs.  
You return exactly one <head> … </head> element.

### OBJECTIVE
Deliver a production-quality <head> block tailored to this document.

### GOLD STANDARD RULES
1. Emit **only** the <head> … </head> markup (no <html>, <body>, comments, JSON fences).  
2. Must include, in this order:  
   • <meta charset="utf-8">  
   • <meta name="viewport" content="width=device-width,initial-scale=1">  
   • <script src="https://cdn.tailwindcss.com"></script>  
   • <title>…</title> → text mirrors **{outline_title}** exactly.  
3. **Typography & colour** (non-negotiable):  
   • Import ≥ 1 Google Font appropriate to the tone (**fallback:** *Merriweather* body + *Montserrat* headings).  
   • Define either CSS variables *or* a Tailwind inline config with at least one brand colour or font family.  
4. Use **no other JavaScript**, analytics, or comments. Keep whitespace compact.
5. Try NOT to use a tailwind config.

### STYLE EXPECTATIONS
- Think “magazine-ready” or “agency handoff” — never barebones.  
- Leverage Tailwind’s `extend` for palette / fonts when cues are present in **<DEPS_INFO>**.  
- When cues are absent, reproduce the typographic/colour approach of the **ARTICLE** example.

---


### EXAMPLE  (follow this structure exactly)

{head_examples}


---


# CONTEXT  (reference only)
<OUTLINE_TITLE>
{outline_title}
</OUTLINE_TITLE>
<SUBTASK>
{subtask_desc}
</SUBTASK>
<USER_MSG>
{user_input}
</USER_MSG>
<DEPS_INFO>
{deps_info}
</DEPS_INFO>

---

### OUTPUT
Return the complete <head> block now:

"""
)

pdf_head_chain = pdf_head_prompt | pdf_head_model | StrOutputParser()


class PDFGeneratorAgent(BaseAgent):
    """
    Worker agent capable of planning a PDF document outline.

    Inherits from BaseAgent and implements task-specific logic to:
      1. Parse the task details (subtask_id, subtask_description, user_message, deps_info).
      2. Generate a simple PDF "outline" based on the subtask details (using GPT).
      3. Save that exact JSON outline (raw) in a markdown (.md) file, without reformatting it.
      4. Iterate over each section to generate final content in HTML, saving it in another .md file.
      5. Report completion to the manager.
    """

    def __init__(self, agent_id: str, chat_id: str):
        """
        Initializes the PDFGeneratorAgent with a unique agent ID.

        Args:
            agent_id (str): Unique identifier for the PDF generator agent.
        """
        super().__init__(agent_id, agent_type="Document Generator Tool")
        self.chat_id = chat_id

    async def _invoke_chain(
        self, chain: Any, payload: Dict[str, Any], *, stream: bool = True
    ) -> str:
        """Run ``chain`` with ``payload`` and return the combined result.

        When ``stream`` is ``True`` runtime log events are emitted for each
        chunk received from ``chain``.
        """
        logger.info(
            "[pdf_generator] LLM input payload:\n%s", json.dumps(payload, indent=2)
        )

        chunks: List[str] = []
        if stream:
            self.runtime_log_stream("stream")

        async for chunk in chain.astream(payload):
            text = chunk if isinstance(chunk, str) else str(chunk)
            if stream:
                self.runtime_log_stream(text)
            chunks.append(text)

        if stream:
            self.runtime_log_stream("stream")

        return "".join(chunks).strip()

    async def execute_task(self, task_details: Dict[str, Any]):
        """
        Steps:
        1) Generate a PDF outline in JSON (raw).
        2) Save it as-is to a .md file.
        3) Log what prompt data we pass to the outline function.
        4) Iterate over each outline section, generating final HTML content in a loop.
        5) Log what prompt data we pass to the pdf text generator function.
        6) Save combined final text (HTML) to another .md file.
        7) Generate CSS for the final HTML text, save in a .md file.
        8) Generate the final PDF using WeasyPrint (HTML + CSS).
        9) Combine the final HTML and CSS into one string, save it in a new .md file.
        10) Finally, report that .md file to the manager as "output_file".
        """
        try:

            self.start_timer("pdf agent")

            # --- Extract details ---
            subtask_id = task_details.get("subtask_id", "no_subtask")
            subtask_description = task_details.get("subtask_description", "no_desc")
            user_input = task_details.get("user_message", "n/a")
            deps_info = task_details.get("deps_info", "n/a")
            raw_uploads = task_details.get("uploaded_images", [])
            self.uploaded_images = self.normalize_uploads(raw_uploads)

            # --- Logging basic info ---
            # self.logger.info(f"[PDFGenerator] Full Subtask Description: {subtask_description}")
            # truncated_deps = deps_info[:300] + "..." if len(deps_info) > 300 else deps_info
            # self.logger.info(f"[PDFGenerator] Deps Info (truncated): {truncated_deps}")

            # ----------------------------
            # (1) Generate & Save Outline
            # ----------------------------
            self.logger.info(
                f"[PDFGenerator] Outline generation input => subtask_desc='{subtask_description}'"
            )
            outline_plan = await self.determine_pdf_outline(
                subtask_desc=subtask_description,
                user_input=user_input,
                deps_info=deps_info,
            )
            # Store full outline text for later section prompts
            self.full_outline_text = json.dumps(
                outline_plan, ensure_ascii=False, indent=2
            )
            self.logger.info("[PDFGenerator] Outline generated successfully.")
            self.save_outline_to_md_file(subtask_id, outline_plan)

            # (1b) ─ Generate the <head> once
            head_text = await self.generate_head(
                outline_plan.get("title", "PDF Document"),
                subtask_description,
                user_input,
                deps_info,
            )
            self.head_text = head_text  # store so other methods can access

            # ----------------------------
            # (2) Generate & Save Full HTML
            # ----------------------------
            self.logger.info(
                f"[PDFGenerator] Full PDF text generation input => subtask_desc='{subtask_description}' "
            )
            full_pdf_text = await self.generate_full_pdf_text(
                outline_plan, subtask_description, user_input, deps_info, head_text
            )

            # Wrap into a complete HTML document and inline CSS
            BASE_DIR = Path(__file__).resolve().parents[3]
            css_file = BASE_DIR / "assets" / "css" / "output.css"
            css_text = css_file.read_text()

            new_full_pdf_text = f"""
            <!DOCTYPE html>
            <html lang="en">
            {head_text}
            <body>
            {full_pdf_text}
            </body>
            </html>
            """

            # ----------------------------
            # Image Replacement
            # ----------------------------
            self.runtime_log("Downloading Images")
            image_tags = await self.extract_image_links(new_full_pdf_text)

            self.logger.info(f"[PDFGenerator] extract_image_links → {image_tags}")

            replacements = await self._find_replacements(image_tags)
            self.logger.info(f"[PDFGenerator] _find_replacements → {replacements}")

            # perform in‐place one‐by‐one replacement
            for orig, repl in zip(image_tags, replacements):
                new_full_pdf_text = new_full_pdf_text.replace(
                    orig["url"], repl["url"], 1
                )
                self.logger.debug(
                    f"[PDFGenerator] replaced {orig['url']} → {repl['url']}"
                )

            self.save_final_text_to_md_file(subtask_id, new_full_pdf_text)

            # ----------------------------
            # (3) Generate Final PDF
            # ----------------------------

            # Now render
            self.runtime_log("Rendering Document")
            timestamp = datetime.utcnow().strftime("%Y%m%d%H%M%S")
            pdf_name = f"pdf_result_{subtask_id}_{timestamp}.pdf"
            pdf_dir = get_public_pdf_dir(self.chat_id)
            pdf_dir.mkdir(parents=True, exist_ok=True)
            public_path = pdf_dir / pdf_name

            # Use Playwright instead of WeasyPrint for PDF rendering
            # self.runtime_log("Rendering PDF")
            await self._render_pdf_with_playwright(new_full_pdf_text, public_path)
            # self.runtime_log("PDF rendered and saved successfully")

            # ── Upload to R2 and grab a presigned GET URL ──
            from app.utils.r2_client import upload_file_to_r2

            pdf_url = upload_file_to_r2(
                public_path,
                content_type="application/pdf",
                expires_in=60 * 60 * 24,  # valid for 24 h
            )

            doc_url = f"{settings.BASE_URL}/docs/{public_path.name}"
            self.runtime_log(f"Generated PDF: {doc_url}")

            # ----------------------------
            # (9) Combine HTML+CSS -> .md file
            # ----------------------------
            combined_markdown = (
                f"# Your PDF is ready**Download PDF:** [{pdf_name}]({doc_url}) \n ---"
                f" \n ## Generated HTML + CSS: {new_full_pdf_text} \n "
                f"You can always download your PDF at {doc_url} \n "
                "#IMPORTANT: Always give the pdf url link to the user in your final message."
            )
            combined_file_path = self.save_combined_html_css_to_md_file(
                subtask_id, combined_markdown
            )
            self.logger.info(
                f"[PDFGenerator] Combined HTML+CSS file ⇒ {combined_file_path}"
            )

            # ----------------------------
            # (10) Report that .md file to manager
            # ----------------------------
            self.report_results(
                {"subtask_id": subtask_id, "output_file": combined_file_path}
            )

            self.logger.info(
                f"[PDFGenerator] Subtask {subtask_id} completed with final PDF, CSS, and combined file reported."
            )

            self.stop_timer("pdf agent")

        except Exception as e:
            self.logger.error(f"[PDFGenerator] Task execution error: {str(e)}")
            self.handle_error(e)

    async def _render_pdf_with_playwright(
        self,
        html_content: str,
        output_path: Path,
        scale_factor: float = 0.9,
    ):
        """Render HTML content to a PDF using Playwright.

        Parameters
        ----------
        html_content: str
            The fully formed HTML markup to render.
        output_path: Path
            Destination path for the generated PDF.
        scale_factor: float, optional
            Increase to zoom the output relative to CSS pixels.
        """
        try:
            async with async_playwright() as p:
                browser = await p.chromium.launch()
                page = await browser.new_page()

                PX_PER_IN = 96
                A4_W_IN = 8.27
                A4_H_IN = 11.69

                await page.set_viewport_size(
                    {
                        "width": int(A4_W_IN * PX_PER_IN),
                        "height": int(A4_H_IN * PX_PER_IN),
                    }
                )

                # turn any <img src="user_uploads/foo.png"> into
                # http://<base>/user_uploads/foo.png
                base = settings.BASE_URL.rstrip("/")
                for fn in getattr(self, "uploaded_images", []):
                    public_url = f"{base}/sessions/{self.chat_id}/user_uploads/{fn}"
                    self.logger.info(
                        f"[PDFGenerator] replacing src user_uploads/{fn} → {public_url}"
                    )
                    html_content = html_content.replace(
                        f'src="user_uploads/{fn}"', f'src="{public_url}"'
                    )

                # scale_factor = 1.5

                # 1️⃣  layout first
                await page.set_content(
                    html_content, wait_until="networkidle", timeout=480_000
                )

                # # measure AFTER layout
                # dims = await page.evaluate(
                #     """
                # () => {
                #   const { scrollWidth } = document.documentElement;
                #   return { w: scrollWidth };
                # }
                # """
                # )

                # scale_factor = min(scale_factor, (A4_W_IN * PX_PER_IN) / dims["w"])

                await page.pdf(
                    path=str(output_path),
                    width=f"{A4_W_IN}in",
                    height=f"{A4_H_IN}in",
                    scale=scale_factor,
                    print_background=True,
                    margin={
                        "top": "0.5in",
                        "right": "0in",
                        "bottom": "0.5in",
                        "left": "0in",
                    },
                )

                await browser.close()
        except Exception as e:
            self.logger.error(f"[PDFGenerator] Playwright PDF generation error: {e}")
            raise

    async def determine_pdf_outline(
        self, subtask_desc: str, user_input: str, deps_info: str
    ) -> Dict[str, Any]:
        """
        Asks GPT to produce a JSON outline. If it fails, we fallback to a default.
        We also store the raw GPT text in _raw_outline.
        """

        # ✂️— if any images, append their names into deps_info so the outline LLM sees them
        if getattr(self, "uploaded_images", []):
            deps_info = (
                deps_info + "\nUploaded images: " + ", ".join(self.uploaded_images)
            )

        # Get relevant examples from RAG system
        examples_text = get_pdf_examples_text(user_input, k=3)

        input_data = {
            "subtask_desc": subtask_desc,
            "user_input": user_input,
            "deps_info": deps_info,
            "examples": examples_text
        }

        # — log outline‐prompt input tokens —
        filled_prompt = pdf_outline_prompt.format_prompt(
            subtask_desc=subtask_desc, user_input=user_input, deps_info=deps_info, examples=examples_text
        ).to_string()
        in_toks = count_tokens(filled_prompt)
        self.log_input_tokens(pdf_outline_model.model, in_toks)

        raw_output = await self._invoke_chain(
            pdf_outline_chain, input_data, stream=False
        )

        # ▶︎ NEW: strip ```json … ``` or ``` … ``` fences in one go
        raw_output = re.sub(r"^```(?:json)?\s*", "", raw_output)  # leading fence
        raw_output = re.sub(r"\s*```$", "", raw_output).strip()  # trailing fence

        # --- Remove leading/trailing triple backticks + optional 'json' ---
        # If it starts with ```json or ``` remove that line entirely.
        lines = raw_output.split("\n")
        if lines and lines[0].strip().startswith("```"):
            lines.pop(0)
        # If it ends with ``` on the last line, remove that line, too
        if lines and lines[-1].strip().endswith("```"):
            lines.pop()

        # Rejoin the lines
        cleaned_output = "\n".join(lines).strip()

        # — log outline‐output tokens —
        out_toks = count_tokens(cleaned_output)
        self.log_output_tokens(pdf_outline_model.model, out_toks)

        try:
            outline = json.loads(cleaned_output)
            if not isinstance(outline, dict) or "sections" not in outline:
                raise ValueError("JSON does not contain 'sections' or is not a dict.")
            # Store the *raw* JSON text (the same text we pass after cleanup)
            outline["_raw_outline"] = cleaned_output
            return outline

        except Exception as parse_err:
            self.logger.warning(
                f"[PDFGenerator] Outline parsing failed. Using fallback. Error: {parse_err}"
            )
            fallback_outline = {
                "title": f"PDF for Subtask: {subtask_desc}",
                "sections": [
                    {
                        "heading": "Generic Early Life",
                        "content": f"Requested by: {user_input}. Additional context: {deps_info}.",
                    },
                    {
                        "heading": "Generic Middle Phase",
                        "content": "Default fallback content.",
                    },
                    {
                        "heading": "Generic Final Phase",
                        "content": "Summary or next steps.",
                    },
                ],
                # STILL store the raw output (with or without triple backticks),
                # so at least we save the original text to MD
                "_raw_outline": raw_output,
            }
            return fallback_outline

    def save_outline_to_md_file(
        self, subtask_id: str, outline_plan: Dict[str, Any]
    ) -> str:
        """
        Saves the *raw JSON* outline to a markdown (.md) file in the knowledge base.
        We no longer convert it into a new format—just write the exact text from GPT.
        """
        timestamp = datetime.utcnow().strftime("%Y%m%d%H%M%S")
        output_file_name = f"pdf_outline_{subtask_id}_{timestamp}.md"
        kb_dir = get_knowledge_base_dir(self.chat_id)
        output_file_path = os.path.join(kb_dir, output_file_name)
        os.makedirs(kb_dir, exist_ok=True)

        # Attempt to get raw JSON text from outline_plan
        raw_json_outline = outline_plan.get("_raw_outline", "{}")

        try:
            with open(output_file_path, "w", encoding="utf-8") as file:
                # Write the raw JSON
                file.write(raw_json_outline)
            self.logger.info(f"[PDFGenerator] Outline saved: {output_file_path}")
            return output_file_path
        except Exception as e:
            self.logger.error(f"[PDFGenerator] Error saving outline: {str(e)}")
            return "file_write_error"

    async def generate_head(
        self,
        outline_title: str,
        subtask_desc: str,
        user_input: str,
        deps_info: str,
    ) -> str:
        # Get relevant head examples from RAG system
        head_examples_text = get_pdf_head_examples_text(
            f"{outline_title} {subtask_desc} {user_input}", k=3
        )

        payload = {
            "outline_title": outline_title,
            "subtask_desc": subtask_desc,
            "user_input": user_input,
            "deps_info": deps_info,
            "head_examples": head_examples_text,
        }

        head_html = await self._invoke_chain(pdf_head_chain, payload, stream=False)

        # strip ``` fences if model adds them
        lines = head_html.splitlines()
        if lines and lines[0].lstrip().startswith("```"):
            lines.pop(0)
        if lines and lines[-1].rstrip().endswith("```"):
            lines.pop()
        return "\n".join(lines)

    async def generate_full_pdf_text(
        self,
        outline_plan: Dict[str, Any],
        subtask_desc: str,
        user_input: str,
        deps_info: str,
        head_html: str,
    ) -> str:
        """
        Iterates over outline_plan["sections"] and, for each heading,
        asks the LLM to generate *only* the new content that follows the
        existing history (i.e. no repeats).

        Returns the concatenated HTML for all sections.
        """
        sections = outline_plan.get("sections", [])
        pdf_history = ""

        self.runtime_log("Writing the Content")

        for section in sections:
            for heading, placeholder_content in section.items():
                self.runtime_log(f"HTML: {heading}")

                # 1) Build the "history" + explicit no-repeat instruction
                # ✂️— if any images, tell the LLM “you’ve got these at your disposal”
                images_note = ""
                if getattr(self, "uploaded_images", []):
                    images_note = (
                        "Use these images where appropriate: "
                        + ", ".join(self.uploaded_images)
                        + "\n"
                    )
                no_repeat_instruction = (
                    "DO NOT REPEAT ANYTHING FROM THE HISTORY. "
                    f"Only generate the content that comes immediately after the above history "
                    f'for the section titled "{heading}".'
                )
                history_with_instruction = (
                    f"{pdf_history}\n\n{images_note}\n\n{no_repeat_instruction}"
                )

                # 2) Get relevant section examples from RAG system
                section_examples_text = get_pdf_section_examples_text(
                    f"{heading} {placeholder_content} {user_input}", k=3
                )

                # 3) Pack up all prompt inputs
                prompt_data = {
                    "history": history_with_instruction,
                    "heading": heading,
                    "placeholder_content": placeholder_content,
                    "subtask_desc": subtask_desc,
                    "user_input": user_input,
                    "deps_info": deps_info,
                    "full_outline": self.full_outline_text,
                    "head_html": head_html,
                    "section_examples": section_examples_text,
                }

                # — log per‐section input tokens —
                filled_sec = pdf_section_prompt.format_prompt(
                    history=history_with_instruction,
                    heading=heading,
                    placeholder_content=placeholder_content,
                    subtask_desc=subtask_desc,
                    user_input=user_input,
                    deps_info=deps_info,
                    full_outline=self.full_outline_text,
                    head_html=head_html,
                    section_examples=section_examples_text,
                ).to_string()
                in_toks_sec = count_tokens(filled_sec)
                self.log_input_tokens(pdf_section_model.model, in_toks_sec)

                # 3) Stream the section HTML back from the LLM
                section_html = await self._invoke_chain(
                    pdf_section_chain, prompt_data, stream=True
                )

                # 4) Strip any code-fence wrappers
                lines = section_html.split("\n")
                if lines and lines[0].strip().startswith("```"):
                    lines.pop(0)
                if lines and lines[-1].strip().endswith("```"):
                    lines.pop()
                section_html = "\n".join(lines).strip()

                # — log per‐section output tokens —
                out_toks_sec = count_tokens(section_html)
                self.log_output_tokens(pdf_section_model.model, out_toks_sec)

                # 5) Append to the running history (so the next section knows where we left off)
                pdf_history += f"\n{section_html}\n"

        return pdf_history

    def save_final_text_to_md_file(self, subtask_id: str, final_text: str) -> str:
        """
        Saves the combined final HTML text into a .md file.
        """
        timestamp = datetime.utcnow().strftime("%Y%m%d%H%M%S")
        file_name = f"pdf_fulltext_{subtask_id}_{timestamp}.md"
        kb_dir = get_knowledge_base_dir(self.chat_id)
        file_path = os.path.join(kb_dir, file_name)
        os.makedirs(kb_dir, exist_ok=True)

        try:
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(final_text)
            self.logger.info(f"[PDFGenerator] Full PDF text saved: {file_path}")
            return file_path
        except Exception as e:
            self.logger.error(f"[PDFGenerator] Error saving final text: {str(e)}")
            return "file_write_error"

    def save_combined_html_css_to_md_file(
        self, subtask_id: str, combined_html: str
    ) -> str:
        timestamp = datetime.utcnow().strftime("%Y%m%d%H%M%S")
        combined_md_name = f"pdf_combined_{subtask_id}_{timestamp}.md"
        kb_dir = get_knowledge_base_dir(self.chat_id)
        combined_md_path = os.path.join(kb_dir, combined_md_name)

        self.logger.info(
            f"[PDFGenerator] Saving combined HTML+TAILWIND to: {combined_md_path}"
        )

        try:
            with open(combined_md_path, "w", encoding="utf-8") as f:
                f.write(combined_html)
            self.logger.info(
                f"[PDFGenerator] Combined HTML+TAILWIND file created => {combined_md_path}"
            )
            return combined_md_path
        except Exception as e:
            self.logger.error(
                f"[PDFGenerator] Error saving combined HTML+TAILWIND: {str(e)}"
            )
            return "file_write_error"

    # ─── Helper 1: URL existence + size check ──────────────────────────────────────
    # ─── Helper 1: URL existence + size check ──────────────────────────────────────
    async def _url_exists(
        self, url: str, min_width: int = 100, min_height: int = 100
    ) -> bool:
        """
        Return True if the URL points to an image that actually loads and
        whose dimensions are at least min_width x min_height.
        We issue a HEAD to weed out 4xx/5xx only, then do a GET with a UA override.
        """
        # ── if this is one of our own uploads, it “exists” by definition
        if url.startswith("user_uploads/"):
            local = get_user_uploads_dir(self.chat_id) / url.split("/", 1)[-1]
            if local.is_file():
                return True

        headers = {"User-Agent": "Mozilla/5.0 (compatible; PDFGeneratorAgent/1.0)"}
        try:
            async with httpx.AsyncClient(timeout=5.0) as client:
                # quick HEAD just to catch 4xx/5xx
                try:
                    head = await client.head(
                        url, headers=headers, follow_redirects=True
                    )
                    if head.status_code >= 400:
                        return False
                except Exception:
                    pass  # fall back to GET below

                # full GET to fetch image bytes
                resp = await client.get(url, headers=headers, follow_redirects=True)
                resp.raise_for_status()

                # strict content-type check
                ctype = resp.headers.get("Content-Type", "")
                if not ctype.startswith("image/"):
                    return False

                # load into PIL and check dimensions
                img = Image.open(BytesIO(resp.content))
                w, h = img.size
                ok = w >= min_width and h >= min_height
                logger.debug(f"[PDFGenerator] _url_exists('{url}') → {ok} ({w}×{h})")
                return ok

        except Exception as e:
            logger.warning(
                f"[PDFGenerator] URL existence/size check failed for {url}: {e}"
            )
            return False

    # ─── Helper 2: Extract placeholder <img> tags ─────────────────────────────────
    async def extract_image_links(self, text: str) -> List[Dict[str, Any]]:
        """
        Extract all <img> tags and return a list of dicts:
          { "url": original_src, "label": alt_text, "exists": bool }
        """
        img_tags = re.findall(r"<img\b[^>]*>", text, flags=re.IGNORECASE)
        out = []
        for tag in img_tags:
            m_src = re.search(r'src=[\'"]([^\'"]+)[\'"]', tag, flags=re.IGNORECASE)
            m_alt = re.search(r'alt=[\'"]([^\'"]*)[\'"]', tag, flags=re.IGNORECASE)
            if not m_src:
                continue
            url = m_src.group(1)
            label = m_alt.group(1) if m_alt else ""
            exists = await self._url_exists(url)
            out.append({"url": url, "label": label, "exists": exists})
            logger.debug(
                f"[PDFGenerator] extract_image_links → {url!r} alt={label!r} exists={exists}"
            )
        return out

    # ─── Helper 3: Replace placeholders via Brave Image Search with retries ─────
    async def _find_replacements(
        self, tags: List[Dict[str, Any]]
    ) -> List[Dict[str, str]]:
        """
        For each tag where exists=False:
          1) Retry (up to 3 times) Brave Image Search on errors.
          2) Flatten [properties.url, thumbnail.src].
          3) Validate each candidate via _url_exists.
          4) Pick the first valid candidate or fall back to original.
        """
        sem = asyncio.Semaphore(5)

        async def lookup(tag):
            # if the placeholder already exists, no need to replace
            if tag["exists"]:
                return {"url": tag["url"], "label": tag["label"]}

            for attempt in range(1, 4):
                try:
                    async with sem:
                        async with httpx.AsyncClient(timeout=10.0) as client:
                            resp = await client.get(
                                "https://api.search.brave.com/res/v1/images/search",
                                headers={
                                    "Accept": "application/json",
                                    "X-Subscription-Token": settings.BRAVE_SEARCH_API,
                                },
                                params={
                                    "q": tag["label"],
                                    "count": 5,
                                    "search_lang": "en",
                                },
                            )
                            resp.raise_for_status()
                            items = resp.json().get("results", [])

                    # build candidate list: properties.url first, then thumbnail.src
                    cands: List[str] = []
                    for it in items:
                        full = it.get("properties", {}).get("url")
                        thumb = it.get("thumbnail", {}).get("src")
                        if full:
                            cands.append(full)
                        if thumb:
                            cands.append(thumb)

                    # validate & pick first valid
                    for candidate in cands:
                        if await self._url_exists(candidate):
                            logger.info(
                                f"[PDFGenerator] chosen replacement for '{tag['label']}': {candidate}"
                            )
                            return {"url": candidate, "label": tag["label"]}

                    # no valid candidate found => no need to retry
                    break

                except Exception as e:
                    logger.warning(
                        f"[PDFGenerator] replacement lookup error for {tag['label']!r} "
                        f"(attempt {attempt}/3): {e}"
                    )
                    if attempt < 3:
                        # small backoff before retrying
                        await asyncio.sleep(1)

            # if we get here, either no valid candidate or retries exhausted
            logger.info(
                f"[PDFGenerator] no replacement found for '{tag['label']}', using original"
            )
            return {"url": tag["url"], "label": tag["label"]}

        # kick off all lookups in parallel
        return await asyncio.gather(*(lookup(t) for t in tags))

    def report_results(self, results: Dict[str, Any]):
        """
        Reports the results of the subtask execution to the manager.

        Sends a message to the manager containing the subtask ID and the output file path.

        Args:
            results (Dict[str, Any]): Dictionary containing:
                - "subtask_id": Unique identifier for the subtask.
                - "output_file": Path to the saved markdown file.
        """
        subtask_id = results["subtask_id"]
        output_file = results["output_file"]
        send_message(
            self.chat_id,
            sender="worker",
            receiver="manager",
            message={"subtask_id": subtask_id, "output_file": output_file},
        )
        self.logger.info(f"Reported {subtask_id}: {output_file}")
