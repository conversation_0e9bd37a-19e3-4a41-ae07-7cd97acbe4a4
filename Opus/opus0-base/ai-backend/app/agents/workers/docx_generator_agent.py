# app/agents/workers/docx_generator_agent.py
"""
Module: docx_generator_agent.py

This module defines the DOCXGeneratorAgent class, a worker agent that generates DOCX documents from LLM-generated Markdown.
It follows the same execution pattern as PDFGeneratorAgent: generate outline, generate content, parse Markdown to DOCX,
and upload to R2 with presigned download links.
"""

import logging
import json
import re
import asyncio
import httpx
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional
from pathlib import Path
from io import BytesIO
from docx import Document
from docx.enum.style import WD_STYLE_TYPE
from docx.shared import Inches

from app.agents.base_agent import BaseAgent
from app.core.config import settings
from app.utils.constants import (
    get_knowledge_base_dir,
    get_public_docx_dir,
    get_user_uploads_dir,
)
from app.utils.communication import send_message
from pathlib import Path
from app.utils.token_counter import count_tokens
from app.utils.example_rag import get_docx_examples_text, get_docx_section_examples_text

from langchain_google_genai import ChatGoogleGenerativeA<PERSON>

from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser


# Initialize module-level logger
logger = logging.getLogger(__name__)

# -------------------------------------------------------------------------
# Define a GPT model and prompt chain for generating a DOCX outline plan
# -------------------------------------------------------------------------
docx_outline_model = ChatGoogleGenerativeAI(
    api_key=settings.GOOGLE_API_KEY,
    model="gemini-2.5-flash-preview-04-17",
    temperature=0.0,
)

docx_outline_prompt = ChatPromptTemplate.from_template(
    """
You are a specialized assistant that creates a concise DOCX outline in JSON format. You are part of the DOCX Generator team.

## Task
Create a structured outline for a DOCX document based on the user's request. The outline should be comprehensive yet focused.

## Input Context
- **Subtask Description**: {subtask_desc}
- **User Input**: {user_input}
- **Dependencies Info**: {deps_info}

## Examples
{examples}

## Output Format
Return ONLY a valid JSON object with this exact structure:
```json
{{
  "title": "Document Title",
  "fonts": {{
    "primary_font": "Times New Roman",
    "heading_font": "Arial",
    "document_type": "business|academic|creative|technical|legal|marketing"
  }},
  "header": {{
    "text": "Optional header text (leave empty if no header needed)",
    "include_page_number": false
  }},
  "footer": {{
    "text": "Optional footer text (leave empty if no footer needed)",
    "include_page_number": true,
    "include_date": false
  }},
  "sections": [
    {{
      "heading": "Section 1 Title",
      "placeholder_content": "Brief description of what this section should contain"
    }},
    {{
      "heading": "Section 2 Title",
      "placeholder_content": "Brief description of what this section should contain"
    }}
  ]
}}
```

## Guidelines
- Create 3-7 logical sections that comprehensively cover the topic
- Use specific, descriptive headings (avoid generic terms like "Introduction", "Conclusion")
- Each placeholder_content should be 1-2 sentences describing the section's purpose
- Ensure sections flow logically and build upon each other
- Focus on the core content the user is requesting

## Font Selection Guidelines
Analyze the user input and document type to select appropriate fonts:

- **Business Documents**: Use professional fonts like "Calibri", "Arial", or "Times New Roman"
- **Academic Papers**: Use traditional fonts like "Times New Roman", "Georgia", or "Garamond"
- **Creative Documents**: Use modern fonts like "Montserrat", "Open Sans", or "Lato"
- **Technical Documents**: Use clean fonts like "Arial", "Helvetica", or "Segoe UI"
- **Legal Documents**: Use formal fonts like "Times New Roman", "Georgia", or "Book Antiqua"
- **Marketing Materials**: Use engaging fonts like "Montserrat", "Roboto", or "Source Sans Pro"
- **User-specified fonts**: If the user mentions a specific font in their request, always use that font for both primary_font and heading_font
- **Fallback**: If uncertain, use "Calibri" for primary_font and "Arial" for heading_font

**CRITICAL**: You MUST include the "fonts" object in your JSON response. The system requires primary_font, heading_font, and document_type fields. Do not omit this section.

## Header/Footer Guidelines
- **Headers**: Use for document title, author name, or section context. Keep concise (1-2 lines max)
- **Footers**: Commonly include page numbers, dates, or copyright information
- **Page Numbers**: Set include_page_number to true in header or footer as appropriate
- **Dates**: Set include_date to true in footer if document needs timestamp
- **Leave empty**: If no header/footer is needed, leave the text field empty
- **Professional documents**: Usually have footers with page numbers
- **Reports/Academic**: Often have headers with document title and footers with page numbers

Generate the outline now:
"""
)

docx_outline_chain = docx_outline_prompt | docx_outline_model | StrOutputParser()

# -------------------------------------------------------------------------
# Define a GPT model and prompt chain for generating DOCX section content
# -------------------------------------------------------------------------
docx_section_model = ChatGoogleGenerativeAI(
    api_key=settings.GOOGLE_API_KEY,
    model="gemini-2.5-flash-preview-04-17",
    temperature=0.5,
    top_p=0.9,
)

docx_section_prompt = ChatPromptTemplate.from_template(
    """
## Role
You're a professional writer creating content for a DOCX document. Generate **Markdown** content that will be converted to a styled DOCX file.

## Objective
Produce well-structured Markdown content for the current section. Use proper Markdown syntax for headings, lists, and formatting. Follow the provided examples closely.

## Context
- **Full Outline**: {full_outline}
- **Current Section**: {heading}
- **Section Description**: {placeholder_content}
- **Subtask Description**: {subtask_desc}
- **User Input**: {user_input}
- **Dependencies**: {deps_info}

## Previous Content
{history}

## Image Support
**IMPORTANT**: Include images to enhance the document. Use this exact syntax:
- For user-uploaded images: `![Descriptive alt text](user_uploads/filename.jpg)`
- For web images: `![Descriptive alt text](https://example.com/image.jpg)`
- **For new images**: When you want to include an image but don't have a specific URL, use: `![Descriptive alt text](placeholder_image)`

**DO NOT** write descriptive text like "*[Image: description]*" or similar. Always use proper Markdown image syntax. The system will automatically find appropriate images for placeholder URLs.

## Examples
{section_examples}

## IMPORTANT
- DO NOT repeat any information from the history in your response
- Only return Markdown content for this section
- Use standard Markdown syntax for formatting
- Include relevant images using `![alt text](placeholder_image)` syntax - the system will find appropriate images automatically
- DO NOT include placeholder text for images like "*[Image: description]*" - always use proper Markdown image syntax

## Markdown Guidelines
- Use `#` for main headings, `##` for subheadings
- Use **bold** and *italic* for emphasis
- Keep paragraphs concise and well-structured
- Ensure content flows naturally from previous sections

## List Formatting Rules (CRITICAL):
**For Sequential/Ordered Content (steps, processes, chronological items, rankings):**
- ALWAYS use numbered lists with this EXACT format: `1. `, `2. `, `3. `
- Examples: steps in a process, chronological events, ranked items, sequential instructions

**For Non-Sequential Content (features, benefits, examples, general points):**
- Use bullet points with this EXACT format: `- ` (dash + space)
- Examples: features, benefits, examples, general characteristics

**IMPORTANT**:
- Use `1. ` (number + period + space) for numbered lists, NOT `* ` or `- `
- Use `- ` (dash + space) for bullet points, NOT `* `
- Choose numbered lists when order/sequence matters
- Choose bullet points when order doesn't matter

## Table Formatting Rules (CRITICAL):
**When to Use Tables:**
- Structured data with multiple attributes (comparisons, specifications, schedules)
- Data that has clear rows and columns relationship
- Information that benefits from tabular presentation

**Table Syntax (EXACT FORMAT REQUIRED):**
```
| Header 1 | Header 2 | Header 3 |
|----------|----------|----------|
| Data 1   | Data 2   | Data 3   |
| Data 4   | Data 5   | Data 6   |
```

**Table Rules:**
- Start and end each row with `|` (pipe character)
- Separate columns with ` | ` (space + pipe + space)
- Include header row followed by separator row with dashes
- Use **bold** for important data, *italic* for emphasis within cells
- Keep cell content concise and readable

**Table Examples:**
```
| Feature | Status | Priority |
|---------|--------|----------|
| **User Authentication** | ✅ Complete | High |
| *Data Export* | 🔄 In Progress | Medium |
| API Integration | ❌ Pending | Low |
```

## Current Section to Write
**Section Heading:** {heading}
**Description:** {placeholder_content}

Write comprehensive, engaging content for this section in Markdown format. Do NOT repeat information from previous sections.

Pay special attention to list formatting - use numbered lists (`1. `, `2. `) for sequential content and bullet points (`- `) for non-sequential content.

## OUTPUT:
"""
)

docx_section_chain = docx_section_prompt | docx_section_model | StrOutputParser()


class DOCXGeneratorAgent(BaseAgent):
    """
    Worker agent capable of generating DOCX documents from Markdown.

    Inherits from BaseAgent and implements task-specific logic to:
      1. Parse the task details (subtask_id, subtask_description, user_message, deps_info).
      2. Generate a DOCX outline based on the subtask details (using GPT).
      3. Save that exact JSON outline (raw) in a markdown (.md) file.
      4. Iterate over each section to generate Markdown content.
      5. Parse Markdown and convert to DOCX format using python-docx.
      6. Upload DOCX to R2 and generate presigned download link.
      7. Report completion to the manager.
    """

    def __init__(self, agent_id: str, chat_id: str):
        """
        Initializes the DOCXGeneratorAgent with a unique agent ID.

        Args:
            agent_id (str): Unique identifier for the DOCX generator agent.
            chat_id (str): Chat session identifier.
        """
        super().__init__(agent_id, agent_type="Document Generator Tool")
        self.chat_id = chat_id
        self.full_outline_text = ""
        self.uploaded_images = []
        self.fonts_config = {}

    async def execute_task(self, task_details: Dict[str, Any]):
        """
        Steps:
        1) Generate a DOCX outline in JSON (raw).
        2) Save it as-is to a .md file.
        3) Iterate over each outline section, generating Markdown content.
        4) Save combined Markdown content to another .md file.
        5) Parse Markdown and convert to DOCX using python-docx.
        6) Upload DOCX to R2 and generate presigned download link.
        7) Report completion to the manager.
        """
        try:
            self.start_timer("docx agent")
            
            # Extract task details
            subtask_id = task_details.get("subtask_id", "unknown")
            subtask_description = task_details.get("subtask_description", "")
            user_input = task_details.get("user_message", "")
            deps_info = task_details.get("deps_info", "")
            
            # Process uploaded images
            raw_uploads = task_details.get("uploaded_images", [])
            self.uploaded_images = self._normalize_uploads(raw_uploads)
            
            # If we have uploaded images, append their names to deps_info
            if self.uploaded_images:
                deps_info = (
                    deps_info + "\nUploaded images: " + ", ".join(self.uploaded_images)
                )
            
            self.current_task = task_details
            self.logger.info(f"[DOCXGenerator] Starting task {subtask_id}")

            # ----------------------------
            # (1) Generate & Save Outline
            # ----------------------------
            self.logger.info(
                f"[DOCXGenerator] Outline generation input => subtask_desc='{subtask_description}'"
            )
            outline_plan = await self.determine_docx_outline(
                subtask_desc=subtask_description,
                user_input=user_input,
                deps_info=deps_info,
            )
            # Store full outline text for later section prompts
            self.full_outline_text = json.dumps(
                outline_plan, ensure_ascii=False, indent=2
            )
            # Store font configuration for use in content creation
            self.fonts_config = outline_plan.get("fonts", {})
            self.logger.info(f"[DOCXGenerator] Outline generated successfully with fonts: {self.fonts_config}")
            self.save_outline_to_md_file(subtask_id, outline_plan)

            # ----------------------------
            # (2) Generate Section Content
            # ----------------------------
            self.runtime_log("Generating Content")
            full_markdown_content = await self.generate_full_docx_content(
                outline_plan=outline_plan,
                subtask_desc=subtask_description,
                user_input=user_input,
                deps_info=deps_info,
            )
            
            self.save_final_content_to_md_file(subtask_id, full_markdown_content)

            # ----------------------------
            # (3) Generate DOCX Document
            # ----------------------------
            self.runtime_log("Creating DOCX Document")
            timestamp = datetime.now(timezone.utc).strftime("%Y%m%d%H%M%S")
            docx_name = f"docx_result_{subtask_id}_{timestamp}.docx"
            
            # Create DOCX from Markdown
            self.logger.info(f"[DOCXGenerator] Starting DOCX creation from markdown ({len(full_markdown_content)} chars)")
            docx_path = await self.create_docx_from_markdown(
                full_markdown_content,
                outline_plan.get("title", "Document"),
                docx_name,
                outline_plan
            )
            self.logger.info(f"[DOCXGenerator] DOCX creation completed: {docx_path}")

            # ----------------------------
            # (4) Upload to R2 and Report
            # ----------------------------
            self.runtime_log("Uploading Document")
            from app.utils.r2_client import upload_file_to_r2

            upload_file_to_r2(
                docx_path,
                content_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                expires_in=60 * 60 * 24,  # valid for 24 h
            )

            doc_url = f"{settings.BASE_URL}/docs/{docx_path.name}"
            self.runtime_log(f"Generated DOCX: {doc_url}")

            # ----------------------------
            # (5) Create final report
            # ----------------------------
            combined_markdown = (
                f"# Your DOCX Document is Ready\n\n"
                f"**Download DOCX:** [{docx_name}]({doc_url})\n\n"
                f"---\n\n"
                f"## Generated Content:\n\n{full_markdown_content}\n\n"
                f"You can always download your DOCX document at {doc_url}\n\n"
                f"#IMPORTANT: Always give the docx url link to the user in your final message."
            )
            combined_file_path = self.save_combined_content_to_md_file(
                subtask_id, combined_markdown
            )

            # ----------------------------
            # (6) Report to manager
            # ----------------------------
            self.report_results(
                {"subtask_id": subtask_id, "output_file": combined_file_path}
            )

            self.logger.info(
                f"[DOCXGenerator] Subtask {subtask_id} completed with final DOCX document reported."
            )

            self.stop_timer("docx agent")

        except Exception as e:
            self.logger.error(f"[DOCXGenerator] Task execution error: {str(e)}")
            self.handle_error(e)

    def _normalize_uploads(self, uploads: List[str]) -> List[str]:
        """
        Normalize uploaded image filenames.
        
        Args:
            uploads: List of uploaded image filenames
        
        Returns:
            List of normalized filenames
        """
        return [Path(f).name for f in uploads if f]

    async def determine_docx_outline(
        self, subtask_desc: str, user_input: str, deps_info: str
    ) -> Dict[str, Any]:
        """
        Asks GPT to produce a JSON outline for DOCX generation. If it fails, we fallback to a default.
        """

        # Get relevant examples from RAG system
        examples_text = get_docx_examples_text(user_input, k=3)

        input_data = {
            "subtask_desc": subtask_desc,
            "user_input": user_input,
            "deps_info": deps_info,
            "examples": examples_text
        }

        try:
            # Log input tokens
            concatenated = f"{subtask_desc} {user_input} {deps_info} {examples_text}"
            in_tokens = count_tokens(concatenated)
            self.log_input_tokens(docx_outline_model.model, in_tokens)

            # Generate outline
            raw_outline = await self._invoke_chain(docx_outline_chain, input_data, stream=False)

            # Log output tokens
            out_tokens = count_tokens(raw_outline)
            self.log_output_tokens(docx_outline_model.model, out_tokens)

            # Store raw outline for debugging
            self._raw_outline = raw_outline

            # Try to parse JSON
            try:
                # Clean up the response - remove markdown code blocks if present
                cleaned = raw_outline.strip()
                if cleaned.startswith("```json"):
                    cleaned = cleaned[7:]
                if cleaned.startswith("```"):
                    cleaned = cleaned[3:]
                if cleaned.endswith("```"):
                    cleaned = cleaned[:-3]

                outline_plan = json.loads(cleaned.strip())

                # Validate structure
                if not isinstance(outline_plan, dict) or "title" not in outline_plan or "sections" not in outline_plan:
                    raise ValueError("Invalid outline structure")

                # Ensure fonts structure exists, add simple fallback if missing
                if "fonts" not in outline_plan:
                    self.logger.warning("[DOCXGenerator] No fonts in outline, using default fonts")
                    outline_plan["fonts"] = {
                        "primary_font": "Calibri",
                        "heading_font": "Arial",
                        "document_type": "general"
                    }

                return outline_plan

            except (json.JSONDecodeError, ValueError) as e:
                self.logger.warning(f"[DOCXGenerator] JSON parse error: {e}, using fallback")
                return self._get_fallback_outline(subtask_desc, user_input)

        except Exception as e:
            self.logger.error(f"[DOCXGenerator] Outline generation error: {e}")
            return self._get_fallback_outline(subtask_desc, user_input)



    def _get_fallback_outline(self, subtask_desc: str, user_input: str) -> Dict[str, Any]:
        """Generate a fallback outline when LLM fails."""
        return {
            "title": f"Document: {subtask_desc[:50]}...",
            "fonts": {
                "primary_font": "Calibri",
                "heading_font": "Arial",
                "document_type": "general"
            },
            "header": {
                "text": "",
                "include_page_number": False
            },
            "footer": {
                "text": "",
                "include_page_number": True,
                "include_date": False
            },
            "sections": [
                {
                    "heading": "Overview",
                    "placeholder_content": "Introduction and overview of the topic"
                },
                {
                    "heading": "Main Content",
                    "placeholder_content": "Detailed information about the subject"
                },
                {
                    "heading": "Summary",
                    "placeholder_content": "Key takeaways and conclusions"
                }
            ]
        }

    async def generate_full_docx_content(
        self,
        outline_plan: Dict[str, Any],
        subtask_desc: str,
        user_input: str,
        deps_info: str,
    ) -> str:
        """
        Iterates over outline_plan["sections"] and, for each heading,
        asks the LLM to generate Markdown content.

        Returns the concatenated Markdown for all sections.
        """
        sections = outline_plan.get("sections", [])
        content_history = f"# {outline_plan.get('title', 'Document')}\n\n"

        self.runtime_log("Writing the Content")

        for i, section in enumerate(sections):
            heading = section.get("heading", f"Section {i+1}")
            placeholder_content = section.get("placeholder_content", "")

            self.logger.info(f"[DOCXGenerator] Generating content for section: {heading}")

            # Get relevant section examples from RAG system
            section_examples_text = get_docx_section_examples_text(
                f"{heading} {placeholder_content} {user_input}", k=3
            )

            # Pack up all prompt inputs
            prompt_data = {
                "history": content_history,
                "heading": heading,
                "placeholder_content": placeholder_content,
                "subtask_desc": subtask_desc,
                "user_input": user_input,
                "deps_info": deps_info,
                "full_outline": self.full_outline_text,
                "section_examples": section_examples_text,
            }

            # Log input tokens
            concatenated = f"{content_history} {heading} {placeholder_content} {subtask_desc} {user_input} {deps_info} {self.full_outline_text} {section_examples_text}"
            in_tokens = count_tokens(concatenated)
            self.log_input_tokens(docx_section_model.model, in_tokens)

            # Generate section content
            section_content = await self._invoke_chain(docx_section_chain, prompt_data, stream=True)

            # Log output tokens
            out_tokens = count_tokens(section_content)
            self.log_output_tokens(docx_section_model.model, out_tokens)

            # Clean up section content - remove markdown fences
            section_content = self._clean_markdown_fences(section_content.strip())
            if section_content:
                content_history += f"\n\n{section_content}\n"

            self.logger.info(f"[DOCXGenerator] Section '{heading}' content generated ({len(section_content)} chars)")

        return content_history.strip()

    def _clean_markdown_fences(self, content: str) -> str:
        """
        Remove markdown code fence markers from LLM output.

        Args:
            content: Raw content from LLM that may contain ```markdown fences

        Returns:
            Cleaned content without fence markers
        """
        lines = content.splitlines()

        # Remove opening fence (```markdown, ```md, or just ```)
        if lines and lines[0].strip().startswith("```"):
            lines.pop(0)

        # Remove closing fence
        if lines and lines[-1].strip() == "```":
            lines.pop()

        return "\n".join(lines).strip()

    async def create_docx_from_markdown(
        self, markdown_content: str, title: str, filename: str, outline_plan: Dict[str, Any] = None
    ) -> Path:
        """
        Parse Markdown content and create a DOCX document using python-docx.

        Args:
            markdown_content: The Markdown content to convert
            title: Document title
            filename: Output filename

        Returns:
            Path to the created DOCX file
        """
        # Create document
        doc = Document()

        # Set up styles with font configuration
        fonts_config = outline_plan.get("fonts", {}) if outline_plan else {}
        self._setup_docx_styles(doc, fonts_config)

        # Add title
        title_para = doc.add_heading(title, level=0)
        title_para.alignment = 1  # Center alignment
        # Apply heading font to title if configured
        if fonts_config.get("heading_font"):
            for run in title_para.runs:
                run.font.name = fonts_config["heading_font"]

        # Set up headers and footers if specified in outline
        if outline_plan:
            self._setup_headers_footers(doc, outline_plan)

        # Process images first
        self.logger.info(f"[DOCXGenerator] Processing images in markdown content ({len(markdown_content)} chars)")
        image_tags = await self.extract_image_links(markdown_content)
        self.logger.info(f"[DOCXGenerator] Found {len(image_tags)} image references")
        replacements = await self._find_replacements(image_tags)
        self.logger.info(f"[DOCXGenerator] Image replacement completed, processing {len(replacements)} replacements")
        
        # Create a mapping using both URL and label to handle multiple placeholder images
        # Key format: "url|label" to ensure uniqueness
        url_map = {}
        for orig, repl in zip(image_tags, replacements):
            key = f"{orig['url']}|{orig['label']}"
            url_map[key] = repl["url"]

        self.logger.info(f"[DOCXGenerator] Created URL mapping with {len(url_map)} entries:")
        for key, value in url_map.items():
            self.logger.info(f"  '{key}' -> '{value[:80]}...'")

        # Also create a simple URL-only mapping for backward compatibility
        simple_url_map = {orig["url"]: repl["url"] for orig, repl in zip(image_tags, replacements)}
        self.logger.info(f"[DOCXGenerator] Note: Simple URL mapping would have {len(simple_url_map)} entries (may lose data if duplicate URLs)")

        # Parse and add content
        lines = markdown_content.split('\n')
        i = 0
        current_list = None
        list_type = None

        # Cache to prevent downloading the same image multiple times
        downloaded_images = set()

        # Safety counter to prevent infinite loops
        loop_iterations = 0
        max_iterations = len(lines) * 2  # Allow some extra iterations for safety

        self.logger.info(f"[DOCXGenerator] Starting markdown processing with {len(lines)} lines")

        while i < len(lines):
            # Safety check to prevent infinite loops
            loop_iterations += 1
            if loop_iterations > max_iterations:
                self.logger.error(f"[DOCXGenerator] INFINITE LOOP DETECTED! Breaking after {loop_iterations} iterations at line {i}")
                break

            line = lines[i].strip()

            # Add periodic logging to detect infinite loops
            if i % 10 == 0:
                self.logger.info(f"[DOCXGenerator] Processing line {i}/{len(lines)}: '{line[:50]}...'")

            if not line:
                # Empty line - end current list if any
                current_list = None
                list_type = None
                i += 1
                continue

            # Handle image lines (Markdown format)
            if line.startswith('![') and '](' in line and ')' in line:
                current_list = None
                list_type = None

                # Extract URL and alt text
                match = re.match(r'!\[(.*?)\]\((.*?)\)', line)
                if match:
                    alt_text, url = match.groups()
                    self.logger.info(f"[DOCXGenerator] Phase 2: Line {i}: Processing markdown image - alt='{alt_text[:50]}...', url='{url}'")

                    # Handle cases where there's no URL (just descriptive alt text)
                    if not url.strip() or not self._is_valid_url(url.strip()):
                        self.logger.info(f"[DOCXGenerator] Phase 2: Line {i}: Invalid URL '{url}', checking Phase 1 replacements")

                        # Create the same key format used in Phase 1: "url|label"
                        lookup_key = f"{url.strip()}|{alt_text.strip()}"
                        replacement_url = url_map.get(lookup_key)

                        self.logger.info(f"[DOCXGenerator] Phase 2: Line {i}: Looking up key '{lookup_key}' -> {replacement_url[:80] + '...' if replacement_url else 'None'}")

                        if replacement_url and replacement_url != url.strip():
                            # Check if we've already downloaded this image
                            cache_key = f"{alt_text}:{replacement_url}"
                            if cache_key in downloaded_images:
                                self.logger.info(f"[DOCXGenerator] Phase 2: Line {i}: Skipping already downloaded image for '{alt_text[:50]}...'")
                            else:
                                # Use the replacement found in Phase 1
                                self.logger.info(f"[DOCXGenerator] Phase 2: Line {i}: Using Phase 1 replacement for '{alt_text[:50]}...': {replacement_url[:80]}...")
                                await self._add_image_to_docx(doc, replacement_url)
                                downloaded_images.add(cache_key)
                        else:
                            # Skip the image if no replacement was found in Phase 1
                            self.logger.info(f"[DOCXGenerator] Phase 2: Line {i}: Skipping image with description: {alt_text[:50]}... (no replacement found in Phase 1)")
                        # Don't use continue here - let the loop increment i naturally

                    # Use replacement URL if available (check both new and old mapping formats)
                    lookup_key = f"{url}|{alt_text.strip()}"
                    final_url = url_map.get(lookup_key, url)  # Try new format first
                    if final_url == url:
                        # Fallback to simple URL mapping for backward compatibility
                        final_url = simple_url_map.get(url, url)

                    cache_key = f"{alt_text}:{final_url}"
                    if cache_key in downloaded_images:
                        self.logger.info(f"[DOCXGenerator] Phase 2: Line {i}: Skipping already downloaded image for '{alt_text[:50]}...'")
                    else:
                        self.logger.info(f"[DOCXGenerator] Phase 2: Line {i}: Using URL for '{alt_text[:50]}...': {final_url[:80]}... (original: {url})")
                        await self._add_image_to_docx(doc, final_url)
                        downloaded_images.add(cache_key)
            


            # Handle headings
            elif line.startswith('#'):
                current_list = None
                list_type = None
                level = len(line) - len(line.lstrip('#'))
                heading_text = line.lstrip('#').strip()
                if heading_text and level <= 3:  # Only handle up to level 3 headings
                    heading_para = doc.add_heading(heading_text, level=level)
                    # Apply heading font if configured
                    if self.fonts_config.get("heading_font"):
                        for run in heading_para.runs:
                            run.font.name = self.fonts_config["heading_font"]

            # Handle bullet lists (support both - and * formats)
            elif line.startswith('- ') or line.startswith('* '):
                # Handle different spacing patterns: "- ", "* ", "*   "
                if line.startswith('- '):
                    list_item_text = line[2:].strip()
                elif line.startswith('*   '):
                    list_item_text = line[4:].strip()
                else:  # line.startswith('* ')
                    list_item_text = line[2:].strip()

                if current_list is None or list_type != 'bullet':
                    current_list = []
                    list_type = 'bullet'
                current_list.append(list_item_text)

                # Check if next line continues the list
                next_line = lines[i + 1].strip() if i + 1 < len(lines) else ""
                if not (next_line.startswith('- ') or next_line.startswith('* ')):
                    # End of list, add all items with formatting
                    for item in current_list:
                        para = doc.add_paragraph(style='List Bullet')
                        self._convert_markdown_to_docx_runs(para, item)
                    current_list = None
                    list_type = None

            # Handle numbered lists
            elif re.match(r'^\d+\.\s', line):
                list_item_text = re.sub(r'^\d+\.\s', '', line).strip()
                if current_list is None or list_type != 'number':
                    current_list = []
                    list_type = 'number'
                current_list.append(list_item_text)

                # Check if next line continues the list
                if i + 1 >= len(lines) or not re.match(r'^\d+\.\s', lines[i + 1].strip()):
                    # End of list, add all items with formatting
                    for item in current_list:
                        para = doc.add_paragraph(style='List Number')
                        self._convert_markdown_to_docx_runs(para, item)
                    current_list = None
                    list_type = None

            # Handle tables
            elif '|' in line and line.count('|') >= 2:
                current_list = None
                list_type = None

                # Collect all table lines
                table_lines = []
                table_start_index = i

                while i < len(lines) and lines[i].strip() and '|' in lines[i] and lines[i].count('|') >= 2:
                    table_lines.append(lines[i].strip())
                    i += 1

                # Process the collected table
                if self._validate_table_structure(table_lines):
                    self._create_docx_table(doc, table_lines)
                else:
                    # Fallback: treat as regular paragraphs
                    self.logger.warning(f"[DOCXGenerator] Invalid table structure at line {table_start_index}, treating as paragraphs")
                    for table_line in table_lines:
                        para = doc.add_paragraph()
                        self._convert_markdown_to_docx_runs(para, table_line)

                i -= 1  # Adjust for loop increment

            # Handle regular paragraphs
            else:
                current_list = None
                list_type = None
                if line:
                    # Add paragraph with proper markdown formatting
                    para = doc.add_paragraph()
                    self._convert_markdown_to_docx_runs(para, line)

            i += 1

        # Log completion of markdown processing
        self.logger.info(f"[DOCXGenerator] Markdown processing completed. Processed {len(lines)} lines. Downloaded {len(downloaded_images)} unique images.")

        # Save document
        self.logger.info(f"[DOCXGenerator] Preparing to save document...")
        docx_dir = get_public_docx_dir(self.chat_id)
        docx_dir.mkdir(parents=True, exist_ok=True)
        docx_path = docx_dir / filename

        self.logger.info(f"[DOCXGenerator] Saving document to: {docx_path}")
        doc.save(str(docx_path))
        self.logger.info(f"[DOCXGenerator] DOCX saved successfully to: {docx_path}")

        return docx_path

    def _validate_table_structure(self, table_lines: List[str]) -> bool:
        """
        Validate that table lines form a proper markdown table structure.

        Args:
            table_lines: List of potential table lines

        Returns:
            True if valid table structure, False otherwise
        """
        if len(table_lines) < 2:
            return False

        # Check that all rows start and end with |
        for line in table_lines:
            if not (line.startswith('|') and line.endswith('|')):
                return False

        # Check that all rows have the same number of columns
        col_counts = []
        for line in table_lines:
            # Count columns by counting pipes and subtracting 1 (for start/end pipes)
            col_count = line.count('|') - 1
            col_counts.append(col_count)

        # All rows should have the same column count
        if len(set(col_counts)) != 1:
            return False

        # Check for separator row (second row should be dashes)
        if len(table_lines) >= 2:
            separator_row = table_lines[1]
            # Remove outer pipes and split
            cells = [cell.strip() for cell in separator_row[1:-1].split('|')]
            # Check if all cells contain only dashes and spaces
            for cell in cells:
                if not all(c in '-| ' for c in cell):
                    return False

        return True

    def _create_docx_table(self, doc: Document, table_lines: List[str]):
        """
        Create a DOCX table from markdown table lines.

        Args:
            doc: DOCX document object
            table_lines: List of validated markdown table lines
        """
        # Parse table structure
        rows = []
        for line in table_lines:
            if line.startswith('|') and line.endswith('|'):
                # Remove outer pipes and split
                cells = [cell.strip() for cell in line[1:-1].split('|')]
                rows.append(cells)

        # Remove separator row (usually second row with dashes)
        if len(rows) >= 2:
            separator_cells = rows[1]
            if all(cell.strip().replace('-', '').replace(' ', '') == '' for cell in separator_cells):
                rows.pop(1)  # Remove separator row

        if not rows or len(rows[0]) == 0:
            self.logger.warning("[DOCXGenerator] Empty table after processing, skipping")
            return

        try:
            # Create DOCX table
            table = doc.add_table(rows=len(rows), cols=len(rows[0]))
            table.style = 'Table Grid'

            # Populate table
            for row_idx, row_data in enumerate(rows):
                for col_idx, cell_data in enumerate(row_data):
                    if col_idx < len(table.rows[row_idx].cells):
                        cell = table.rows[row_idx].cells[col_idx]

                        # Clear default paragraph and add formatted content
                        if cell_data.strip():
                            cell.paragraphs[0].clear()
                            self._convert_markdown_to_docx_runs(cell.paragraphs[0], cell_data)

                        # Style header row
                        if row_idx == 0:
                            for paragraph in cell.paragraphs:
                                for run in paragraph.runs:
                                    run.bold = True

            # Apply table formatting
            self._format_docx_table(table)

            self.logger.info(f"[DOCXGenerator] Created table with {len(rows)} rows and {len(rows[0])} columns")

        except Exception as e:
            self.logger.error(f"[DOCXGenerator] Error creating table: {e}")
            # Fallback: add as paragraphs
            for line in table_lines:
                para = doc.add_paragraph()
                self._convert_markdown_to_docx_runs(para, line)

    def _format_docx_table(self, table):
        """
        Apply formatting to a DOCX table.

        Args:
            table: DOCX table object to format
        """
        try:
            from docx.shared import Inches

            # Set reasonable column widths
            total_width = Inches(6.0)  # Total table width
            col_width = total_width / len(table.columns)

            for column in table.columns:
                column.width = col_width

        except Exception as e:
            self.logger.warning(f"[DOCXGenerator] Could not set table column widths: {e}")

    def _setup_docx_styles(self, doc: Document, fonts_config: Dict[str, str] = None):
        """
        Set up custom styles for the DOCX document with font configuration.

        Args:
            doc: DOCX document object
            fonts_config: Dictionary containing font configuration (primary_font, heading_font, document_type)
        """
        styles = doc.styles

        # Get font configuration with fallbacks
        if not fonts_config:
            fonts_config = {"primary_font": "Calibri", "heading_font": "Arial", "document_type": "general"}

        primary_font = fonts_config.get("primary_font", "Calibri")
        heading_font = fonts_config.get("heading_font", "Arial")

        self.logger.info(f"[DOCXGenerator] Setting up styles with primary_font='{primary_font}', heading_font='{heading_font}'")

        # Ensure we have the basic styles we need
        try:
            # Configure Normal style with primary font
            normal_style = styles['Normal']
            normal_style.font.name = primary_font
            self.logger.info(f"[DOCXGenerator] Set Normal style font to '{primary_font}'")

            # Configure heading styles with heading font
            for level in range(1, 4):  # Heading 1, 2, 3
                heading_style_name = f'Heading {level}'
                if heading_style_name in [s.name for s in styles]:
                    heading_style = styles[heading_style_name]
                    heading_style.font.name = heading_font
                    self.logger.info(f"[DOCXGenerator] Set {heading_style_name} font to '{heading_font}'")

            # These styles should exist by default, but let's make sure
            if 'List Bullet' not in [s.name for s in styles]:
                bullet_style = styles.add_style('List Bullet', WD_STYLE_TYPE.PARAGRAPH)
                bullet_style.base_style = styles['Normal']
                bullet_style.font.name = primary_font
            else:
                bullet_style = styles['List Bullet']
                bullet_style.font.name = primary_font

            if 'List Number' not in [s.name for s in styles]:
                number_style = styles.add_style('List Number', WD_STYLE_TYPE.PARAGRAPH)
                number_style.base_style = styles['Normal']
                number_style.font.name = primary_font
            else:
                number_style = styles['List Number']
                number_style.font.name = primary_font

            # Ensure table styles are available
            if 'Table Grid' not in [s.name for s in styles]:
                # Table Grid should exist by default, but create if missing
                table_style = styles.add_style('Table Grid', WD_STYLE_TYPE.TABLE)
                self.logger.info("[DOCXGenerator] Created Table Grid style")

        except Exception as e:
            self.logger.warning(f"[DOCXGenerator] Style setup warning: {e}")

    def _setup_headers_footers(self, doc: Document, outline_plan: Dict[str, Any]):
        """
        Set up headers and footers for the DOCX document based on the outline plan.

        Args:
            doc: DOCX document object
            outline_plan: The outline plan containing header/footer specifications
        """
        try:
            # Get the first section (default section)
            section = doc.sections[0]

            # Set up header if specified
            header_config = outline_plan.get("header", {})
            if header_config and header_config.get("text", "").strip():
                self._setup_header(section, header_config)
                self.logger.info(f"[DOCXGenerator] Header configured: '{header_config.get('text', '')[:50]}...'")

            # Set up footer if specified
            footer_config = outline_plan.get("footer", {})
            if footer_config and (footer_config.get("text", "").strip() or
                                footer_config.get("include_page_number", False) or
                                footer_config.get("include_date", False)):
                self._setup_footer(section, footer_config)
                self.logger.info(f"[DOCXGenerator] Footer configured with text: '{footer_config.get('text', '')[:50]}...'")

        except Exception as e:
            self.logger.warning(f"[DOCXGenerator] Header/footer setup warning: {e}")

    def _setup_header(self, section, header_config: Dict[str, Any]):
        """
        Set up header for a document section.

        Args:
            section: Document section object
            header_config: Header configuration from outline
        """
        header = section.header
        header.is_linked_to_previous = False  # Create a new header definition

        # Clear existing content and add new content
        header_para = header.paragraphs[0]
        header_para.clear()

        # Build header text
        header_text = header_config.get("text", "").strip()
        include_page_number = header_config.get("include_page_number", False)

        if include_page_number and header_text:
            # Add text with page number (right-aligned)
            header_para.text = f"{header_text}\tPage "
            # Add page number field
            from docx.oxml.shared import qn
            from docx.oxml import parse_xml
            page_num_run = header_para.add_run()
            fldChar1 = parse_xml(r'<w:fldChar w:fldCharType="begin" xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main"/>')
            page_num_run._r.append(fldChar1)
            instrText = parse_xml(r'<w:instrText xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main"> PAGE </w:instrText>')
            page_num_run._r.append(instrText)
            fldChar2 = parse_xml(r'<w:fldChar w:fldCharType="end" xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main"/>')
            page_num_run._r.append(fldChar2)
        elif include_page_number:
            # Just page number
            header_para.text = "Page "
            from docx.oxml.shared import qn
            from docx.oxml import parse_xml
            page_num_run = header_para.add_run()
            fldChar1 = parse_xml(r'<w:fldChar w:fldCharType="begin" xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main"/>')
            page_num_run._r.append(fldChar1)
            instrText = parse_xml(r'<w:instrText xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main"> PAGE </w:instrText>')
            page_num_run._r.append(instrText)
            fldChar2 = parse_xml(r'<w:fldChar w:fldCharType="end" xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main"/>')
            page_num_run._r.append(fldChar2)
        else:
            # Just text
            header_para.text = header_text

        # Apply header style
        header_para.style = 'Header'

    def _setup_footer(self, section, footer_config: Dict[str, Any]):
        """
        Set up footer for a document section.

        Args:
            section: Document section object
            footer_config: Footer configuration from outline
        """
        footer = section.footer
        footer.is_linked_to_previous = False  # Create a new footer definition

        # Clear existing content and add new content
        footer_para = footer.paragraphs[0]
        footer_para.clear()

        # Build footer components
        footer_text = footer_config.get("text", "").strip()
        include_page_number = footer_config.get("include_page_number", False)
        include_date = footer_config.get("include_date", False)

        # Build footer content with proper spacing
        footer_parts = []

        if footer_text:
            footer_parts.append(footer_text)

        if include_date:
            from datetime import datetime
            date_str = datetime.now().strftime("%B %d, %Y")
            footer_parts.append(date_str)

        if include_page_number:
            footer_parts.append("Page ")

        # Join parts with tabs for proper spacing (left, center, right alignment)
        if len(footer_parts) == 1:
            footer_content = footer_parts[0]
        elif len(footer_parts) == 2:
            footer_content = f"{footer_parts[0]}\t\t{footer_parts[1]}"
        else:
            footer_content = f"{footer_parts[0]}\t{footer_parts[1]}\t{footer_parts[2]}"

        footer_para.text = footer_content

        # Add page number field if requested
        if include_page_number:
            from docx.oxml import parse_xml
            page_num_run = footer_para.add_run()
            fldChar1 = parse_xml(r'<w:fldChar w:fldCharType="begin" xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main"/>')
            page_num_run._r.append(fldChar1)
            instrText = parse_xml(r'<w:instrText xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main"> PAGE </w:instrText>')
            page_num_run._r.append(instrText)
            fldChar2 = parse_xml(r'<w:fldChar w:fldCharType="end" xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main"/>')
            page_num_run._r.append(fldChar2)

        # Apply footer style
        footer_para.style = 'Footer'

    def _convert_markdown_to_docx_runs(self, paragraph, text: str):
        """
        Convert markdown text directly to DOCX runs with proper formatting.

        Handles nested formatting by processing bold first, then italic within each section.
        This ensures proper hierarchy: **bold *and italic* text** works correctly.

        Args:
            paragraph: DOCX paragraph object to add runs to
            text: Text with markdown formatting (e.g., "**bold** and *italic*")
        """
        current_pos = 0

        # Find all **bold** sections first
        bold_pattern = r'\*\*(.*?)\*\*'
        for match in re.finditer(bold_pattern, text):
            # Add text before bold section
            if match.start() > current_pos:
                before_text = text[current_pos:match.start()]
                self._add_text_with_italic(paragraph, before_text, bold=False)

            # Add bold text (may contain italic formatting)
            bold_text = match.group(1)
            self._add_text_with_italic(paragraph, bold_text, bold=True)
            current_pos = match.end()

        # Add remaining text after last bold section
        if current_pos < len(text):
            remaining = text[current_pos:]
            self._add_text_with_italic(paragraph, remaining, bold=False)

    def _add_text_with_italic(self, paragraph, text: str, bold: bool):
        """
        Add text to paragraph, processing italic formatting within the text.

        Args:
            paragraph: DOCX paragraph object to add runs to
            text: Text that may contain *italic* formatting
            bold: Whether this text should be bold
        """
        if not text:
            return

        current_pos = 0

        # Find all *italic* sections within this text
        italic_pattern = r'\*(.*?)\*'
        for match in re.finditer(italic_pattern, text):
            # Add text before italic section
            if match.start() > current_pos:
                before_text = text[current_pos:match.start()]
                if before_text:
                    run = paragraph.add_run(before_text)
                    run.bold = bold
                    # Apply font from configuration
                    if self.fonts_config.get("primary_font"):
                        run.font.name = self.fonts_config["primary_font"]

            # Add italic text
            italic_text = match.group(1)
            if italic_text:
                run = paragraph.add_run(italic_text)
                run.bold = bold
                run.italic = True
                # Apply font from configuration
                if self.fonts_config.get("primary_font"):
                    run.font.name = self.fonts_config["primary_font"]
            current_pos = match.end()

        # Add remaining text after last italic section
        if current_pos < len(text):
            remaining = text[current_pos:]
            if remaining:
                run = paragraph.add_run(remaining)
                run.bold = bold
                # Apply font from configuration
                if self.fonts_config.get("primary_font"):
                    run.font.name = self.fonts_config["primary_font"]

    async def _invoke_chain(
        self, chain: Any, payload: Dict[str, Any], *, stream: bool = True
    ) -> str:
        """Run ``chain`` with ``payload`` and return the combined result.

        When ``stream`` is ``True`` runtime log events are emitted for each
        chunk received from ``chain``.
        """
        logger.info(
            "[docx_generator] LLM input payload:\n%s", json.dumps(payload, indent=2)
        )

        chunks: List[str] = []
        if stream:
            self.runtime_log_stream("stream")

        async for chunk in chain.astream(payload):
            text = chunk if isinstance(chunk, str) else str(chunk)
            if stream:
                self.runtime_log_stream(text)
            chunks.append(text)

        if stream:
            self.runtime_log_stream("stream")

        return "".join(chunks).strip()

    def save_outline_to_md_file(self, subtask_id: str, outline_plan: Dict[str, Any]) -> str:
        """Save the outline plan to a markdown file."""
        kb_dir = get_knowledge_base_dir(self.chat_id)
        kb_dir.mkdir(parents=True, exist_ok=True)

        outline_file = kb_dir / f"docx_outline_{subtask_id}.md"
        outline_content = f"# DOCX Outline for Subtask {subtask_id}\n\n"
        outline_content += f"```json\n{json.dumps(outline_plan, ensure_ascii=False, indent=2)}\n```\n"

        outline_file.write_text(outline_content, encoding="utf-8")
        self.logger.info(f"[DOCXGenerator] Outline saved to: {outline_file}")
        return str(outline_file)

    def save_final_content_to_md_file(self, subtask_id: str, content: str) -> str:
        """Save the final Markdown content to a file."""
        kb_dir = get_knowledge_base_dir(self.chat_id)
        kb_dir.mkdir(parents=True, exist_ok=True)

        content_file = kb_dir / f"docx_content_{subtask_id}.md"
        final_content = f"# DOCX Content for Subtask {subtask_id}\n\n{content}"

        content_file.write_text(final_content, encoding="utf-8")
        self.logger.info(f"[DOCXGenerator] Content saved to: {content_file}")
        return str(content_file)

    def save_combined_content_to_md_file(self, subtask_id: str, combined_content: str) -> str:
        """Save the combined final content with download link to a file."""
        kb_dir = get_knowledge_base_dir(self.chat_id)
        kb_dir.mkdir(parents=True, exist_ok=True)

        combined_file = kb_dir / f"docx_final_{subtask_id}.md"
        combined_file.write_text(combined_content, encoding="utf-8")
        self.logger.info(f"[DOCXGenerator] Combined content saved to: {combined_file}")
        return str(combined_file)

    def report_results(self, results: Dict[str, Any]):
        """
        Reports the results of the subtask execution to the manager.

        Sends a message to the manager containing the subtask ID and the output file path.

        Args:
            results (Dict[str, Any]): Dictionary containing:
                - "subtask_id": Unique identifier for the subtask.
                - "output_file": Path to the saved markdown file.
        """
        subtask_id = results["subtask_id"]
        output_file = results["output_file"]
        send_message(
            self.chat_id,
            sender="worker",
            receiver="manager",
            message={"subtask_id": subtask_id, "output_file": output_file},
        )
        self.logger.info(f"[DOCXGenerator] Reported {subtask_id}: {output_file}")

    async def extract_image_links(self, markdown_content: str) -> List[Dict[str, Any]]:
        """
        Extract image references from markdown content.
        Returns a list of dicts with image info.

        Supports Markdown format ![alt](url) only.
        """
        # Match Markdown image syntax: ![alt text](url)
        md_images = re.findall(r'!\[(.*?)\]\((.*?)\)', markdown_content)

        images = []
        # Process Markdown format
        for alt, url in md_images:
            exists = await self._url_exists(url)
            images.append({"url": url, "label": alt, "exists": exists})
            self.logger.debug(
                f"[DOCXGenerator] extract_image_links → {url!r} alt={alt!r} exists={exists}"
            )

        return images

    async def _url_exists(self, url: str, min_width: int = 100, min_height: int = 100) -> bool:
        """
        Return True if the URL points to an image that actually loads and
        whose dimensions are at least min_width x min_height.
        We issue a HEAD to weed out 4xx/5xx only, then do a GET with a UA override.
        """
        self.logger.debug(f"[DOCXGenerator] Checking url {url} exists and is image]")
        # Handle user uploads
        if url.startswith("user_uploads/"):
            local = get_user_uploads_dir(self.chat_id) / url.split("/", 1)[-1]
            return local.is_file()

        headers = {"User-Agent": "Mozilla/5.0 (compatible; DOCXGeneratorAgent/1.0)"}
        try:
            async with httpx.AsyncClient(timeout=5.0) as client:
                # Quick HEAD request to check status
                try:
                    head = await client.head(url, headers=headers, follow_redirects=True)
                    if head.status_code >= 400:
                        return False
                except Exception:
                    pass  # Fall back to GET below

                # Full GET to fetch image bytes
                resp = await client.get(url, headers=headers, follow_redirects=True)
                resp.raise_for_status()

                # Strict content-type check
                ctype = resp.headers.get("Content-Type", "")
                if not ctype.startswith("image/"):
                    return False

                # Load into PIL and check dimensions
                from PIL import Image
                from io import BytesIO
                img = Image.open(BytesIO(resp.content))
                w, h = img.size
                ok = w >= min_width and h >= min_height
                self.logger.debug(f"[DOCXGenerator] _url_exists('{url}') → {ok} ({w}×{h})")
                return ok

        except Exception as e:
            self.logger.warning(f"[DOCXGenerator] URL existence/size check failed for {url}: {e}")
            return False

    async def _find_replacements(self, tags: List[Dict[str, Any]]) -> List[Dict[str, str]]:
        """
        For each tag where exists=False:
        1) Try Brave Image Search for replacements
        2) Validate each candidate
        3) Pick the first valid candidate or fall back to original
        """
        self.logger.info(f"[DOCXGenerator] Phase 1: _find_replacements called with {len(tags)} tags")
        for i, tag in enumerate(tags):
            self.logger.info(f"[DOCXGenerator] Phase 1: Tag {i}: url='{tag['url']}', label='{tag['label']}', exists={tag['exists']}")

        sem = asyncio.Semaphore(5)
        # Cache to prevent duplicate searches for the same query - shared across all parallel tasks
        search_cache = {}
        cache_lock = asyncio.Lock()

        async def lookup(tag):
            # If the image already exists, no need to replace
            if tag["exists"]:
                return {"url": tag["url"], "label": tag["label"]}

            # Check cache first to avoid duplicate searches
            cache_key = tag["label"].lower().strip()
            async with cache_lock:
                if cache_key in search_cache:
                    cached_result = search_cache[cache_key]
                    if cached_result:
                        return {"url": cached_result, "label": tag["label"]}
                    else:
                        # Previous search failed, return original
                        return {"url": tag["url"], "label": tag["label"]}
            
            for attempt in range(1, 4):
                try:
                    async with sem:
                        async with httpx.AsyncClient(timeout=10.0) as client:
                            resp = await client.get(
                                "https://api.search.brave.com/res/v1/images/search",
                                headers={
                                    "Accept": "application/json",
                                    "X-Subscription-Token": settings.BRAVE_SEARCH_API,
                                },
                                params={
                                    "q": tag["label"],
                                    "count": 5,
                                    "search_lang": "en",
                                },
                            )
                            resp.raise_for_status()
                            items = resp.json().get("results", [])
                    
                    # Build candidate list: properties.url first, then thumbnail.src
                    cands = []
                    for it in items:
                        full = it.get("properties", {}).get("url")
                        thumb = it.get("thumbnail", {}).get("src")
                        if full:
                            cands.append(full)
                        if thumb:
                            cands.append(thumb)
                    
                    # Check each candidate
                    for url in cands:
                        if await self._url_exists(url):
                            # Cache the successful result
                            async with cache_lock:
                                search_cache[cache_key] = url
                            self.logger.info(
                                f"[DOCXGenerator] chosen replacement for '{tag['label']}': {url}"
                            )
                            return {"url": url, "label": tag["label"]}

                    # If we get here, no candidates worked - no need to retry
                    self.logger.warning(
                        f"[DOCXGenerator] No valid replacements for {tag['url']} on attempt {attempt}"
                    )
                    break
                except Exception as e:
                    self.logger.warning(
                        f"[DOCXGenerator] Brave search error on attempt {attempt}: {e}"
                    )
                    if attempt < 3:
                        # Small backoff before retrying
                        await asyncio.sleep(1)

            # Cache the failed search to prevent retries
            async with cache_lock:
                search_cache[cache_key] = None
            # Fallback to original
            self.logger.info(
                f"[DOCXGenerator] no replacement found for '{tag['label']}', using original"
            )
            return {"url": tag["url"], "label": tag["label"]}
        
        # Process all tags in parallel (following PDF agent pattern)
        return await asyncio.gather(*(lookup(tag) for tag in tags))

    def _is_valid_url(self, url: str) -> bool:
        """
        Check if a string looks like a valid URL.

        Args:
            url: String to check

        Returns:
            True if it looks like a valid URL, False otherwise
        """
        if not url:
            return False

        # Check for common URL patterns
        url_lower = url.lower()
        return (
            url_lower.startswith(('http://', 'https://')) or
            url_lower.startswith('user_uploads/') or
            ('.' in url and not ' ' in url and len(url) > 4)
        )



    async def _add_image_to_docx(self, doc: Document, url: str):
        """
        Add an image to the DOCX document from a URL or local file.

        Args:
            doc: The Document object
            url: Image URL or user_uploads path
        """
        self.logger.debug(f"[DOCXGenerator] Adding image to DOCX: {url}")
        try:
            from docx.shared import Inches
            
            # Handle user uploads
            if url.startswith("user_uploads/"):
                img_path = get_user_uploads_dir(self.chat_id) / url.split("/", 1)[-1]
                if img_path.is_file():
                    # Add image with reasonable width (6 inches max)
                    doc.add_picture(str(img_path), width=Inches(6))
                    return
            
            # Handle external URLs
            async with httpx.AsyncClient(timeout=10.0) as client:
                resp = await client.get(url, follow_redirects=True)
                resp.raise_for_status()
                
                # Save to BytesIO and add to document
                from io import BytesIO
                img_data = BytesIO(resp.content)
                doc.add_picture(img_data, width=Inches(6))
                self.logger.debug(f"[DOCXGenerator] Successfully added image to DOCX: {url}")
        except Exception as e:
            self.logger.warning(f"[DOCXGenerator] Failed to add image {url}: {e}")
            # Silently skip failed images - don't add error text to document


