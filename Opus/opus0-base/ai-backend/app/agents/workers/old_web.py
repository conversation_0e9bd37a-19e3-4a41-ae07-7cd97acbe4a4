import os
import re
import logging
import json
from datetime import datetime
from typing import Dict, Any, List, Set
import random  # for jitter in exponential backoff

import asyncio
import httpx

from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.prompts import Chat<PERSON>romptTemplate
from langchain_core.output_parsers import StrOutputParser


from app.agents.base_agent import BaseAgent
from app.core.config import settings
from app.utils.constants import KNOWLEDGE_BASE_DIR
from app.utils.communication import send_message

# ADD: Import Crawl4AI classes
from crawl4ai import AsyncWebCrawler, CrawlerRunConfig, BrowserConfig, CacheMode

# Import Rich components for graphical stage separators.
from rich.console import Console
# from rich.tree import Tree

# Initialize module-level logger
logger = logging.getLogger(__name__)

console = Console()

def print_stage(stage: str):
    """Prints a tree-like separator for a stage."""
    # tree = Tree(f"[bold green]{stage}[/]")
    tree = f"[bold green]{stage}[/]"
    console.print(tree)

# -------------------------------------------------------------------------
# GPT chain for generating multiple sub-queries using subtask_desc, deps_info, and user_query.
# -------------------------------------------------------------------------
multi_query_model = ChatGoogleGenerativeAI(
    api_key=settings.GOOGLE_API_KEY, 
    model="gemini-2.0-flash",
    temperature=0.0,
    model_kwargs={
      "stop": ["]"]  # whenever you see a close-bracket, end the generation
    }
)
multi_query_prompt = ChatPromptTemplate.from_template("""

**Role:** You are an expert query generator for Google Search using best practices.

**Instructions:**
1. Given a **Task Description** and **Additional Information**, generate **1 to max 4** concise search queries covering varied angles.
2. Choose the number of queries based on task complexity. 1-2 queries for simple tasks and 3-4 for tasks that require multiple dynamics.
3. **Output only** a comma-separated array of strings, with **no** additional text.

**Output Format Example:**

[
  "query one",
  "query two"
]


**Examples:**

* **Example 1**

  * Additional Information: n/a
  * Task Description: Find information about Indian politics
  * **Output:**


    [
      "overview of Indian politics",
      "major political parties in India",
      "history of Indian politics timeline",
      "recent developments in Indian government policy"
    ]


* **Example 2**

  * Additional Information: Zerodha, zepto, lenskart
  * Task Description: Gather information about identified startup unicorns including their founding year, industry and valuation.
  * **Output:**


    [
      "information about Zerodha including their founding year, industry and valuation",
      "information about Zepto including their founding year, industry and valuation",
      "information about Lenskart including their founding year, industry and valuation"
    ]


---

**IMPORTANT:**

* Do **not** repeat the given information like the Task Description or Additional Information in your output.
* Provide **only** the array.

---

## Additional Information:

{deps_info}

## Task Description:

{subtask_desc}

**OUTPUT:**

""")
multi_query_chain = multi_query_prompt | multi_query_model | StrOutputParser()


select_links_model = ChatGoogleGenerativeAI(
    api_key=settings.GOOGLE_API_KEY, 
    model="gemini-2.0-flash",
    temperature=0.0,
    model_kwargs={
      "stop": ["]"]
    }
)
select_links_prompt = ChatPromptTemplate.from_template("""
You are an expert at evaluating web links for their potential to provide comprehensive and detailed information about a given topic.

From a given task description and some URL links, select any URLs that are likely to provide the most useful and detailed information related to the task.
You can choose just a couple if it's a simple question, or more if it's a deeper topic.
For eg, you might just need 1-2 links to find the capital of france. For a deeper research to find more information, you need more like 2-3. Try to be resourceful while not missing any detail.
Return your answer as a JSON array of URLs. 
Output format example:

[
  "https://example.com/page1",
  "https://example.com/page2"
]

**IMPORTANT!:** No extra text should be included.

Task Description: "{subtask_desc}"

You are provided with a JSON array of URLs:
{links_json}

OUTPUT:
""")
select_links_chain = select_links_prompt | select_links_model | StrOutputParser()

#  NEW: Summarizer chain for extracting key points from crawled content
summarizer_model = ChatGoogleGenerativeAI(
    api_key=settings.GOOGLE_API_KEY, 
    model="gemini-2.0-flash",
)
summarizer_prompt = ChatPromptTemplate.from_template("""
**Role:** You are an information‐extraction assistant for a large multi agent AI system. You only work for you specific given subtask.

Given the task description, and the following crawled content, 
extract all the important details, key points and any useful links that directly address the subtask.
Always provide the links that you got information from with your output.
Do add perspectives for different sources to have a comprehensive understanding of the subject matter. Try not to give all the information from only one source.
Keep your information extraction very detailed and comprehensive and structured with lists and headings to display the information. Give all pootentially useful information for further use.


\n

**Task Description:** {subtask_desc}

\n

**Content to Summarize:** {content}

\n

OUTPUT:

""")
summarizer_chain = summarizer_prompt | summarizer_model | StrOutputParser()

async def summarize_crawled_content(
    user_query: str,
    subtask_desc: str,
    content: str
) -> str:
    """
    Pure function: runs the summarizer_chain via .astream on the given content.
    Returns a Markdown bullet list (string).
    """
    inputs = {
        "user_query": user_query,
        "subtask_desc": subtask_desc,
        "content": content
    }
    chunks: List[str] = []
    async for chunk in summarizer_chain.astream(inputs):
        chunks.append(chunk)
    return "".join(chunks).strip()


class WebScraperAgent(BaseAgent):
    """
    WebScraperAgent performs a simplified web scraping process:
      1) Generates 2 varied sub-queries from the subtask description, deps_info, and user_query via GPT.
      2) For each sub-query, fetches up to 5 links from Brave Search, skipping image/video links.
      3) Merges all unique links into a deduplicated set.
      4) Uses GPT to select any links from these that are most likely to provide detailed information.
      5) Saves the selected links to a markdown (.md) file.
      6) Reports the output file path to the manager.
    """

    def __init__(self, agent_id: str):
        """
        Initializes the WebScraperAgent with a unique agent ID and verifies the Brave Search API key.

        Args:
            agent_id (str): Unique identifier for the WebScraperAgent.
        
        Raises:
            ValueError: If the BRAVE_SEARCH_API key is not provided in the configuration.
        """
        super().__init__(agent_id, agent_type="Search Tool")
        self.brave_api_key = settings.BRAVE_SEARCH_API
        if not self.brave_api_key:
            self.logger.error("BRAVE_SEARCH_API key not found in configuration.")
            raise ValueError("BRAVE_SEARCH_API key is required for WebScraperAgent.")
        self.search_endpoint = "https://api.search.brave.com/res/v1/web/search"
        self.headers = {
            "Accept": "application/json",
            "Accept-Encoding": "gzip",
            "X-Subscription-Token": self.brave_api_key
        }
        self.logger.debug("Initialized WebScraperAgent with Brave Search API key.")      
        # ─── Persist a single crawler/browser for the life of this agent ───
        self._crawler = AsyncWebCrawler(config=BrowserConfig(headless=True))
        self._crawler_entered = False
        # start browser in background so execute_task never blocks on __aenter__
        asyncio.create_task(self._start_crawler())

        # reuse one httpx client across all searches
        self._http_client = httpx.AsyncClient(timeout=30)
        self.logger.debug("WebScraperAgent: persistent crawler & HTTP client initialized")
        
    async def _start_crawler(self):
        try:
            await self._crawler.__aenter__()
            self._crawler_entered = True
            self.logger.debug("WebScraperAgent: crawler.started")
        except Exception as e:
            self.logger.error(f"WebScraperAgent: failed to start crawler: {e}")

    async def execute_task(self, task_details: Dict[str, Any]):
        """
        Executes the simplified web scraping task.

        Process:
          1) Generate 2 sub-queries based on subtask_desc, deps_info, and any user_query.
          2) For each sub-query, fetch up to 5 links from Brave Search, filtering out image/video URLs.
          3) Merge all unique links into a set.
          4) Use GPT to select any links that are most likely to give detailed information about the subtask.
          5) Save the selected links to a markdown file.
          6) Report the output file path to the manager.

        Args:
            task_details (Dict[str, Any]): Contains keys such as:
                - "subtask_id": Unique subtask identifier.
                - "subtask_description": Description or context for the subtask.
                - "user_message": The user's original query (optional).
                - "deps_info": Additional dependency info from previous subtasks (optional).
        """
        try:
            subtask_id = task_details.get("subtask_id", "no_subtask")
            subtask_desc = task_details.get("subtask_description", "no_desc")
            # If user_message is not provided, we'll keep it "n/a" or similarly
            user_query = task_details.get("user_message", "n/a")
            # Additional dep info (e.g. partial solutions or background context)
            deps_info = task_details.get("deps_info", "n/a")

            print_stage("WebScraperAgent: Start")
            self.logger.info(f"Subtask {subtask_id} started.")
            self.logger.info(f"Desc: {subtask_desc}")
            self.logger.info(f"User Query: {user_query}")
            self.logger.info(f"Deps Info: {deps_info}")

            print_stage("Generate Sub-Queries")
            sub_queries = await self.generate_multiple_queries(subtask_desc, deps_info, user_query)
            self.logger.info(f"[MultiQuery] sub-queries generated: {sub_queries}")

            print_stage("Fetch Links")
            link_set: Set[str] = set()
            tasks = [self.fetch_subquery_links(q, max_links=5) for q in sub_queries]
            all_sub_results = await asyncio.gather(*tasks)
            for idx, links in enumerate(all_sub_results):
                self.logger.debug(f"[Sub-query #{idx+1}] {len(links)} links found.")
                for link in links:
                    link_set.add(link)
            self.logger.info(f"[LinkSet] Unique links before selection: {len(link_set)}")

            print_stage("Select Relevant Links")
            selected_links = await self.select_top_links(list(link_set), subtask_desc)
            self.logger.info(f"[SelectedLinks] Relevant links: {selected_links}")
            self.runtime_log(f"Selected links: {selected_links}")

            # # -----------------------------------------------------------------
            # # Existing step: Save the selected links to MD (per original logic)
            # # -----------------------------------------------------------------
            # print_stage("Save Selected Links to Markdown")
            # md_file_path = self.save_links_to_md(subtask_id, subtask_desc, set(selected_links))
            # self.logger.info(f"[SaveToMD] File: {md_file_path}")

            # -----------------------------------------------------------------
            # NEW STEP: Crawl the selected links (in batch) and save the content
            # -----------------------------------------------------------------
            print_stage("Crawl and Save Page Content")
            # self.runtime_log(f"Beginning crawl of {len(selected_links)} pages...")
            self.logger.info(f"Starting parallel crawl for {len(selected_links)} links.")
            if selected_links:
                crawled_md_path = await self.crawl_and_save_content(subtask_id, subtask_desc, user_query, selected_links)
                self.logger.info(f"[CrawledContent] File: {crawled_md_path}")

                # Report the final (crawled) content file to the manager
                print_stage("Report Crawled Results")
                self.report_results({"subtask_id": subtask_id, "output_file": crawled_md_path})
            else:
                self.logger.info("No links to crawl. Using the selected-links .md as final output.")
                print_stage("Report Results (No crawl)")
                # self.report_results({"subtask_id": subtask_id, "output_file": md_file_path})

            self.logger.info(f"Subtask {subtask_id} completed.")

        except Exception as ex:
            self.logger.error(f"[WebScraper Error] {str(ex)}")
            self.handle_error(ex)

    async def generate_multiple_queries(
        self,
        subtask_desc: str,
        deps_info: str,
        user_query: str,
        *,
        max_queries: int = 4,
    ) -> List[str]:
        """
        Ask the LLM for 1‑4 search queries and return them as a Python list.
        Very light processing: only strip ``` fences, then json.loads.
        Falls back to a single concatenated query if parsing fails.
        """
        prompt_input = {
            "subtask_desc": subtask_desc,
            "deps_info": deps_info,
            "user_query": user_query,
        }

        try:
            raw = await multi_query_chain.ainvoke(prompt_input)
            self.logger.debug(f"[MultiQuery‑raw] {raw}")

            # strip leading/trailing ``` or ```json fences (if present)
            clean = re.sub(r"^\s*```(?:json)?\s*", "", raw)
            clean = re.sub(r"\s*```\s*$", "", clean).strip()

            queries = json.loads(clean)

            if not isinstance(queries, list):
                raise ValueError("Result is not a list")

            # keep 1‑max_queries items
            return queries[: max_queries] if queries else [
                f"{subtask_desc} {deps_info} {user_query}"
            ]

        except Exception as e:
            self.logger.error(
                f"[generate_multiple_queries] Parse failure: {e}. "
                "Using single fallback query."
            )
            return [f"{subtask_desc} {deps_info} {user_query}"]

    async def fetch_subquery_links(self, query: str, max_links: int) -> List[str]:
        """
        For a single sub-query, fetch up to max_links from Brave Search, skipping image/video links.

        Args:
            query (str): The sub-query string.
            max_links (int): Maximum number of links to retrieve.

        Returns:
            List[str]: A list of link strings.
        """
        self.runtime_log(f"Searching web for “{query}”")
        data = await self.perform_web_search(query, count=max_links)
        results_list = data.get("web", {}).get("results", [])
        links: List[str] = []
        for item in results_list[:max_links]:
            url = item.get("url", "")
            if not url:
                continue
            if self.is_image_or_video_link(url, item):
                continue
            links.append(url)
        return links

    async def perform_web_search(self, query: str, count: int = 5, offset: int = 0) -> dict:
        """
        Performs a Brave Search for a sub-query and returns the full JSON response.

        Args:
            query (str): The sub-query.
            count (int): Number of results to retrieve.
            offset (int): Offset for pagination.

        Returns:
            dict: JSON response from Brave Search.
        """
        params = {
            "q": query,
            "count": count,
            "offset": offset,
            "safesearch": "off",
            "result_filter": "web"
        }
        self.logger.debug(f"[perform_web_search] Params: {params}")
        
        max_retries = 3
        base_delay = 1  # seconds
        
        for attempt in range(max_retries):
            try:
                r = await self._http_client.get(self.search_endpoint, headers=self.headers, params=params)
                self.logger.debug(f"[perform_web_search] Status: {r.status_code} for query '{query}'")
                r.raise_for_status()
                return r.json()

            except httpx.HTTPStatusError as e:
                status = e.response.status_code
                # If rate limited, retry with exponential backoff + jitter
                if status == 429 and attempt < max_retries - 1:
                    delay = base_delay * (2 ** attempt) + random.uniform(0, 1)
                    self.logger.warning(
                        f"[perform_web_search] 429 Too Many Requests on attempt {attempt+1}/{max_retries}. "
                        f"Retrying in {delay:.2f}s..."
                    )
                    await asyncio.sleep(delay)
                    continue
                self.logger.error(f"[perform_web_search Error] HTTP {status} for query '{query}': {e}")
                break

            except Exception as e:
                self.logger.error(f"[perform_web_search Error] {str(e)} for query '{query}'")
                break

        # All retries failed
        return {}

    def is_image_or_video_link(self, url: str, item: dict) -> bool:
        """
        Determines if a URL corresponds to an image or video, which should be skipped.

        Args:
            url (str): The URL to check.
            item (dict): Additional search result details.

        Returns:
            bool: True if the URL is an image or video; False otherwise.
        """
        ext = os.path.splitext(url.lower())[1]
        image_exts = {".jpg", ".jpeg", ".png", ".gif", ".webp"}
        video_exts = {".mp4", ".mov", ".avi", ".mkv", ".webm"}
        return ext in image_exts or ext in video_exts

    async def select_top_links(self, links: List[str], subtask_desc: str) -> List[str]:
        """
        Uses GPT to evaluate a list of URLs and select any that are most likely to provide
        detailed and relevant information based on the subtask description.

        Args:
            links (List[str]): A list of unique URLs.
            subtask_desc (str): The subtask description to provide context.

        Returns:
            List[str]: A list of selected URLs.
        """
        self.logger.info(
            f"Selecting top links from {len(links)} links based on subtask: {subtask_desc}"
        )
        links_json = json.dumps(links, indent=2)
        prompt_input = {
            "subtask_desc": subtask_desc,
            "links_json": links_json
        }

        # 1) collect the raw response
        select_response_chunks: List[str] = []
        async for chunk in select_links_chain.astream(prompt_input):
            select_response_chunks.append(chunk)
        raw_selection = "".join(select_response_chunks).strip()
        self.logger.info(f"Raw top links selection response: {raw_selection}")

        # 2) strip any Markdown fences (``` or ```json) before JSON parse
        let = raw_selection
        # remove leading ```json or ```
        let = re.sub(r"^```(?:json)?\s*", "", let)
        # remove trailing ```
        let = re.sub(r"\s*```$", "", let)
        let = let.strip()

        # 3) attempt to parse
        try:
            selected_links = json.loads(let)
            if not isinstance(selected_links, list):
                raise ValueError("parsed value is not a list")
            return selected_links
        except Exception as e:
            self.logger.error(
                f"Error parsing top links selection: {e}. Using all links as fallback."
            )
            return links

    def save_links_to_md(self, subtask_id: str, desc: str, link_set: Set[str]) -> str:
        """
        Saves the unique links to a markdown (.md) file.

        Args:
            subtask_id (str): Unique identifier for the subtask.
            desc (str): Description or context for the subtask.
            link_set (Set[str]): Set of unique URLs.

        Returns:
            str: File path to the saved markdown file.
        """
        timestamp = datetime.utcnow().strftime('%Y%m%d%H%M%S')
        file_name = f"web_scraper_{subtask_id}_{timestamp}.md"
        file_path = os.path.join(KNOWLEDGE_BASE_DIR, file_name)
        os.makedirs(KNOWLEDGE_BASE_DIR, exist_ok=True)
        try:
            with open(file_path, "w", encoding="utf-8") as md_file:
                md_file.write("# Web Scraper Results\n\n")
                md_file.write(f"**Subtask ID:** {subtask_id}\n\n")
                md_file.write(f"**Description:** {desc}\n\n")
                md_file.write(f"**Date:** {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                md_file.write("## Selected Links\n\n")
                for link in sorted(link_set):
                    md_file.write(f"- {link}\n")
            self.logger.debug(f"[save_links_to_md] {len(link_set)} links saved => {file_path}")
            return file_path
        except Exception as ex:
            self.logger.error(f"[save_links_to_md Error] {str(ex)}")
            return "file_write_error"

    async def crawl_and_save_content(
        self,
        subtask_id: str,
        desc: str,
        user_query: str,
        links: List[str]
    ) -> str:
        """
        Crawls each of the selected links using Crawl4AI in batch mode,
        summarizes the combined content, and saves *only* the summary as a .md file.
        """

        self.logger.info(f"[crawl_and_save_content] Crawling {len(links)} links…")
        run_config = CrawlerRunConfig(
            cache_mode=CacheMode.BYPASS,
            page_timeout=15_000,       # 15 s instead of 60 s
        )

        # 1) Crawl and aggregate raw markdown
        combined_parts: List[str] = []
        
    
        # now crawl (crawler is already started in background)
        results = await self._crawler.arun_many(links, config=run_config)
        self.logger.info(f"[crawl_and_save_content] Finished crawling {len(results)} links.")
        for r in results:
            if r.success:
                combined_parts.append(r.markdown.raw_markdown)
            else:
                # skip pages that failed (e.g. timeouts, blockers)
                self.logger.warning(
                    f"[crawl_and_save_content] skipping {r.url} due to error: {r.error_message}"
                )


        raw_combined = "\n\n".join(combined_parts)

        # 2) Summarize the entire crawl
        # self.runtime_log("Processing crawled content...")
        summary = await summarize_crawled_content(
            user_query=user_query,
            subtask_desc=desc,
            content=raw_combined
        )

        # 3) Save *only* the summary
        timestamp = datetime.utcnow().strftime("%Y%m%d%H%M%S")
        fname = f"web_scraper_summary_{subtask_id}_{timestamp}.md"
        out_path = os.path.join(KNOWLEDGE_BASE_DIR, fname)
        os.makedirs(KNOWLEDGE_BASE_DIR, exist_ok=True)
        # self.runtime_log("Processing complete")

        try:
            with open(out_path, "w", encoding="utf-8") as f:
                f.write("# Summary of Key Points\n\n")
                f.write(summary)
            self.logger.info(f"[crawl_and_save_content] Summary saved => {out_path}")
            return out_path
        except Exception as e:
            self.logger.error(f"[crawl_and_save_content] Write error: {e}")
            return "file_write_error"

    def report_results(self, results: Dict[str, Any]):
        """
        Sends a message to the manager with the subtask ID and output file path.

        Args:
            results (Dict[str, Any]): Should contain:
                - "subtask_id": Unique subtask identifier.
                - "output_file": Path to the saved markdown file.
        """
        subtask_id = results.get("subtask_id")
        output_file = results.get("output_file")
        message = {
            "subtask_id": subtask_id,
            "output_file": output_file
        }
        send_message(sender="web_scraper", receiver="manager", message=message)
        self.logger.info(f"[report_results] {subtask_id} -> {output_file}")