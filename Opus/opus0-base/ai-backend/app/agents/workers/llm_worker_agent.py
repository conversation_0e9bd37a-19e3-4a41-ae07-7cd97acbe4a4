# app/agents/workers/llm_worker_agent.py
"""
Module: llm_worker_agent.py

This module defines the LLMWorkerAgent class, a worker agent that processes
subtasks using Gemini (ChatGoogleGenerativeAI). The agent first generates an
initial response and then iteratively refines it using a feedback loop. The
generation is split into two helper methods: ``_generate_base_response`` for the
initial LLM call and ``_feedback_loop`` for applying self-critique prompts. The
final result is stored as a markdown file in the knowledge base and reported
back to the manager."""

import mimetypes
import os
import re
import logging
import json
from datetime import datetime
from typing import List, Dict, Any, TYPE_CHECKING, Tuple
import base64


from langchain_core.messages import SystemMessage, HumanMessage
from langchain_google_genai import Chat<PERSON>oogleGenerativeAI
from langchain_core.output_parsers import StrOutputParser

from app.utils.constants import get_knowledge_base_dir, get_user_uploads_dir
from app.agents.base_agent import BaseAgent
from app.core.config import settings
from app.utils.communication import send_message
from app.utils.token_counter import count_tokens

if TYPE_CHECKING:  # avoid circular import during module load
    from app.agents.workers.reasoning_worker_agent import ReasoningWorkerAgent
    from app.agents.workers.web_scraper_agent import WebScraperAgent

# Initialize module‐level logger
logger = logging.getLogger(__name__)

# Instantiate Gemini (ChatGoogleGenerativeAI) once at module load.
gpt4_model = ChatGoogleGenerativeAI(
    api_key=settings.GOOGLE_API_KEY,
    model="gemini-2.5-flash-preview-04-17",
    temperature=0.4,
)


class LLMWorkerAgent(BaseAgent):
    """
    Worker agent capable of processing subtasks using Gemini (ChatGoogleGenerativeAI).

    Inherits from BaseAgent and implements task‐specific logic to:
      1. Generate a response for a given subtask (with or without images).
      2. Save the generated response to a markdown file in the knowledge base.
      3. Report task completion back to the manager.
    """

    def __init__(self, agent_id: str, chat_id: str):
        """
        Initializes the LLMWorkerAgent with a unique agent ID.

        Args:
            agent_id (str): Unique identifier for the LLM worker agent.
        """
        super().__init__(agent_id, agent_type="Thinking Tool")
        self.chat_id = chat_id

    async def execute_task(self, task_details: Dict[str, Any]):
        """
        Executes the given subtask by generating a response using Gemini and saving the result.

        Steps:
          1. Extract subtask details from the provided task_details dict.
          2. Generate a response using `generate_response(...)`.
          3. Save the response as a markdown (.md) file in the knowledge base.
          4. Report the task completion (subtask ID and output file) to the manager.

        Args:
            task_details (Dict[str, Any]): Dictionary containing task details, including:
                - "subtask_id": Unique subtask identifier.
                - "subtask_description": Description of the subtask.
                - "user_message": The main user input.
                - "deps_info": (Optional) Additional dependency information.
                - "uploaded_images": list of filenames (strings).
                - "history": a single string with “role: content” lines.
        """
        try:
            self.start_timer("llm agent")
            self.runtime_log("")

            subtask_id = task_details["subtask_id"]
            subtask_description = task_details["subtask_description"]
            user_input = task_details["user_message"]
            deps_info = task_details.get("deps_info", "n/a")
            uploaded_images = self.normalize_uploads(
                task_details.get("uploaded_images", [])
            )
            history_str = task_details.get("history", "")

            # Log full payload (for debugging)
            logger.info(f"Task details received: {json.dumps(task_details, indent=2)}")
            logger.info(f"Process subtask {subtask_id}")
            logger.info(f"  Desc: {subtask_description}")

            # Build the prompt‐messages and count tokens
            messages = self._build_messages(
                history=history_str,
                user_input=user_input,
                subtask_description=subtask_description,
                deps_info=deps_info,
                uploaded_images=uploaded_images,
            )

            # Count input tokens (approximate) by concatenating all text fields
            # Count input tokens (approximate) by concatenating only string‐typed contents:
            concatenated = "\n".join(
                m.content if isinstance(m.content, str) else "" for m in messages
            )
            in_tokens = count_tokens(concatenated)
            self.log_input_tokens(gpt4_model.model, in_tokens)

            # Step 1: Generate the initial response
            base_output = await self._generate_base_response(messages)

            # Step 2: Run the feedback loop for refinements
            response, refinements = await self._feedback_loop(
                base_messages=messages,
                subtask_description=subtask_description,
                initial_response=base_output,
            )

            # Step 3: Save to .md
            output_file_path = self.save_response_to_md_file(
                subtask_id,
                subtask_description,
                base_output,
                refinements,
            )

            # Step 4: Report back to manager
            self.report_results(
                {"subtask_id": subtask_id, "output_file": output_file_path}
            )

            self.stop_timer("llm agent")

        except Exception as e:
            logger.error(f"Task exec error: {e}")
            self.handle_error(e)

    def _build_messages(
        self,
        history: str,
        user_input: str,
        subtask_description: str,
        deps_info: str,
        uploaded_images: List[str],
    ) -> List[Any]:
        """
        Construct the list of BaseMessage objects (SystemMessage + HumanMessage) for Gemini.

        - Always insert a single SystemMessage containing history + “Main Task” + “Subtask” + “Additional info”.
        - If there are valid images (non-.md), then append one HumanMessage with a list of parts
          (text + base64 image_url dicts). Otherwise, append one HumanMessage with a plain text prompt.
        """
        # 1) SYSTEM MESSAGE text (runtime‐interpolated)
        system_content = "\n".join(
            [
                "You are an intelligent assistant completing a single subtask.",
                "",
                "Chat History:",
                history or "(no prior chat)",
                "",
                f"Main Task: {user_input}",
                "",
                "Please answer below:",
            ]
        ).strip()

        system_msg = SystemMessage(content=system_content)

        # 2) HUMAN MESSAGE: either embedded images or a plain‐text instruction
        # Filter out any “.md” entries:
        image_files = [fn for fn in uploaded_images if not fn.lower().endswith(".md")]

        if image_files:
            # Build a list of parts: first a text part, then one image_url per file
            parts: List[dict] = [
                {"type": "text", "text": "Here are the images to reference:"}
            ]
            UP = get_user_uploads_dir(self.chat_id)

            for fn in image_files:
                p = UP / fn
                if not p.is_file():
                    self.logger.warning(f"[llm_worker] upload missing: {p}")
                    continue
                raw = p.read_bytes()
                b64 = base64.b64encode(raw).decode("utf-8")

                # Guess MIME type
                mime, _ = mimetypes.guess_type(p.name)
                if not mime:
                    ext = p.suffix.lstrip(".").lower()
                    if ext == "jpg":
                        ext = "jpeg"
                    mime = f"image/{ext}"

                parts.append(
                    {
                        "type": "image_url",
                        "image_url": {"url": f"data:{mime};base64,{b64}"},
                    }
                )

            human_msg = HumanMessage(content=parts)
        else:
            # No valid images → plain text
            human_text = "\n".join(
                [
                    f"Subtask Description: {subtask_description}",
                    f"Additional info: {deps_info}",
                    "",
                    "Response:",
                ]
            )
            human_msg = HumanMessage(content=human_text)

        return [system_msg, human_msg]

    async def _invoke_stream(self, messages: List[Any], *, stream: bool = True) -> str:
        """Send ``messages`` to Gemini and return the accumulated response.

        Args:
            messages: The message list to pass to ``astream``.
            stream: When ``True`` runtime log events are emitted for each chunk.

        Returns:
            The concatenated response string from Gemini.
        """

        logger.info(
            "[llm_worker] LLM input messages:\n%s",
            "\n".join(
                f"{type(m).__name__}: {getattr(m, 'content', m)}" for m in messages
            ),
        )

        chunks: List[str] = []
        if stream:
            # mark stream start
            self.runtime_log_stream("stream")

        async for chunk in gpt4_model.astream(messages):
            # chunk is an AIMessage (or AIMessageChunk); we want its `.content`
            text = chunk.content if hasattr(chunk, "content") else str(chunk)
            if stream:
                self.runtime_log_stream(text)
            chunks.append(text)

        if stream:
            # mark stream end
            self.runtime_log_stream("stream")

        return "".join(chunks).strip()

    async def _generate_base_response(self, base_messages: List[Any]) -> str:
        """Generate the initial response using ``base_messages``."""

        response = await self._invoke_stream(base_messages, stream=True)
        self.runtime_log_stream("\n\n")
        logger.info("[llm_worker] initial output: %s", response[:200])
        out_tokens = count_tokens(response)
        self.log_output_tokens(gpt4_model.model, out_tokens)
        return response

    async def _feedback_loop(
        self,
        *,
        base_messages: List[Any],
        subtask_description: str,
        initial_response: str,
        max_rounds: int = 5,
    ) -> Tuple[str, List[Dict[str, str]]]:
        """Iteratively refine ``initial_response`` up to ``max_rounds``.

        Returns the final response and a list of refinement dictionaries with
        keys ``agent``, ``instruction`` and ``output``.
        """

        response = initial_response
        refinements: List[Dict[str, str]] = []

        for round_no in range(1, max_rounds + 1):
            review_msgs = [
                SystemMessage(
                    "You are the feedback reviewer for the LLM agent. "
                    "Return a JSON array of two strings: the agent to run next "
                    "('llm', 'reasoning', or 'web') and a short instruction "
                    "describing the improvement. Use 'llm' for minor rewriting "
                    "or clarifications. Choose 'reasoning' for step-by-step "
                    "logic or verification. Select 'web' when fresh online "
                    "information is required. If the current output is fine, "
                    "reply with ['llm', 'none']. Respond only with the JSON array."
                    "IMPORTANT: Doing a refinement or fix is costly. Only do refinements"
                    "when necessary like in case of incomplete information/not meeting the task"
                ),
                HumanMessage(
                    content=(
                        f"Past history and information provided for context: {base_messages}\n\n---\n\n"
                        f"Again, this is the main subtask we need to focus on: {subtask_description}\n\n---\n\n"
                        f"Current Output (output given by the current agent for the subtask):\n{response}\n"
                        "Provide the JSON array:"
                    )
                ),
            ]
            logger.info("[llm_worker] review round %d", round_no)
            review = await self._invoke_stream(review_msgs, stream=False)
            suggestions = review.strip()
            logger.info("[llm_worker] review response: %s", suggestions)

            clean = re.sub(r"^```(?:json)?\s*", "", suggestions)
            clean = re.sub(r"\s*```$", "", clean).strip()
            try:
                agent_choice, improvement = json.loads(clean)
            except Exception:
                logger.warning("[llm_worker] invalid feedback format: %s", suggestions)
                break
            if not isinstance(agent_choice, str) or not isinstance(improvement, str):
                logger.warning("[llm_worker] malformed feedback: %s", suggestions)
                break

            self.runtime_log_stream(f"\n\n---Ref:{improvement}---\n\n")
            if improvement.lower() in {"none", "none.", "'none'", '"none"'}:
                break

            if agent_choice.lower().startswith("reason"):
                # Local import avoids circular dependency
                from app.agents.workers.reasoning_worker_agent import (
                    ReasoningWorkerAgent,
                )  # noqa: WPS433

                target = ReasoningWorkerAgent(
                    f"{self.agent_id}_reason_fb", self.chat_id
                )
            elif agent_choice.lower().startswith("web"):
                from app.agents.workers.web_scraper_agent import (
                    WebScraperAgent,
                )  # noqa: WPS433

                target = WebScraperAgent(f"{self.agent_id}_web_fb", self.chat_id)
            else:
                target = LLMWorkerAgent(f"{self.agent_id}_llm_fb", self.chat_id)
            target.current_task = self.current_task

            response = await self.apply_feedback(
                target_agent=target,
                feedback=improvement,
                previous_output=response,
                task_details=self.current_task or {},
                past_refinements=refinements,
            )
            refinements.append(
                {
                    "agent": agent_choice,
                    "instruction": improvement,
                    "output": response,
                }
            )
            logger.info(
                "[llm_worker] round %d refined output: %s",
                round_no,
                response[:200],
            )
            out_tokens = count_tokens(response)
            self.log_output_tokens(gpt4_model.model, out_tokens)

        return response, refinements

    def save_response_to_md_file(
        self,
        subtask_id: str,
        subtask_description: str,
        initial_output: str,
        refinements: List[Dict[str, str]],
    ) -> str:
        """
        Saves the generated response to a markdown (.md) file in the knowledge base.

        The file name is generated using the subtask ID and the current UTC timestamp.

        Returns:
            str: The file path of the saved md file.
        """
        timestamp = datetime.utcnow().strftime("%Y%m%d%H%M%S")
        output_file_name = f"{subtask_id}_{timestamp}.md"
        kb_dir = get_knowledge_base_dir(self.chat_id)
        output_file_path = os.path.join(kb_dir, output_file_name)
        os.makedirs(kb_dir, exist_ok=True)

        with open(output_file_path, "w", encoding="utf-8") as file:
            file.write("---\n\n")
            file.write(f"SUBTASK {subtask_id} STARTS\n\n")
            file.write("---\n")
            file.write(f"# Subtask {subtask_id}: {subtask_description}\n")
            file.write("## Output:\n")
            file.write(f"{initial_output}\n")
            for ref in refinements:
                file.write("## Refinement:\n")
                file.write(
                    f"### {ref.get('agent', '')} - {ref.get('instruction', '')}\n"
                )
                file.write(f"{ref.get('output', '')}\n")
            file.write("---\n\n")
            file.write(f"SUBTASK {subtask_id} COMPLETED\n\n")
            file.write("---")

        logger.info(f"[llm_worker] Saved: {output_file_path}")
        return output_file_path

    def report_results(self, results: Dict[str, Any]):
        """
        Reports the results of the subtask execution to the manager.

        Sends a message to the manager containing the subtask ID and the output file path.
        """
        subtask_id = results["subtask_id"]
        output_file = results["output_file"]
        send_message(
            self.chat_id,
            sender="worker",
            receiver="manager",
            message={"subtask_id": subtask_id, "output_file": output_file},
        )
        self.logger.info(f"[llm_worker] Reported {subtask_id}: {output_file}")
