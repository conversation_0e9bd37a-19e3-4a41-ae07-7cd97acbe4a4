# app/agents/workers/link_analysis_agent.py

"""
LinkAnalysisAgent

Enhancements
------------
* Extracts the single relevant URL via an LLM (fallback to regex).
* For YouTube URLs: fetch title, description & transcript.
* Other pages: pull readable text via <PERSON><PERSON> reader (with 429 back-off).
* Save everything to a markdown file and report to the manager.
* **NEW:** Includes the raw user message in the link-extraction prompt,
  and emits full debug logs of inputs, prompt, and outputs.
"""

import os
import re
import json
import logging
from datetime import datetime
from typing import Dict, Any, Optional, Tuple, List

import asyncio
import httpx
from yt_dlp import YoutubeDL
from youtube_transcript_api import (
    YouTubeTranscriptApi,
    TranscriptsDisabled,
    NoTranscriptFound,
)

from langchain_google_genai import ChatGoogleGenerative<PERSON>I
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser
from app.utils.token_counter import count_tokens

from app.agents.base_agent import BaseAgent
from app.core.config import settings
from app.utils.constants import get_knowledge_base_dir
from app.utils.communication import send_message

logger = logging.getLogger(__name__)

# ─── LLM-based link extraction chain ─────────────────────────────────────

link_extract_model = ChatGoogleGenerativeAI(
    api_key=settings.GOOGLE_API_KEY,
    model="gemini-2.5-pro-preview-03-25",
)
link_extract_prompt = ChatPromptTemplate.from_template("""
# **Role:**
You are given a subtask description which is your main task, some additional info if any, and the main user message the team you are part is working on.
Return ONLY the single most relevant URL, as a JSON array containing exactly one string, with no extra text.
IMPORTANT!: Subtask descriptions always have corrupted shortened links, only use correct version of the links from the Additional Information!

---

# **INPUTS:**
**Subtask:** {subtask_desc}  
**Additional info:** {deps_info}  
**User message:** {user_msg}

---

# **OUTPUT FORMAT:**
[ "https://example.com/ActualCaseSensitiveLink" ]

---

# **OUTPUT EXAMPLE:**
✅ (correct):  [ "https://www.youtube.com/watch?v=Q56PMJbCFXQ" ]
❌ (corrupted):  [ "https://www.youtube.com/watch?v=q56pmjbcfxq" ]

---

# IMPORTANT:
* Do **not** simplify or lowercase any characters.
* Copy the link exactly as it appears in the user message if present.
* If the user message has no link, return an empty array: []

# OUTPUT:
""")
# We still keep the chain around in case you want to use it elsewhere,
# but _get_link will now bypass chain.ainvoke and manage raw calls directly.
link_extract_chain = link_extract_prompt | link_extract_model | StrOutputParser()


class LinkAnalysisAgent(BaseAgent):
    """
    Extract a single link (via LLM + regex fallback) and analyze it:
      - YouTube: fetch title, description & transcript.
      - Other pages: pull readable text via Jina reader (with 429 back-off).
      - Save everything to a markdown file and report to the manager.
    """

    def __init__(self, agent_id: str, chat_id: str):
        super().__init__(agent_id, agent_type="link_analysis")
        self.chat_id = chat_id
        self.logger.debug("Initialized LinkAnalysisAgent")
        self._last_link_raw: Optional[str] = None


    async def execute_task(self, task_details: Dict[str, Any]):
        # --- FULL INPUT LOGGING ---
        logger.debug(f"[execute_task] received task_details:\n{json.dumps(task_details, indent=2)}")

        try:
            self.start_timer("link agent")
            subtask_id = task_details.get("subtask_id", "no_subtask_id")
            desc       = task_details.get("subtask_description", "")
            deps_info  = task_details.get("deps_info", "")
            user_msg   = task_details.get("user_message", "")

            self.logger.info(f"[{subtask_id}] starting link analysis")

            # ── 1) Extract via LLM (fallback to regex)
            url = await self._get_link(subtask_id, desc, deps_info, user_msg)
            if not url:
                self.logger.info(f"[{subtask_id}] no link found")
                md = self._save_empty_md(subtask_id, desc, user_msg, self._last_link_raw)
                self._report(subtask_id, md)
                return

            self.runtime_log(f"Selected link: {url}")

            # ── 2) Branch on YouTube vs generic
            if self._is_youtube(url):
                self.logger.info(f"[{subtask_id}] detected YouTube link")
                title, description = await self._fetch_yt_metadata(url)
                transcript         = await self._fetch_yt_transcript(url)
                self.runtime_log(f"Video/Playlist title: {title or 'N/A'}")
                md = self._save_youtube_md(
                    subtask_id, desc, url, title, description, transcript
                )
            else:
                self.logger.info(f"[{subtask_id}] fetching generic page text")
                page_text = await self._fetch_text_with_backoff(url)
                md = self._save_generic_md(subtask_id, desc, url, page_text)

            # ── 3) Report results
            self._report(subtask_id, md)
            self.logger.info(f"[{subtask_id}] analysis finished")
            
            self.stop_timer("link agent")

        except Exception as ex:
            self.logger.error(f"[LinkAnalysis] fatal error: {ex}")
            self.handle_error(ex)


    # ── Link extraction helpers ───────────────────────────────────────────

    async def _get_link(
        self,
        subtask_id: str,
        subtask_desc: str,
        deps_info: str,
        user_msg: str
    ) -> Optional[str]:
        """Ask the LLM for a single URL, parse it, or fall back to regex."""

        # 1) Log the raw prompt variables
        self.logger.debug(f"[{subtask_id}] LLM prompt inputs:\n"
                          f"  subtask_desc: {subtask_desc}\n"
                          f"  deps_info:    {deps_info}\n"
                          f"  user_msg:     {user_msg}")

        try:
            # 2) Render the full prompt string
            raw_prompt = link_extract_prompt.format(
                subtask_desc=subtask_desc,
                deps_info=deps_info,
                user_msg=user_msg,
            )
            # **NEW**: log full prompt text at INFO level
            self.logger.info(f"[{subtask_id}] raw LLM link-extract prompt:\n{raw_prompt}")

            # --- count & log input tokens ---
            in_toks = count_tokens(raw_prompt)
            self.log_input_tokens(link_extract_model.model, in_toks)

            # 3) Call the LLM directly, log its raw output at INFO level
            raw_output = await link_extract_model.apredict(text=raw_prompt)
            self.logger.info(f"[{subtask_id}] raw LLM link-extract output:\n{raw_output}")
            self._last_link_raw = raw_output
            
            # --- count & log output tokens ---
            out_toks = count_tokens(raw_output)
            self.log_output_tokens(link_extract_model.model, out_toks)


            # 4) Parse that raw output through your existing StrOutputParser
            parsed = StrOutputParser().parse(raw_output)
            self.logger.debug(f"[{subtask_id}] parsed LLM link-extract output:\n{parsed}")

            # 5) JSON-extract the URL
            url = self._parse_link_json(parsed)
            if url:
                return url

        except Exception as e:
            self.logger.warning(f"[{subtask_id}] LLM link extraction failed: {e}")

        # --- fallback to regex on the user message ---
        self.logger.debug(f"[{subtask_id}] falling back to regex on user_msg")
        self._last_link_raw = None
        return self._extract_link(user_msg)


    @staticmethod
    def _parse_link_json(raw: str) -> Optional[str]:
        """Clean away fences, JSON-parse, return the single URL."""
        clean = re.sub(r"^\s*(?:json)?\s*", "", raw)
        clean = re.sub(r"\s*\s*$", "", clean).strip()
        try:
            arr = json.loads(clean)
            if isinstance(arr, list) and arr and isinstance(arr[0], str):
                return arr[0]
        except Exception:
            pass
        return None


    @staticmethod
    def _extract_link(text: str) -> Optional[str]:
        """Fallback regex for first http(s)://… URL in arbitrary text."""
        m = re.search(r"(https?://[^\s]+)", text)
        return m.group(1) if m else None


    @staticmethod
    def _is_youtube(url: str) -> bool:
        """
        Return True for direct YouTube video URLs:
          - those containing '/watch?v='
          - short links 'youtu.be/<video_id>'
        Everything else (playlists, channels, etc.) returns False.
        """
        u = url.lower()
        # full watch URLs
        if "youtube.com/watch?v=" in u:
            return True
        # short URLs like youtu.be/<video_id>
        # only treat as video if there's a non-empty id segment
        if re.search(r"youtu\.be/[A-Za-z0-9_-]+", u):
            return True
        return False


    # ── YouTube handling ─────────────────────────────────────────────────
    # ── YouTube metadata ─────────────────────────────────────────────────
    async def _fetch_yt_metadata(self, url: str) -> Tuple[str, str]:
        """
        Fetch title and description for a YouTube video via yt_dlp.
        """
        self.logger.info(f"[LinkAnalysisAgent] Fetching YouTube metadata for {url}")
        try:
            def _extract() -> Tuple[str, str]:
                opts = {"skip_download": True, "quiet": True, "no_warnings": True}
                with YoutubeDL(opts) as ydl:
                    info = ydl.extract_info(url, download=False)
                    return info.get("title", ""), info.get("description", "")
            title, desc = await asyncio.to_thread(_extract)
            self.logger.debug(f"[LinkAnalysisAgent] Metadata fetched: title={title!r}")
            return title, desc
        except Exception as e:
            self.logger.error(f"[LinkAnalysisAgent] Error fetching metadata for {url}: {e}", exc_info=True)
            return "", ""


    # ── Combined transcript/captions ──────────────────────────────────────
    async def _fetch_yt_transcript(self, url: str) -> str:
        """
        Concurrently try:
          1) official transcript via YouTubeTranscriptApi
          2) captions via yt_dlp (automatic/subtitles)
        Prefer the transcript; fall back to the yt_dlp captions.
        """
        vid = self._yt_id(url)
        if not vid:
            self.logger.warning(f"[LinkAnalysisAgent] Could not parse YouTube ID from {url}")
            return ""

        self.logger.info(f"[LinkAnalysisAgent] Attempting transcript+caption fetch for video id={vid}")
        transcript_task = asyncio.to_thread(self._get_transcript_api, vid)
        captions_task   = asyncio.to_thread(self._get_captions_via_ytdlp, url)
        transcript_list, captions_text = await asyncio.gather(transcript_task, captions_task)

        if transcript_list:
            # each seg may be a dict OR a FetchedTranscriptSnippet
            parts = []
            for seg in transcript_list:
                if isinstance(seg, dict):
                    parts.append(seg.get("text", ""))
                else:
                    # FetchedTranscriptSnippet has .text
                    parts.append(getattr(seg, "text", ""))
            text = "\n".join(parts)
            self.logger.debug(f"[LinkAnalysisAgent] Transcript length={len(text)} chars")
            return text

        if captions_text:
            self.logger.debug(f"[LinkAnalysisAgent] Captions length={len(captions_text)} chars")
            return captions_text

        self.logger.warning(f"[LinkAnalysisAgent] No transcript or captions available for {url}")
        return ""


    # ── sync helper for YouTubeTranscriptApi ─────────────────────────────
    def _get_transcript_api(self, vid: str) -> Optional[List[Dict[str, Any]]]:
        """
        Try English transcript, then first available language.
        Returns list of segments or None on failure.
        """
        try:
            return YouTubeTranscriptApi.get_transcript(vid, languages=["en"])
        except (TranscriptsDisabled, NoTranscriptFound):
            self.logger.info(f"[LinkAnalysisAgent] No English transcript for {vid}; trying fallback")
        except Exception as e:
            self.logger.warning(f"[LinkAnalysisAgent] Error fetching English transcript: {e}")

        try:
            avail = YouTubeTranscriptApi.list_transcripts(vid)
            transcript = avail.find_transcript([t.language_code for t in avail]).fetch()
            return transcript
        except Exception as e:
            self.logger.error(f"[LinkAnalysisAgent] Fallback transcript fetch failed for {vid}: {e}")
            return None


    # ── sync helper for ytdlp captions ──────────────────────────────────
    def _get_captions_via_ytdlp(self, url: str) -> str:
        """
        Use yt_dlp to fetch automatic/subtitle tracks (preferring English).
        Strips out timestamps and returns just the text.
        """
        try:
            opts = {
                "skip_download": True,
                "quiet": True,
                "no_warnings": True,
                "writesubtitles": True,
                "writeautomaticsub": True,
                "subtitleslangs": ["en"],
            }
            with YoutubeDL(opts) as ydl:
                info = ydl.extract_info(url, download=False)

            # try auto-captions first, then regular subtitles
            tracks = info.get("automatic_captions", {}) or info.get("subtitles", {})
            entries = tracks.get("en") or tracks.get("en-US") or []
            if not entries:
                self.logger.info(f"[LinkAnalysisAgent] No yt_dlp captions for {url}")
                return ""

            cap_url = entries[0].get("url")
            if not cap_url:
                return ""

            r = httpx.get(cap_url, timeout=30.0)
            r.raise_for_status()
            raw = r.text

            # strip SRT/vtt timestamps and indexes
            lines = []
            for line in raw.splitlines():
                if re.match(r"^\d+$", line) or re.match(r"^\d{2}:\d{2}:\d{2}", line):
                    continue
                lines.append(line)
            return "\n".join(lines).strip()

        except Exception as e:
            self.logger.error(f"[LinkAnalysisAgent] Error fetching captions via yt_dlp for {url}: {e}", exc_info=True)
            return ""



    @staticmethod
    def _yt_id(url: str) -> Optional[str]:
        for pat in (r"youtube\.com/watch\?v=([^&]+)", r"youtu\.be/([^?&/]+)"):
            m = re.search(pat, url)
            if m:
                return m.group(1)
        return None


    # ── Generic page text (Jina) ────────────────────────────────────────
    async def _fetch_text_with_backoff(self, url: str) -> str:
        endpoint     = f"https://r.jina.ai/{url}"
        max_attempts = 5
        for attempt in range(1, max_attempts + 1):
            try:
                async with httpx.AsyncClient(timeout=60) as client:
                    r = await client.get(endpoint)
                    if r.status_code == 429:
                        wait = int(r.headers.get("Retry-After", 2**(attempt-1)))
                        self.logger.warning(f"429 attempt {attempt}; sleeping {wait}s")
                        await asyncio.sleep(wait)
                        continue
                    r.raise_for_status()
                    return r.text
            except httpx.HTTPError as e:
                if attempt == max_attempts:
                    return f"Error after {attempt} attempts: {e}"
                await asyncio.sleep(2**(attempt-1))
        return "[jina] unexpected exit"


    # ── Markdown writers ─────────────────────────────────────────────────
    def _save_youtube_md(
        self,
        sub_id: str,
        desc: str,
        url: str,
        title: str,
        video_desc: str,
        transcript: str,
    ) -> str:
        ts   = datetime.utcnow().strftime("%Y%m%d%H%M%S")
        fname = f"link_analysis_{sub_id}_{ts}.md"
        kb_dir = get_knowledge_base_dir(self.chat_id)
        path = os.path.join(kb_dir, fname)
        os.makedirs(kb_dir, exist_ok=True)

        with open(path, "w", encoding="utf-8") as f:
            f.write("# YouTube Link Analysis\n\n")
            f.write(f"**Subtask ID:** {sub_id}\n\n")
            f.write(f"**Description:** {desc}\n\n")
            f.write(f"**Date:** {datetime.utcnow():%Y-%m-%d %H:%M:%S}\n\n")

            f.write("## Video Metadata\n\n")
            f.write(f"- **URL:** {url}\n")
            f.write(f"- **Title:** {title or 'N/A'}\n\n")

            f.write("### Description\n\n")
            f.write((video_desc or "_No description available_") + "\n\n")

            f.write("### Full Transcript\n\n")
            f.write((transcript or "_No transcript available_") + "\n\n")

            f.write("## LLM Link Extraction Output\n\n")
            if self._last_link_raw is not None:
                f.write("\n" + self._last_link_raw.strip() + "\n \n")
            else:
                f.write("_(no LLM output; fallback regex used)_\n")

        return path


    def _save_generic_md(self, sub_id: str, desc: str, url: str, body: str) -> str:
        ts   = datetime.utcnow().strftime("%Y%m%d%H%M%S")
        fname = f"link_analysis_{sub_id}_{ts}.md"
        kb_dir = get_knowledge_base_dir(self.chat_id)
        path = os.path.join(kb_dir, fname)
        os.makedirs(kb_dir, exist_ok=True)

        with open(path, "w", encoding="utf-8") as f:
            f.write("# Webpage Link Analysis\n\n")
            f.write(f"**Subtask ID:** {sub_id}\n\n")
            f.write(f"**Description:** {desc}\n\n")
            f.write(f"**Date:** {datetime.utcnow():%Y-%m-%d %H:%M:%S}\n\n")
            f.write(f"**URL:** {url}\n\n")
            f.write(body or "_No readable content_")
            f.write("\n\n## LLM Link Extraction Output\n\n")
            if self._last_link_raw is not None:
                f.write("\n" + self._last_link_raw.strip() + "\n \n")
            else:
                f.write("_(no LLM output; fallback regex used)_\n")

        return path


    def _save_empty_md(
        self,
        sub_id: str,
        desc: str,
        msg: str,
        raw_output: Optional[str] = None
    ) -> str:
        ts   = datetime.utcnow().strftime("%Y%m%d%H%M%S")
        fname = f"link_analysis_{sub_id}_{ts}.md"
        kb_dir = get_knowledge_base_dir(self.chat_id)
        path = os.path.join(kb_dir, fname)
        os.makedirs(kb_dir, exist_ok=True)

        with open(path, "w", encoding="utf-8") as f:
            f.write("# Link Analysis: No Link Detected\n\n")
            f.write(f"**Subtask ID:** {sub_id}\n\n")
            f.write(f"**Description:** {desc}\n\n")
            f.write(f"**Date:** {datetime.utcnow():%Y-%m-%d %H:%M:%S}\n\n")
            f.write("Original user message:\n\n\n" + msg + "\n\n\n")
            f.write("## LLM Link Extraction Output\n\n")
            if raw_output is not None:
                f.write("\n" + raw_output.strip() + "\n\n")
            else:
                f.write("_(no LLM output; fallback regex used)_\n")

        return path


    # ── Manager comms ────────────────────────────────────────────────────

    def _report(self, subtask_id: str, md_path: str):
        send_message(
            self.chat_id,
            sender="link_analysis",
            receiver="manager",
            message={"subtask_id": subtask_id, "output_file": md_path},
        )
        self.logger.info(f"[{subtask_id}] reported → {md_path}")