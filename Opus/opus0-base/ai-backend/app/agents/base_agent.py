# app/agents/base_agent.py
"""
Module: base_agent.py

Common functionality shared by all Opus0 worker and manager agents. Includes
logging helpers, runtime timing utilities, and a reusable feedback handler that
other agents can call to re-run subtasks with additional instructions.
"""

import time
import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from contextlib import contextmanager

import base64
import mimetypes
from typing import List, Union
from langchain_core.messages import HumanMessage
from app.core.config import settings

from app.utils.communication import send_message, receive_message
from app.utils.task_status import update_subtask_status
from app.utils.constants import DATA_DIR, SLEEP_INTERVAL, get_user_uploads_dir
from app.utils.live_updates import emit_log

# ──────────────────────────────────────────────────────────────
#          MODULE-LEVEL “ANALYSIS” LOGGER CONFIGURATION
# ──────────────────────────────────────────────────────────────

_ANALYSIS_LOGGER = logging.getLogger("agent_analysis")
if not _ANALYSIS_LOGGER.handlers:
    analysis_log = DATA_DIR / "agent_analysis.log"
    DATA_DIR.mkdir(parents=True, exist_ok=True)
    fh = logging.FileHandler(analysis_log)
    fh.setLevel(logging.INFO)
    # now include a %(unit)s so we can show "s" for time or "" for tokens
    fh.setFormatter(
        logging.Formatter(
            "[%(asctime)s] %(agent_id)s %(model)s %(label)s: %(duration).4f%(unit)s"
        )
    )
    _ANALYSIS_LOGGER.addHandler(fh)
    _ANALYSIS_LOGGER.propagate = False
_ANALYSIS_LOGGER.setLevel(logging.INFO)


class BaseAgent(ABC):
    """
    Abstract base class for agents.
    """

    def __init__(self, agent_id: str, agent_type: str):
        self.agent_id = agent_id
        self.agent_type = agent_type
        self.current_task: Optional[Dict[str, Any]] = None

        # normal logging goes to root/parent handlers only
        self.logger = self.setup_logger()

        # analysis logger is optional—only log if someone calls it
        self.analysis_logger = _ANALYSIS_LOGGER

        # storage for named timers
        self._timers: Dict[str, float] = {}

    def setup_logger(self) -> logging.Logger:
        """
        Agent-specific logger. Propagates up to the root handlers.
        """
        logger = logging.getLogger(self.agent_id)
        logger.propagate = True
        logger.setLevel(logging.INFO)
        return logger

    def start_timer(self, label: str):
        """Begin timing a labeled operation."""
        self._timers[label] = time.perf_counter()

    def stop_timer(self, label: str):
        """
        End timing, compute duration, and emit to:
          - analysis_logger (with agent_id, label, duration, unit)
          - the agent’s own logger for quick reference
        """
        start = self._timers.pop(label, None)
        if start is None:
            self.logger.warning(f"Timer '{label}' was not started.")
            return
        duration = time.perf_counter() - start

        # structured write to analysis.log—use 's' unit for seconds
        self.analysis_logger.info(
            "",
            extra={
                "agent_id": self.agent_id,
                "model": "",
                "label": label,
                "duration": duration,
                "unit": "s",
            },
        )

        # convenience message in normal agent log
        self.logger.info(f"[analysis] {label} took {duration:.4f}s")

    @contextmanager
    def time_block(self, label: str):
        """
        Context manager for timing:

            with self.time_block("step"):
                ...
        """
        self.start_timer(label)
        try:
            yield
        finally:
            self.stop_timer(label)

    def log_input_tokens(self, model: str, input_tokens: int):
        """
        Emit input‐token metric under the analysis logger, no 's' unit.
        """
        self.analysis_logger.info(
            "",
            extra={
                "agent_id": self.agent_id,
                "model": model,
                "label": "input_tokens",
                "duration": float(input_tokens),
                "unit": "",
            },
        )
        self.logger.info(f"[analysis] model={model} input_tokens={input_tokens}")

    def log_output_tokens(self, model: str, output_tokens: int):
        """
        Emit output‐token metric under the analysis logger, no 's' unit.
        """
        self.analysis_logger.info(
            "",
            extra={
                "agent_id": self.agent_id,
                "model": model,
                "label": "output_tokens",
                "duration": float(output_tokens),
                "unit": "",
            },
        )
        self.logger.info(f"[analysis] model={model} output_tokens={output_tokens}")

    def runtime_log(self, step: str):
        """
        Emit a “subtask_progress” frame for the current subtask.
        """
        sid = self.current_task.get("subtask_id") if self.current_task else "unknown"
        emit_log(
            event="subtask_progress",
            subtask_id=sid,
            subtask_desc=f"<{self.agent_type}>: {step}",
        )

    def runtime_log_stream(self, chunk: str):
        """Emit a streaming chunk for the current subtask."""
        sid = self.current_task.get("subtask_id") if self.current_task else "unknown"
        emit_log(
            event="subtask_stream",
            subtask_id=sid,
            subtask_desc=f"<runtimeStream>{chunk}</runtimeStream>",
        )

    def normalize_uploads(self, raw: Union[str, List[str]]) -> List[str]:
        """Turn comma-separated or list into List[str]."""
        if isinstance(raw, str):
            return [fn.strip() for fn in raw.split(",") if fn.strip()]
        return raw or []

    def build_image_message(self, uploads: List[str]) -> HumanMessage:
        """
        Given filenames in USER_UPLOADS_DIR, read + base64-embed each as an
        image_url HumanMessage, using correct MIME types.
        """
        UP = get_user_uploads_dir(getattr(self, "chat_id", ""))
        parts: List[dict] = [
            {"type": "text", "text": "Here are the images to reference:"}
        ]
        for fn in uploads:
            p = UP / fn
            if not p.is_file():
                self.logger.warning(f"upload not found: {p}")
                continue

            raw = p.read_bytes()
            b64 = base64.b64encode(raw).decode("utf-8")

            # try the stdlib guess
            mime, _ = mimetypes.guess_type(p.name)
            if not mime:
                # fallback for any weird extension
                ext = p.suffix.lstrip(".").lower()
                if ext == "jpg":
                    ext = "jpeg"
                mime = f"image/{ext}"

            parts.append(
                {"type": "image_url", "image_url": {"url": f"data:{mime};base64,{b64}"}}
            )

        return HumanMessage(content=parts)

    async def apply_feedback(
        self,
        target_agent: "BaseAgent",
        feedback: str,
        previous_output: str,
        task_details: Dict[str, Any],
        past_refinements: Optional[List[Dict[str, str]]] = None,
    ) -> str:
        """Apply ``feedback`` to ``previous_output`` using ``target_agent``.

        This helper rebuilds the target agent's messages and re-invokes its base
        response generation with a final HumanMessage describing the desired
        improvements.

        Args:
            target_agent: The agent that should generate the revised response.
            feedback: Improvement instructions to apply.
            previous_output: The current result from which to iterate.
            task_details: Subtask details used for building the base messages.
            past_refinements: Prior refinements including text and output.

        Returns:
            The refined response produced by ``target_agent``.
        """

        base_messages = target_agent._build_messages(
            history=task_details.get("history", ""),
            user_input=task_details.get("user_message", ""),
            subtask_description=task_details.get("subtask_description", ""),
            deps_info=task_details.get("deps_info", ""),
            uploaded_images=target_agent.normalize_uploads(
                task_details.get("uploaded_images", [])
            ),
        )

        refinements_block = ""
        if past_refinements:
            parts = []
            for idx, ref in enumerate(past_refinements, start=1):
                parts.append(
                    f"Refinement {idx} ({ref.get('agent', '')}): {ref.get('instruction', '')}\n"
                    f"Output:\n{ref.get('output', '')}"
                )
            refinements_block = "\n\n".join(parts) + "\n\n"

        refine_msgs = base_messages + [
            HumanMessage(
                content=(
                    f"Previous result:\n{previous_output}\n\n"
                    f"{refinements_block}Improvements to apply:\n{feedback}\n\n"
                    "Revised response:"
                )
            )
        ]

        # ensure that streaming logs remain under the parent agent's runtime
        # card by temporarily reusing this agent's runtime_log_stream
        orig_stream = target_agent.runtime_log_stream
        target_agent.runtime_log_stream = self.runtime_log_stream
        try:
            return await target_agent._generate_base_response(refine_msgs)
        finally:
            target_agent.runtime_log_stream = orig_stream

    def main_loop(self):
        self.logger.info("Starting main loop.")
        while True:
            try:
                message = receive_message(getattr(self, "chat_id", ""), self.agent_id)
                if message:
                    self.logger.info(f"Received message: {message}")
                    self.handle_message(message)
            except Exception as e:
                self.logger.error(f"Error in main loop: {e}")
                self.handle_error(e)
            time.sleep(SLEEP_INTERVAL)

    @abstractmethod
    def execute_task(self, task_details: Dict[str, Any]):
        pass

    def report_results(self, results: Dict[str, Any]):
        self.logger.info(f"Results reported: {results}")

    def handle_error(self, error: Exception):
        self.logger.error(f"Error occurred: {error}")

    def handle_feedback(self, feedback: Dict[str, Any]):
        self.logger.info(f"Processing feedback: {feedback}")

    def update_task_status(self, status: str):
        if not self.current_task:
            self.logger.error("No current task to update status for.")
            return
        subtask_id = self.current_task.get("subtask_id")
        update_subtask_status(getattr(self, "chat_id", ""), subtask_id, status)
        self.logger.info(f"Updated task status for subtask {subtask_id} to {status}")
