# app/agents/agent_factory.py
"""
Module: agent_factory.py

This module defines the AgentFactory class that listens for manager->worker messages,
routes the subtasks to an appropriate worker agent (LLMWorkerAgent, ReasoningWorkerAgent,
ResearcherAgent, WebScraperAgent, or LinkAnalysisAgent) using a GPT-based router, and then
triggers the worker agent's task execution. The factory continuously polls for new messages
and stops when a done_event is set by the manager.
"""

import os
import asyncio
import logging
from typing import Dict, Any, Optional

from app.core.config import settings
from app.agents.base_agent import BaseAgent
from app.utils.token_counter import count_tokens

from app.utils.communication import receive_message
from app.utils.constants import SLEEP_INTERVAL, FAST_INTERVAL
from app.agents.workers.llm_worker_agent import LLMWorkerAgent
from app.agents.workers.reasoning_worker_agent import ReasoningWorkerAgent
from app.agents.workers.researcher_agent import ResearcherAgent
from app.agents.workers.code_interpreter_agent import CodeInterpreterAgent
from app.agents.workers.web_scraper_agent import (
    WebScraperAgent,
)  # <-- Import the new WebScraperAgent
from app.agents.workers.link_analysis_agent import LinkAnalysisAgent
from app.agents.workers.pdf_generator_agent import PDFGeneratorAgent
from app.agents.workers.docx_generator_agent import DOCXGeneratorAgent
from app.agents.workers.slides_generator_agent import SlidesGeneratorAgent
from app.agents.workers.xlsx_generator_agent import XLSXGeneratorAgent

# Import the LLM or ChatOpenAI for the routing mechanism
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser

logger = logging.getLogger(__name__)

# ---------------------------------------------------------------------
# Define a router chain that uses a small LLM (model="chatgpt-4o-latest").
# This chain will receive a short prompt with the subtask description
# and user message, and output one of the agent keywords: "llm",
# "reasoning", "web_scraper", "researcher", "link_analysis",
# "pdf_generator", "docx_generator", "xlsx_generator", or "code_interpreter" or others.
# ---------------------------------------------------------------------

router_model = ChatGoogleGenerativeAI(
    api_key=settings.GOOGLE_API_KEY,
    model="gemini-2.5-flash",
    temperature=0.0,
    top_p=0.1,
)

router_prompt = ChatPromptTemplate.from_template(
    """
# **Role:**
You are a part of an office of AI Agents. Your task is to analyze the overall user task for context and then, given the subtask description, determine which AI worker agent is best suited to handle that subtask. You have the following possible AI worker agents:
1) **llm**: a standard language model for typical tasks (e.g., conversation, summarization, writing). Use it when you need to write something.
2) **reasoning**: a reasoning-based agent for complex math, logic, and advanced coding tasks. USE IT WHEN WRITING COMPLETE CODE FILES, OR DESIGNING COMPLEX THINGS. Use thinking agent when doing very basic coding and basic designing. DO NOT use for creating documents, Excel files, PDFs, or Word documents - use the specific document generators instead.
3) **web_scraper**: an agent specialized in scraping the web and conducting research. Don't use when you need to write something. This mainly just fetches information and data from the internet.
4) **researcher**: an autonomous multi-round researcher that iteratively searches the web, extracts content from multiple sites, and produces concise summaries.
5) **link_analysis**: an agent that processes links provided in the user message and subtask description. Use when need to analyse a given web link or youtube video. NEVER USE WHEN NO LINK GIVEN IN THE SUBTASK. Also, not all tasks with a link need link analyser.
6) **pdf_generator**: an agent that creates PDF documents based on generated outlines and content. Use this agent only when the subtask explicitly asks to generating or create a PDF document. Not when the subtask wants to plan something about the pdf when the subtask wants to generate information.
7) **docx_generator**: an agent that creates DOCX/Word documents based on generated outlines and content. Use this agent only when the subtask explicitly asks to generate or create a DOCX, Word document, or .docx file. Not for planning or research.
8) **slides_generator**: an agent that creates PowerPoint presentations (.pptx files) based on generated outlines and content. Use this agent only when the subtask explicitly asks to generate or create a presentation, slideshow, or PowerPoint document.
9) **xlsx_generator**: an agent that creates Excel (.xlsx) spreadsheets based on generated outlines and data. Use this agent when the subtask asks to generate, create, or build an Excel file, spreadsheet, .xlsx file, workbook, or any tabular data in Excel format. Keywords: Excel, spreadsheet, .xlsx, workbook, tabular data, data tracking, budget tracker, inventory sheet. Not for planning or research.
10) **code_interpreter**: an agent that executes short Python code snippets inside a sandbox and returns the output. Use when the user explicitly requests code execution.

**Important:**
If the user specifies a particular agent, always use that one. Do not assign a task to **pdf_generator**, **slides_generator** or **docx_generator** or **xlsx_generator** for research or general information gathering.
Don't use PDF agent for things like planning pdf layout, generating styling for the pdf. Only use when asked to generate the pdf explicitly!!
Similarly, only use **slides_generator** when explicitly asked to create presentations, slideshows, or PowerPoint files.

**CRITICAL: Document Generation Priority**
- If the subtask mentions creating, generating, or building an Excel file, spreadsheet, .xlsx file, or workbook → ALWAYS use **xlsx_generator**
- If the subtask mentions creating, generating, or building a PDF document or .pdf file → ALWAYS use **pdf_generator**
- If the subtask mentions creating, generating, or building a Word document, DOCX, or .docx file → ALWAYS use **docx_generator**
- Same goes for the slides agent and its use.
- Do NOT use reasoning agent for document creation tasks - use the specific document generators!

# **Instructions:**
Based on the provided user message and subtask description, respond with exactly one word (case-sensitive) from the following options: **llm**, **reasoning**, **web_scraper**, **researcher**, **link_analysis**, **pdf_generator**, **docx_generator**, **xlsx_generator**, **slides_generator**, **code_interpreter**.
Do not provide any additional text or explanation.

# **Context Available:**

**User message (For context, to understand what the user wants):** {user_message}

---

**Subtask description (This is the work we need to find an agent for. Find an AI worker agent that will be the best for this subtask):** {subtask_description}

---

- **Additional dependency information:** {deps_info}

---

- **Chat History:** {history}

---

- **Task Status (contains all the tasks that the system needs to go through. You need to give the agent for the specific subtask given to you.):** {task_status}

---


# **Examples:**

## **Example 1: Translation Task**
- **User Message:** "Translate 'How are you' into Spanish."
- **Subtask Description:** "Translate 'How are you' into Spanish."
- **Expected Output:** llm

## **Example 2: Capital Cities Task**
- **User Message:** "What are the capitals of France, UK, India, and the US?"
- **Subtask Description:** "Identify the capital of France"
- **Expected Output:** llm
- **Context:** we chose llm instead of web scraper because this is too basic of a search that the llm would know it.

## **Example 3: Healthcare Task - Simple**
- **User Message:** "Compile a list of FDA-approved medications for hypertension. Gather details like generic name, brand name, manufacturer, and side effects for each."
- **Subtask Description:** "Find 5 FDA-approved medications for hypertension"
- **Expected Output:** web_scraper
- **Context:** we need up to date information about something like this.

## **Example 4: AI Researchers Task**
- **User Message:** "Find about the top 5 AI department universities in London and give 10 professors from each, including their contact details."
- **Subtask Description:** "Find 10 professors from UCL and their contact details."
- **Expected Output:** web_scraper

## **Example 5: Research Project - Complex**
- **User Message:** "Create an itinery for my 2 week trip to London. I want to travel hidden places and crazy raves while also exploring food. Give estimated budget and tips too. "
- **Subtask Description:** "Find estimated cost for the 2 week trip."
- **Expected Output:** llm

## **Example 6: Itinerary Plan - Complex**
- **User Message:** "Plan a 7-day trip to Japan focusing on hidden cultural experiences and advanced tech attractions, all within a budget of 3000 pounds."
- **Subtask Description:** "Create a day-by-day itinerary that includes cultural spots, tech attractions, cost estimation, and travel tips."
- **Expected Output:** llm

## **Example 7: Itinerary Plan - Complex**
- **User Message:** "Plan a 7-day trip to Japan focusing on hidden cultural experiences and advanced tech attractions, all within a budget of 3000 pounds."
- **Subtask Description:** "Find hidden cultural experiences to do in Japan."
- **Expected Output:** web_scraper

## **Example 7: Research Paper PDF**
- **User Message:** "I need a research paper on AI advancements in multi-agent systems."
- **Subtask Description:** "Generate a PDF with sections including abstract, introduction, and detailed analysis on multi-agent systems."
- **Expected Output:** pdf_generator

## **Example 8: PDF designing and creation**
- **User Message:** "generate a pdf for a 7 day japan trip. Give everything in the pdf that a tourist would need in a fun well styled format"
- **Subtask Description:** "create a detailed plan for days 1-3 of the 7-day japan itinerary, focusing on tokyo, including specific attractions like senso-ji temple, meiji jingu shrine, and shinjuku gyoen national garden, activities, estimated times, costs, and relevant photos, based on the researched information and the designed pdf template."
- **Expected Output:** llm

## **Example 9: DOCX Document Creation**
- **User Message:** "I need a Word document with my project proposal for the new marketing campaign."
- **Subtask Description:** "Generate a DOCX document with sections including executive summary, campaign objectives, target audience analysis, and budget breakdown."
- **Expected Output:** docx_generator

## **Example 10: Word Document Request**
- **User Message:** "Create a docx file with the meeting notes from today's discussion."
- **Subtask Description:** "Create a Word document containing formatted meeting notes with attendees, agenda items, decisions made, and action items."
- **Expected Output:** docx_generator

## **Example 11: Excel Spreadsheet Creation**
- **User Message:** "I need an Excel file with quarterly sales data for our three product lines."
- **Subtask Description:** "Create an Excel file (.xlsx) with quarterly sales performance data including Q1-Q4 figures for Software, Hardware, and Services product lines."
- **Expected Output:** xlsx_generator

## **Example 12: Budget Tracker Spreadsheet**
- **User Message:** "Create a budget tracking spreadsheet for our department expenses."
- **Subtask Description:** "Generate a .xlsx file for tracking monthly expenses by category with budget vs actual comparisons."
- **Expected Output:** xlsx_generator

## **Example 13: Solving university level math problem**
- **User Message:** " Given sin(A ± B) = sin A cos B ± cos A sin B and cos(A ± B) = cos A cos B ∓ sin A sin B, show thatcos A sin B =12[sin(A + B) − sin(A − B)] and sin2 A =12[1 − cos 2A \n\n solve this above problem"
- **Subtask Description:** "Solve the problem provided by the user"
- **Expected Output:** reasoning

## **Example 14: Programming**
- **User Message:** " Look at this code .... update the exec function to have ... complex logic"
- **Subtask Description:** "Try understanding the .... and fix the ... parameters to fix the funciton"
- **Expected Output:** reasoning

## **Example 11: PowerPoint Presentation Creation**
- **User Message:** "Create a presentation about renewable energy trends for my business meeting."
- **Subtask Description:** "Generate a PowerPoint presentation with slides covering renewable energy innovations, market trends, and future outlook."
- **Expected Output:** slides_generator

## **Example 12: Slideshow for Educational Content**
- **User Message:** "I need a slideshow explaining artificial intelligence basics for my students."
- **Subtask Description:** "Create a presentation that explains AI concepts, machine learning, and real-world applications in an educational format."
- **Expected Output:** slides_generator

## **Example 13: Company Presentation**
- **User Message:** "Build a company overview presentation for our startup pitch to investors."
- **Subtask Description:** "Generate a professional presentation covering our mission, products, market opportunity, and financial projections."
- **Expected Output:** slides_generator

# **IMPORTANT NOTE:** Reasoner agent is always very expensive. Only use it when absolutely necessary, when working on complex or lengthy code or math problems. Don't use it for basic calculations or programs.

# **Output Format:**
Respond with exactly one of the following words: llm, reasoning, web_scraper, researcher, link_analysis, pdf_generator, docx_generator, slides_generator, xlsx_generator, code_interpreter


"""
)

# Combine into a chain.
router_chain = router_prompt | router_model | StrOutputParser()
# ------------------------------------------------------------------

MAX_CONCURRENT_WORKERS = 5  # ← tune this limit to your liking


class AgentFactory(BaseAgent):
    """
    AgentFactory continuously checks for manager->worker messages.

    When a message is found, it routes the subtask to the appropriate worker agent
    (LLMWorkerAgent, ReasoningWorkerAgent, ResearcherAgent, WebScraperAgent, or LinkAnalysisAgent)
    based on a GPT-based router decision, and then triggers the worker agent's task execution.
    The factory runs asynchronously until a done_event is signaled by the manager.
    """

    # ------------------------------------------------------------------ #
    #                       ── INITIALISATION ──                         #
    # ------------------------------------------------------------------ #
    def __init__(
        self,
        agent_id: str = "factory",
        done_event: Optional[asyncio.Event] = None,
        max_parallel: int = 5,  # <- cap concurrent workers
        chat_id: str = "",
    ):
        """Initialize the AgentFactory."""

        super().__init__(agent_id, agent_type="factory")
        self.done_event = done_event
        self.chat_id = chat_id

        # NEW: bookkeeping for concurrently‑running worker tasks
        self._running: set[asyncio.Task] = set()
        self._sem: asyncio.Semaphore = asyncio.Semaphore(max_parallel)

    async def execute_task(self, task_details: Dict[str, Any]):
        """Compatibility stub for BaseAgent."""
        self.logger.info("AgentFactory.execute_task called but not implemented")

    def setup_logger(self) -> logging.Logger:
        """
        Sets up the logger for the AgentFactory.

        Returns:
            logging.Logger: Configured logger instance.
        """
        logger = logging.getLogger(self.agent_id)
        if not logger.handlers:
            # Rely on the centralized logging configuration.
            logger.addHandler(logging.NullHandler())
        logger.setLevel(logging.INFO)
        return logger

    # ------------------------------------------------------------------ #
    #                     ── MESSAGE POLLING LOOP ──                     #
    # ------------------------------------------------------------------ #
    async def find_manager_to_worker_messages(self) -> bool:
        """
        Asynchronously checks for new messages from the manager intended for workers.

        If a message is found, processes the subtask and returns True; otherwise, returns False.

        Returns:
            bool: True if a message was processed, False otherwise.
        """
        message = receive_message(self.chat_id, "worker")
        if message:
            # NOTE: *don't* await – we launch it and return immediately
            await self.process_subtask(message)
            return True
        return False

    # ------------------------------------------------------------------ #
    #                   ── ROUTING   &  AGENT  PICKING ──                #
    # ------------------------------------------------------------------ #
    async def route_subtask(
        self,
        subtask_description: str,
        user_message: str,
        history: str = "",
        deps_info: str = "",
        task_status: str = "",
    ) -> str:
        """
        Uses a small LLM router to decide among: llm, reasoning, web_scraper,
        researcher, link_analysis, pdf_generator, docx_generator, slides_generator, xlsx_generator, or code_interpreter

        Prepares input data for the router, accumulates response chunks from the asynchronous chain,
        cleans up the output, and returns the chosen agent type.

        Args:
            subtask_description (str): Description of the subtask.
            user_message (str): The original user message.
            history (str): Chat history string from the manager.
            deps_info (str): Collected dependency info for the subtask.
            task_status (str): JSON string of the task_status subtasks.

        Returns:
            str: Chosen agent type (e.g., "llm", "reasoning", "web_scraper",
            "researcher", "link_analysis", "pdf_generator", "docx_generator", "slides_generator", "xlsx_generator" or "code_interpreter")
        """
        
        self.logger.info("Routing subtask via GPT-router...")
        input_data = {
            "subtask_description": subtask_description,
            "user_message": user_message,
            "history": history,
            "deps_info": deps_info,
            "task_status": task_status,
        }

        filled = router_prompt.format_prompt(**input_data).to_string()
        in_tokens = count_tokens(filled)
        self.log_input_tokens(router_model.model, in_tokens)

        response_chunks = []
        async for chunk in router_chain.astream(input_data):
            response_chunks.append(chunk)
        router_output = "".join(response_chunks).strip().lower()
        out_tokens = count_tokens(router_output)
        self.log_output_tokens(router_model.model, out_tokens)
        self.logger.info(f"Router raw output: '{router_output}'")
        if router_output.startswith('"') and router_output.endswith('"'):
            router_output = router_output[1:-1].strip()
        chosen_agent_type = "llm"
        if "web_scraper" in router_output:
            chosen_agent_type = "web_scraper"
        elif "reasoning" in router_output:
            chosen_agent_type = "reasoning"
        elif "researcher" in router_output:
            chosen_agent_type = "researcher"
        elif "link_analysis" in router_output:
            chosen_agent_type = "link_analysis"
        elif "code_interpreter" in router_output:
            chosen_agent_type = "code_interpreter"
        elif "llm" in router_output:
            chosen_agent_type = "llm"
        elif "pdf_generator" in router_output:
            chosen_agent_type = "pdf_generator"
        elif "docx_generator" in router_output:
            chosen_agent_type = "docx_generator"
        elif "slides_generator" in router_output:
            chosen_agent_type = "slides_generator"
        elif "code_interpreter" in router_output:
            chosen_agent_type = "code_interpreter"
        elif "xlsx_generator" in router_output:
            chosen_agent_type = "xlsx_generator"
        self.logger.info(f"Router chose: '{chosen_agent_type}'")
        return chosen_agent_type

    # ------------------------------------------------------------------ #
    #                     ── WORKER LAUNCH HELPER ──                      #
    # ------------------------------------------------------------------ #
    async def _run_worker(self, agent, task_details: Dict[str, Any]):
        """
        Runs a single worker inside the concurrency semaphore, logs completion/exception,
        and removes the asyncio.Task from the _running set via callback.
        """
        async with self._sem:
            try:
                await agent.execute_task(task_details)
                self.logger.info(f"Subtask {task_details['subtask_id']} done.")
            except Exception as e:
                self.logger.exception(
                    f"Worker error on subtask {task_details['subtask_id']}: {e}"
                )

    # ------------------------------------------------------------------ #
    #                     ── DISPATCH A SUBTASK ──                        #
    # ------------------------------------------------------------------ #
    async def process_subtask(self, message: Dict[str, Any]):
        """
        Processes a manager->worker message by routing the subtask to the appropriate worker agent.

        Extracts task details from the message, determines the best agent using the router, instantiates
        the corresponding agent, and triggers its task execution.

        Args:
            message (Dict[str, Any]): Message from the manager containing subtask details.
        """
        subtask_id = message["subtask_id"]
        subtask_description = message["subtask_description"]
        user_message = message["user_message"]
        self.logger.info(f"Received subtask {subtask_id}")
        chosen_agent_type = await self.route_subtask(
            subtask_description,
            user_message,
            history=message.get("history", ""),
            deps_info=message.get("deps_info", ""),
            task_status=message.get("task_status", ""),
        )
        if chosen_agent_type == "reasoning":
            agent = ReasoningWorkerAgent(
                agent_id=f"reasoning_worker_agent_{subtask_id}", chat_id=self.chat_id
            )
        elif chosen_agent_type == "researcher":
            agent = ResearcherAgent(
                agent_id=f"researcher_agent_{subtask_id}", chat_id=self.chat_id
            )
        elif chosen_agent_type == "web_scraper":
            agent = WebScraperAgent(
                agent_id=f"web_scraper_agent_{subtask_id}", chat_id=self.chat_id
            )
        elif chosen_agent_type == "link_analysis":
            agent = LinkAnalysisAgent(
                agent_id=f"link_analysis_agent_{subtask_id}", chat_id=self.chat_id
            )
        elif chosen_agent_type == "pdf_generator":
            agent = PDFGeneratorAgent(
                agent_id=f"pdf_generator_agent_{subtask_id}", chat_id=self.chat_id
            )
        elif chosen_agent_type == "docx_generator":
            agent = DOCXGeneratorAgent(
                agent_id=f"docx_generator_agent_{subtask_id}", chat_id=self.chat_id
            )
        elif chosen_agent_type == "slides_generator":
            agent = SlidesGeneratorAgent(
                agent_id=f"slides_generator_agent_{subtask_id}", chat_id=self.chat_id
            )
        elif chosen_agent_type == "xlsx_generator":
            agent = XLSXGeneratorAgent(
                agent_id=f"xlsx_generator_agent_{subtask_id}", chat_id=self.chat_id
            )
        elif chosen_agent_type == "code_interpreter":
            agent = CodeInterpreterAgent(
                agent_id=f"code_interpreter_agent_{subtask_id}", chat_id=self.chat_id
            )
        else:
            agent = LLMWorkerAgent(
                agent_id=f"llm_worker_agent_{subtask_id}", chat_id=self.chat_id
            )

        # Simply carry over everything—including “history”—into task_details
        task_details = {
            "subtask_id": subtask_id,
            "subtask_description": subtask_description,
            "user_message": user_message,
            "deps_info": message.get("deps_info", "n/a"),
            "uploaded_images": message.get("uploaded_images", []),
            "history": message.get("history", ""),  # now a clean string
            "parameters": message.get("parameters", {}),
        }
        self.logger.info(f"Exec subtask {subtask_id} with {chosen_agent_type}")
        # let the agent know which subtask it’s working on:
        agent.current_task = task_details
        # now launch it
        worker_task = asyncio.create_task(self._run_worker(agent, task_details))

        # NEW: launch the worker **without blocking** the factory loop
        self._running.add(worker_task)
        worker_task.add_done_callback(self._running.discard)

    # ------------------------------------------------------------------ #
    #                          ── MAIN LOOP ──                           #
    # ------------------------------------------------------------------ #
    async def run_async(self):
        """
        Main asynchronous loop for the AgentFactory.

        Continuously checks for new manager->worker messages. The loop stops if the done_event is set by the manager.
        """
        self.logger.info("Agent Factory async start")
        while True:
            # Graceful‑shutdown gate
            if self.done_event and self.done_event.is_set():
                self.logger.info(
                    "Done_event set. Waiting for running workers to finish..."
                )
                # Wait for all launched workers, but don't cancel them.
                await asyncio.gather(*self._running, return_exceptions=True)
                break

            self.logger.debug("Checking for new messages...")
            processed = await self.find_manager_to_worker_messages()

            # Poll at SLEEP_INTERVAL whether or not we just processed something
            # (or use a shorter FAST_INTERVAL for processed, if you want)
            await asyncio.sleep(FAST_INTERVAL if processed else SLEEP_INTERVAL)

        self.logger.info("Agent Factory loop ended.")
