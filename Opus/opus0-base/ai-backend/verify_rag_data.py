#!/usr/bin/env python3
"""
Simple verification script for RAG data files.
This script verifies that all dataset files exist and contain valid JSON.
"""

import json
import sys
from pathlib import Path

def print_separator(title: str):
    """Print a nice separator with title."""
    print("\n" + "="*60)
    print(f" {title} ".center(60, "="))
    print("="*60)

def verify_dataset(name: str, file_path: str):
    """Verify a single dataset file."""
    print(f"\n🔍 Verifying {name}...")
    
    path = Path(file_path)
    if not path.exists():
        print(f"❌ File not found: {file_path}")
        return False
    
    try:
        examples = []
        with open(path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if line:
                    try:
                        example = json.loads(line)
                        examples.append(example)
                    except json.JSONDecodeError as e:
                        print(f"❌ JSON error on line {line_num}: {e}")
                        return False
        
        print(f"✅ {name}: {len(examples)} examples loaded successfully")
        
        # Show first example structure
        if examples:
            first_example = examples[0]
            print(f"📋 Example structure: {list(first_example.keys())}")
            if 'task' in first_example:
                print(f"📝 Sample task: {first_example['task'][:50]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Error reading {name}: {e}")
        return False

def test_formatter_functions():
    """Test the formatter functions with real data."""
    print_separator("TESTING FORMATTER FUNCTIONS")

    # Test subtask refiner formatter with real data
    print("\n🧪 Testing subtask_refiner_example_to_text formatter with real examples...")

    try:
        # Load real examples from the dataset
        examples_file = Path("app/rag_data/subtask_refiner_examples.jsonl")
        if not examples_file.exists():
            print("❌ Subtask refiner examples file not found")
            return

        examples = []
        with open(examples_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line:
                    examples.append(json.loads(line))

        if not examples:
            print("❌ No examples found in dataset")
            return

        # Create a simple formatter function since we can't import the real one
        def simple_subtask_refiner_formatter(example: dict) -> str:
            """Simple formatter that mimics the real one."""
            all_subtasks_data = example.get("all_subtasks_data", {})
            completed_subtask_desc = example.get("completed_subtask_desc", "")
            completed_subtask_output = example.get("completed_subtask_output", "")
            pending_subtask_desc = example.get("pending_subtask_desc", "")
            response = example.get("response", {})
            reasoning = example.get("reasoning", "")

            # Format as the original subtask refiner examples
            formatted_example = f"""**All Subtasks Data:**
```json
{json.dumps(all_subtasks_data, indent=4, ensure_ascii=False)}
```

**Completed Subtask Description:**
"{completed_subtask_desc}"

**Completed Subtask Output:**
"{completed_subtask_output}"

**Pending Subtask Description:**
"{pending_subtask_desc}"

**Possible Output:**:
```json
{json.dumps(response, indent=4, ensure_ascii=False)}
```
(Reason "Not to be added in the final output": {reasoning})"""

            return formatted_example

        print(f"✅ Found {len(examples)} examples in dataset")

        # Format and display first 2 examples
        for i, example in enumerate(examples[:2], 1):
            print(f"\n{'='*80}")
            print(f"EXAMPLE {i}: {example.get('task', 'Unknown task')}")
            print(f"{'='*80}")

            formatted = simple_subtask_refiner_formatter(example)
            print(formatted)

            if i < 2:  # Add separator between examples
                print(f"\n{'-'*80}")
                print("END OF EXAMPLE")
                print(f"{'-'*80}")

        print(f"\n✅ Successfully formatted {min(2, len(examples))} examples!")
        print(f"📊 Total examples available: {len(examples)}")

    except Exception as e:
        print(f"❌ Formatter test failed: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Main verification function."""
    print("🧪 RAG Data Verification Script")
    print("=" * 50)
    
    datasets = [
        ("PDF Generator Examples", "app/rag_data/pdf_generator_examples.jsonl"),
        ("PDF Section Examples", "app/rag_data/pdf_section_examples.jsonl"),
        ("PDF Head Examples", "app/rag_data/pdf_head_examples.jsonl"),
        ("Subtask Refiner Examples", "app/rag_data/subtask_refiner_examples.jsonl"),
        ("Task Planner Examples", "app/rag_data/task_planner_examples.jsonl")
    ]
    
    print_separator("DATASET VERIFICATION")
    
    all_valid = True
    for name, file_path in datasets:
        if not verify_dataset(name, file_path):
            all_valid = False
    
    # Test formatter functions
    test_formatter_functions()
    
    print_separator("VERIFICATION COMPLETE")
    
    if all_valid:
        print("✅ All datasets are valid and ready for use!")
        print("\n📋 Next steps:")
        print("1. Install LangChain dependencies to run full RAG tests")
        print("2. Run: python test_rag_systems.py (when dependencies are available)")
        print("3. Test with real subtask refiner usage")
    else:
        print("❌ Some datasets have issues. Please fix them before proceeding.")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
