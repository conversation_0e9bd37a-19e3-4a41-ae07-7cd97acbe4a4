#!/usr/bin/env python3
"""
Test script for improved markdown parsing in DOCXGeneratorAgent

This script tests the new markdown-to-DOCX conversion to ensure
all formatting is properly handled.
"""

import sys
from pathlib import Path

# Add the app directory to the Python path
sys.path.insert(0, str(Path(__file__).parent / "app"))

try:
    from docx import Document
    from app.agents.workers.docx_generator_agent import DOCXGeneratorAgent
    DOCX_AVAILABLE = True
except ImportError:
    DOCX_AVAILABLE = False
    print("⚠️  python-docx not available, skipping DOCX tests")


def test_markdown_parsing():
    """Test the improved markdown parsing functionality."""
    
    if not DOCX_AVAILABLE:
        print("❌ Cannot test - python-docx not installed")
        return
    
    agent = DOCXGeneratorAgent(
        agent_id="test_parser",
        chat_id="test_chat_parser"
    )
    
    # Test cases for markdown formatting
    test_cases = [
        {
            "name": "Simple Bold",
            "input": "This is **bold text** in a sentence.",
            "expected_runs": [
                {"text": "This is ", "bold": False, "italic": False},
                {"text": "bold text", "bold": True, "italic": False},
                {"text": " in a sentence.", "bold": False, "italic": False}
            ]
        },
        {
            "name": "Simple Italic", 
            "input": "This is *italic text* in a sentence.",
            "expected_runs": [
                {"text": "This is ", "bold": False, "italic": False},
                {"text": "italic text", "bold": False, "italic": True},
                {"text": " in a sentence.", "bold": False, "italic": False}
            ]
        },
        {
            "name": "Bold and Italic",
            "input": "**Scale and Speed:** This is *important* information.",
            "expected_runs": [
                {"text": "Scale and Speed:", "bold": True, "italic": False},
                {"text": " This is ", "bold": False, "italic": False},
                {"text": "important", "bold": False, "italic": True},
                {"text": " information.", "bold": False, "italic": False}
            ]
        },
        {
            "name": "Nested Formatting",
            "input": "**Bold with *italic* inside** and normal text.",
            "expected_runs": [
                {"text": "Bold with ", "bold": True, "italic": False},
                {"text": "italic", "bold": True, "italic": True},
                {"text": " inside", "bold": True, "italic": False},
                {"text": " and normal text.", "bold": False, "italic": False}
            ]
        },
        {
            "name": "Multiple Bold Sections",
            "input": "**First bold** and **second bold** with normal text.",
            "expected_runs": [
                {"text": "First bold", "bold": True, "italic": False},
                {"text": " and ", "bold": False, "italic": False},
                {"text": "second bold", "bold": True, "italic": False},
                {"text": " with normal text.", "bold": False, "italic": False}
            ]
        },
        {
            "name": "No Formatting",
            "input": "Just plain text with no formatting.",
            "expected_runs": [
                {"text": "Just plain text with no formatting.", "bold": False, "italic": False}
            ]
        }
    ]
    
    print("🧪 Testing Improved Markdown Parsing...")
    print("=" * 60)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\nTest {i}: {test_case['name']}")
        print(f"Input: '{test_case['input']}'")
        
        try:
            # Create a test document and paragraph
            doc = Document()
            para = doc.add_paragraph()
            
            # Apply the markdown conversion
            agent._convert_markdown_to_docx_runs(para, test_case['input'])
            
            # Extract the actual runs
            actual_runs = []
            for run in para.runs:
                actual_runs.append({
                    "text": run.text,
                    "bold": run.bold or False,
                    "italic": run.italic or False
                })
            
            print(f"Expected: {len(test_case['expected_runs'])} runs")
            print(f"Actual:   {len(actual_runs)} runs")
            
            # Compare results
            success = True
            if len(actual_runs) != len(test_case['expected_runs']):
                print("❌ FAIL: Different number of runs")
                success = False
            else:
                for j, (expected, actual) in enumerate(zip(test_case['expected_runs'], actual_runs)):
                    if expected != actual:
                        print(f"❌ FAIL: Run {j+1} mismatch")
                        print(f"  Expected: {expected}")
                        print(f"  Actual:   {actual}")
                        success = False
            
            if success:
                print("✅ PASS")
            else:
                print("Actual runs:")
                for j, run in enumerate(actual_runs):
                    print(f"  Run {j+1}: {run}")
                    
        except Exception as e:
            print(f"❌ ERROR: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 60)


def test_list_formatting():
    """Test that list items also get proper formatting."""
    
    if not DOCX_AVAILABLE:
        return
        
    print("📝 Testing List Formatting...")
    print("=" * 60)
    
    agent = DOCXGeneratorAgent(
        agent_id="test_list_parser", 
        chat_id="test_chat_list"
    )
    
    markdown_content = """# Test Document

## Features
- **Bold feature**: This is important
- *Italic feature*: This is emphasized  
- Normal feature: This is regular

## Steps
1. **First step**: Do this thing
2. *Second step*: Do that thing
3. Normal step: Do the other thing
"""
    
    try:
        # Test creating a DOCX with formatted lists
        doc = Document()
        
        # Simulate the markdown parsing (simplified)
        lines = markdown_content.strip().split('\n')
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            if line.startswith('# '):
                doc.add_heading(line[2:], level=1)
            elif line.startswith('## '):
                doc.add_heading(line[3:], level=2)
            elif line.startswith('- '):
                para = doc.add_paragraph(style='List Bullet')
                agent._convert_markdown_to_docx_runs(para, line[2:])
            elif line.startswith(('1. ', '2. ', '3. ')):
                para = doc.add_paragraph(style='List Number')
                agent._convert_markdown_to_docx_runs(para, line[3:])
        
        print("✅ List formatting test completed successfully")
        print(f"Document created with {len(doc.paragraphs)} paragraphs")
        
    except Exception as e:
        print(f"❌ List formatting test failed: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 60)


if __name__ == "__main__":
    print("DOCXGeneratorAgent Markdown Parsing Test Suite")
    print("=" * 70)
    
    if DOCX_AVAILABLE:
        test_markdown_parsing()
        test_list_formatting()
        print("\n🎉 Markdown parsing tests completed!")
        print("The improved parsing should now handle all markdown formatting correctly.")
    else:
        print("Install python-docx to run these tests: pip install python-docx")
