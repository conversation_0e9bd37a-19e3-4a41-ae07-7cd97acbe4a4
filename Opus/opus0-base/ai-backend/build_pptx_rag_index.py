#!/usr/bin/env python3
# build_pptx_rag_index.py
"""
<PERSON>ript to build FAISS index for python-pptx code examples.

This script builds the RAG index for the new python-pptx code examples dataset
used by the SlidesGeneratorAgent.
"""

import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def build_pptx_code_index():
    """Build FAISS index for python-pptx code examples dataset."""
    try:
        from app.utils.example_rag import build_index
        
        print("🔨 Building python-pptx code RAG index...")
        
        # Build pptx_code index
        print("Building pptx_code index...")
        pptx_index, pptx_examples = build_index("pptx_code")
        print(f"✅ Built pptx_code index with {len(pptx_examples)} examples")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to build index: {e}")
        return False

def test_pptx_rag():
    """Test the python-pptx RAG functionality."""
    try:
        from app.utils.example_rag import get_pptx_code_examples_text
        
        print("\n🧪 Testing python-pptx RAG functionality...")
        
        # Test pptx code examples retrieval
        examples = get_pptx_code_examples_text("create title slide with colors", k=2)
        print(f"✅ Retrieved python-pptx code examples: {len(examples)} characters")
        
        # Print a sample of the retrieved examples
        if examples:
            print("\n📝 Sample retrieved example:")
            print("=" * 50)
            print(examples[:500] + "..." if len(examples) > 500 else examples)
            print("=" * 50)
        
        return True
        
    except Exception as e:
        print(f"❌ RAG testing failed: {e}")
        return False

def main():
    """Main function to build and test python-pptx RAG index."""
    print("🚀 Building Python-PPTX RAG Index\n")
    
    # Build index
    build_success = build_pptx_code_index()
    
    if build_success:
        # Test RAG functionality
        test_success = test_pptx_rag()
        
        if test_success:
            print("\n🎉 Python-PPTX RAG index built and tested successfully!")
            return True
        else:
            print("\n❌ RAG testing failed")
            return False
    else:
        print("\n❌ Index building failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
