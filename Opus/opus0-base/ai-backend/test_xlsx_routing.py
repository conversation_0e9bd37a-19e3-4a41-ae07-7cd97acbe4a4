#!/usr/bin/env python3
"""
Test script to verify Excel agent routing works correctly.
"""

import sys
import asyncio
from pathlib import Path

# Add the app directory to the Python path
sys.path.insert(0, str(Path(__file__).parent / "app"))

async def test_routing():
    """Test that Excel-related tasks get routed to xlsx_generator."""
    
    try:
        from app.agents.agent_factory import AgentFactory
        
        # Create agent factory
        factory = AgentFactory(chat_id="test_chat")
        
        # Test Excel-related task descriptions
        test_cases = [
            {
                "description": "Generate an Excel file with sales data",
                "user_message": "I need an Excel file with sales data",
                "expected": "xlsx_generator"
            },
            {
                "description": "Create a .xlsx spreadsheet",
                "user_message": "Create a .xlsx spreadsheet",
                "expected": "xlsx_generator"
            },
            {
                "description": "Generate a PDF document",
                "user_message": "I need a PDF document",
                "expected": "pdf_generator"  # Test that PDF routing still works
            },
            {
                "description": "Write a summary report",
                "user_message": "Write a text summary of our quarterly performance",
                "expected": "llm"  # Should NOT go to Excel agent
            }
        ]
        
        print("🧪 Testing Excel agent routing...\n")
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"Test {i}: {test_case['description']}")
            
            try:
                # Temporarily patch the logger to capture router output
                import logging

                # Create a custom handler to capture log messages
                class LogCapture(logging.Handler):
                    def __init__(self):
                        super().__init__()
                        self.messages = []

                    def emit(self, record):
                        if "Router raw output:" in record.getMessage():
                            self.messages.append(record.getMessage())

                log_capture = LogCapture()
                factory.logger.addHandler(log_capture)

                chosen_agent = await factory.route_subtask(
                    subtask_description=test_case["description"],
                    user_message=test_case["user_message"],
                    history="",
                    deps_info="",
                    task_status=""
                )

                # Show what the router actually output
                if log_capture.messages:
                    raw_output = log_capture.messages[-1].split("Router raw output: '")[1].split("'")[0]
                    print(f"   Raw router output: '{raw_output}'")

                factory.logger.removeHandler(log_capture)

                expected = test_case["expected"]
                if chosen_agent == expected:
                    print(f"✅ PASS: Routed to '{chosen_agent}' (expected '{expected}')")
                else:
                    print(f"❌ FAIL: Routed to '{chosen_agent}' (expected '{expected}')")

            except Exception as e:
                print(f"❌ ERROR: {e}")
            
            print()
        
        print("🎉 Routing test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False


async def main():
    """Run the routing test."""
    print("🚀 Starting Excel agent routing test...\n")
    
    success = await test_routing()
    
    if success:
        print("\n✅ All tests completed successfully!")
        print("The Excel agent is properly integrated into the routing system.")
    else:
        print("\n❌ Tests failed!")
        print("There may be issues with the Excel agent integration.")
    
    return success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
