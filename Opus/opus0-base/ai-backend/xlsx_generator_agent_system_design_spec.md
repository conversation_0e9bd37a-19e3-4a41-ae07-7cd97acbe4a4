# 📊 System Design Doc – XLSXGeneratorAgent

## 1 · Goal
Build a worker agent that converts LLM-generated structured data into a styled .xlsx file. It must return a presigned download link, mirroring PDFGeneratorAgent and DOCXGeneratorAgent behavior.

## 2 · Why JSON → openpyxl?

| Need | Pure openpyxl | JSON → openpyxl (chosen) |
|------|---------------|---------------------------|
| LLM simplicity | Requires verbose code | ✅ LLMs excel at structured JSON |
| Human audit trail | None | ✅ Readable JSON stored & reusable |
| Excel styling power | ✅ | ✅ (direct mapping) |
| Data structure clarity | Complex nested objects | ✅ Clear structured data |
| Parsing reliability | Manual cell manipulation | ✅ No parsing errors |

## 3 · File & Class

**File:** `app/agents/workers/xlsx_generator_agent.py`  
**Class:** `XLSXGeneratorAgent(BaseAgent)`

Follow the exact coding conventions used by PDFGeneratorAgent and DOCXGeneratorAgent (file header, separators, logging, error handling).

## 4 · Execution Flow

1. Generate **Excel outline** via <PERSON><PERSON><PERSON><PERSON> prompt (title + worksheet structure as JSON)
2. Generate **Excel data** via LangChain prompt (complete worksheet data as JSON)
3. Save both outline and data as `.json` in the knowledge-base folder
4. Process JSON data directly:
   - `worksheets[]` → Excel worksheet tabs
   - `tables[].headers[]` → Excel table headers
   - `tables[].rows[][]` → Excel table data
   - `formatting{}` → Excel styling and number formats
5. Build `.xlsx` with `openpyxl`, mapping JSON structure to Excel features
6. Save to `/sessions/{chat_id}/docs/xlsx_result_<subtask>_<timestamp>.xlsx`
7. Upload to R2 and obtain a 24-h presigned link
8. Report results via `send_message()` including the link

## 5 · MVP Scope

**Supported:**
- Multiple worksheets
- Tables with headers and data rows
- Basic cell formatting (bold headers, borders)
- Auto-sizing columns
- Data types (text, numbers, dates)
- Simple formulas in cells
- Charts and graphs (bar, line, pie charts)
- Cross-worksheet data references
- Chart positioning and conflict resolution

**Deferred:**
- Advanced formatting (colors, fonts, conditional formatting)
- Pivot tables
- Data validation
- Macros
- Images

## 6 · Technical Implementation

### 6.1 Dependencies
- **openpyxl**: Primary Excel library (already available via pandas dependency)
- **pandas**: For data manipulation and validation
- **re**: For Markdown parsing
- **datetime**: For timestamps

### 6.2 Core Methods

```python
class XLSXGeneratorAgent(BaseAgent):
    async def execute_task(task_details: Dict[str, Any])
    async def determine_xlsx_outline(subtask_desc, user_input, deps_info) -> Dict[str, Any]
    async def generate_xlsx_data(outline_plan, subtask_desc, user_input, deps_info) -> Dict[str, Any]
    def create_xlsx_from_json(json_data: Dict[str, Any], xlsx_path: Path)
    def _create_excel_worksheet(workbook, worksheet_data: Dict[str, Any])
    def _apply_excel_formatting(worksheet, formatting_config: Dict[str, Any])
    def _set_cell_value_and_format(cell, value: Any, data_type: str, formatting: Dict[str, Any])
    def save_outline_to_json_file(subtask_id: str, outline_plan: Dict[str, Any])
    def save_data_to_json_file(subtask_id: str, data: Dict[str, Any])
    def report_results(results: Dict[str, Any])
```

### 6.3 RAG Integration
Follow the same pattern as PDF and DOCX agents:
- **Dataset:** `xlsx_generator_examples.jsonl`
- **Index:** `xlsx_generator_index`
- **Functions:** `get_xlsx_examples_text()`, `get_xlsx_section_examples_text()`
- **Formatter:** `xlsx_example_to_text()`, `xlsx_section_example_to_text()`

### 6.4 File Structure
```
/sessions/{chat_id}/
├── knowledge_base/
│   ├── xlsx_outline_{subtask_id}_{timestamp}.json
│   └── xlsx_data_{subtask_id}_{timestamp}.json
└── docs/
    └── xlsx_result_{subtask_id}_{timestamp}.xlsx
```

## 7 · Prompt Design

### 7.1 Outline Prompt
Generate JSON structure defining:
- Document title
- Worksheet names and purposes
- Table structures (headers, data types)
- Basic formatting preferences

### 7.2 Data Prompt
Generate JSON with:
- Complete worksheet data structure
- Table headers and rows
- Cell values with data types
- Formulas as strings
- Formatting specifications

## 8 · Error Handling & Validation

- **JSON validation**: Ensure valid structure and required fields
- **Data type validation**: Handle mixed data types gracefully
- **Memory management**: Handle large datasets efficiently
- **Fallback behavior**: Generate simple tables if complex structures fail
- **Graceful degradation**: Skip unsupported features, log warnings
- **Schema validation**: Validate JSON against expected structure

## 9 · Integration Points

### 9.1 R2 Upload
- **Content-Type**: `application/vnd.openxmlformats-officedocument.spreadsheetml.sheet`
- **File naming**: `xlsx_result_{subtask_id}_{timestamp}.xlsx`
- **Presigned URL**: 24-hour expiration

### 9.2 Directory Structure
- Use `get_public_docx_dir()` pattern → create `get_public_xlsx_dir()`
- Follow same session-based organization

### 9.3 Constants Update
Add to `app/utils/constants.py`:
```python
def get_public_xlsx_dir(chat_id: str) -> Path:
    """Return the public_xlsx folder for ``chat_id``."""
    return get_session_dir(chat_id) / "docs"
```

## 10 · Deliverables

1. **`xlsx_generator_agent.py`** - Production-ready, logged, error-handled
2. **RAG dataset** - `app/rag_data/xlsx_generator_examples.jsonl`
3. **RAG integration** - Update `app/utils/example_rag.py`
4. **Constants update** - Add xlsx directory helper
5. **Upload support** - Update allowed extensions to include "xlsx"
6. **Tests** - Unit tests for table parsing and Excel generation

## 11 · Success Criteria

- ✅ Generates valid .xlsx files that open in Excel/Google Sheets
- ✅ Handles multiple worksheets correctly
- ✅ Preserves table structure and basic formatting
- ✅ Follows exact same patterns as PDF/DOCX agents
- ✅ Integrates with existing RAG system
- ✅ Provides reliable R2 upload and presigned URLs
- ✅ Maintains consistent error handling and logging
