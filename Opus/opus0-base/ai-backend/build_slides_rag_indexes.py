#!/usr/bin/env python3
"""
Build RAG indexes for slides generator agent
"""

import sys
from pathlib import Path

# Add the app directory to the Python path
sys.path.insert(0, str(Path(__file__).parent / "app"))

def build_slides_indexes():
    """Build FAISS indexes for slides generator datasets."""
    try:
        from app.utils.example_rag import build_index
        
        print("🔨 Building slides generator RAG indexes...")
        
        # Build slides generator index
        print("Building slides_generator index...")
        slides_index, slides_examples = build_index("slides_generator")
        print(f"✅ Built slides_generator index with {len(slides_examples)} examples")
        
        # Build slides content index
        print("Building slides_content index...")
        content_index, content_examples = build_index("slides_content")
        print(f"✅ Built slides_content index with {len(content_examples)} examples")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to build indexes: {e}")
        return False

def test_slides_rag():
    """Test the slides RAG functionality."""
    try:
        from app.utils.example_rag import get_slides_examples_text, get_slides_content_examples_text
        
        print("\n🧪 Testing slides RAG functionality...")
        
        # Test slides examples retrieval
        examples = get_slides_examples_text("renewable energy presentation", k=2)
        print(f"✅ Retrieved slides examples: {len(examples)} characters")
        
        # Test slides content examples retrieval
        content = get_slides_content_examples_text("AI presentation content", k=2)
        print(f"✅ Retrieved slides content examples: {len(content)} characters")
        
        return True
        
    except Exception as e:
        print(f"❌ RAG testing failed: {e}")
        return False

def main():
    """Main function to build and test slides RAG indexes."""
    print("🚀 Building Slides Generator RAG Indexes\n")
    
    # Build indexes
    build_success = build_slides_indexes()
    
    if build_success:
        # Test RAG functionality
        test_success = test_slides_rag()
        
        if test_success:
            print("\n🎉 Slides RAG indexes built and tested successfully!")
            return True
        else:
            print("\n❌ RAG testing failed")
            return False
    else:
        print("\n❌ Index building failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
