#!/usr/bin/env python3
"""
<PERSON>ript to rebuild RAG indexes for XLSX generator examples.
This ensures the updated examples with chart functionality are immediately available.
"""

import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.utils.example_rag import build_index, _DATASET_CONFIGS

def rebuild_xlsx_indexes():
    """Rebuild both XLSX generator and section indexes."""
    print("🔄 Rebuilding XLSX RAG indexes...")
    
    datasets_to_rebuild = ["xlsx_generator", "xlsx_section"]
    
    for dataset_name in datasets_to_rebuild:
        try:
            print(f"\n📊 Rebuilding {dataset_name} index...")
            
            config = _DATASET_CONFIGS[dataset_name]
            data_path = config["data_path"]
            index_path = config["index_path"]
            
            # Check if data file exists
            if not data_path.exists():
                print(f"❌ Data file not found: {data_path}")
                continue
                
            # Build the index
            index, examples = build_index(dataset_name, data_path, index_path)
            
            print(f"✅ Successfully rebuilt {dataset_name} index")
            print(f"   📁 Data file: {data_path}")
            print(f"   🗂️  Index path: {index_path}")
            print(f"   📈 Examples loaded: {len(examples)}")
            
            # Show sample of examples for verification
            if examples:
                first_example = examples[0]
                if 'user_input' in first_example:
                    print(f"   📝 Sample: {first_example['user_input'][:50]}...")
                elif 'task' in first_example:
                    print(f"   📝 Sample: {first_example['task'][:50]}...")
                    
        except Exception as e:
            print(f"❌ Failed to rebuild {dataset_name} index: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    return True

def verify_chart_examples():
    """Verify that chart examples are properly included."""
    print("\n🔍 Verifying chart examples...")
    
    try:
        from app.utils.example_rag import retrieve_examples
        
        # Test queries that should return chart examples
        test_queries = [
            "quarterly sales with charts",
            "financial dashboard charts",
            "revenue analysis with visualization",
            "market share pie chart"
        ]
        
        for query in test_queries:
            print(f"\n🔎 Testing query: '{query}'")
            
            # Test xlsx_generator examples
            generator_results = retrieve_examples(query, k=2, dataset_name="xlsx_generator")
            print(f"   📊 Generator results: {len(generator_results)} examples")
            
            for i, result in enumerate(generator_results, 1):
                response = result.get('response', {})
                worksheets = response.get('worksheets', [])
                has_charts = any('charts' in ws for ws in worksheets)
                print(f"      Example {i}: {response.get('title', 'Unknown')} - Charts: {'✅' if has_charts else '❌'}")
            
            # Test xlsx_section examples  
            section_results = retrieve_examples(query, k=2, dataset_name="xlsx_section")
            print(f"   📋 Section results: {len(section_results)} examples")
            
            for i, result in enumerate(section_results, 1):
                response = result.get('response', {})
                worksheets = response.get('worksheets', [])
                has_charts = any('charts' in ws for ws in worksheets)
                print(f"      Example {i}: Charts: {'✅' if has_charts else '❌'}")
                
    except Exception as e:
        print(f"❌ Verification failed: {e}")
        return False
        
    return True

def main():
    """Main function."""
    print("🚀 XLSX RAG Index Rebuild Script")
    print("=" * 50)
    
    # Rebuild indexes
    if not rebuild_xlsx_indexes():
        print("\n❌ Index rebuild failed!")
        return 1
    
    # Verify chart examples
    if not verify_chart_examples():
        print("\n❌ Chart example verification failed!")
        return 1
    
    print("\n" + "=" * 50)
    print("✅ XLSX RAG indexes successfully rebuilt!")
    print("📊 Chart examples are now available in the RAG system")
    print("\n📋 Summary of updates:")
    print("   • Added chart examples to xlsx_generator_examples.jsonl")
    print("   • Added chart examples to xlsx_section_examples.jsonl") 
    print("   • Charts include: bar, line, and pie chart types")
    print("   • Examples show proper JSON format with data_range specifications")
    print("   • RAG indexes rebuilt and verified")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
