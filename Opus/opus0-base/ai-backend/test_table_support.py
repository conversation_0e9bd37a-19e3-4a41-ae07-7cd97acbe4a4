#!/usr/bin/env python3
"""
Test script for table support in DOCXGeneratorAgent

This script tests the table parsing, validation, and DOCX creation
functionality to ensure tables are properly handled.
"""

import sys
from pathlib import Path

# Add the app directory to the Python path
sys.path.insert(0, str(Path(__file__).parent / "app"))

try:
    from docx import Document
    from app.agents.workers.docx_generator_agent import DOCXGeneratorAgent
    DOCX_AVAILABLE = True
except ImportError:
    DOCX_AVAILABLE = False
    print("⚠️  python-docx not available, skipping DOCX tests")


def test_table_validation():
    """Test table structure validation."""
    
    if not DOCX_AVAILABLE:
        return
    
    agent = DOCXGeneratorAgent("test_agent", "test_chat")
    
    print("🧪 Testing Table Validation...")
    print("=" * 50)
    
    test_cases = [
        {
            "name": "Valid Simple Table",
            "lines": [
                "| Header 1 | Header 2 |",
                "|----------|----------|",
                "| Data 1   | Data 2   |"
            ],
            "expected": True
        },
        {
            "name": "Valid Complex Table",
            "lines": [
                "| Feature | Status | Priority |",
                "|---------|--------|----------|",
                "| **Auth** | ✅ Done | High |",
                "| *Export* | 🔄 Progress | Medium |"
            ],
            "expected": True
        },
        {
            "name": "Invalid - No Separator",
            "lines": [
                "| Header 1 | Header 2 |",
                "| Data 1   | Data 2   |"
            ],
            "expected": False
        },
        {
            "name": "Invalid - Inconsistent Columns",
            "lines": [
                "| Header 1 | Header 2 |",
                "|----------|----------|",
                "| Data 1   | Data 2   | Data 3 |"
            ],
            "expected": False
        },
        {
            "name": "Invalid - Missing Pipes",
            "lines": [
                "Header 1 | Header 2",
                "|----------|----------|",
                "| Data 1   | Data 2   |"
            ],
            "expected": False
        }
    ]
    
    for test_case in test_cases:
        result = agent._validate_table_structure(test_case["lines"])
        status = "✅ PASS" if result == test_case["expected"] else "❌ FAIL"
        print(f"{status} {test_case['name']}: {result}")
    
    print()


def test_table_creation():
    """Test DOCX table creation from markdown."""
    
    if not DOCX_AVAILABLE:
        return
    
    agent = DOCXGeneratorAgent("test_agent", "test_chat")
    
    print("🏗️  Testing Table Creation...")
    print("=" * 50)
    
    # Test markdown with table
    test_markdown = """# Document with Tables

## Project Status

| Feature | Status | Priority | Assignee |
|---------|--------|----------|----------|
| **User Authentication** | ✅ Complete | High | John |
| *Data Export* | 🔄 In Progress | Medium | Jane |
| API Integration | ❌ Pending | Low | Bob |
| **Security Audit** | ✅ Complete | High | Alice |

## Comparison Table

| Metric | Q1 | Q2 | Q3 |
|--------|----|----|----| 
| Revenue | $100K | $150K | $200K |
| Users | 1,000 | 1,500 | 2,000 |
| *Growth Rate* | 10% | **15%** | 20% |

Regular paragraph after table.
"""
    
    try:
        # Create document and parse
        doc = Document()
        agent._setup_docx_styles(doc)
        
        # Parse the markdown content
        lines = test_markdown.strip().split('\n')
        i = 0
        tables_created = 0
        
        while i < len(lines):
            line = lines[i].strip()
            
            if not line:
                i += 1
                continue
                
            # Handle headings
            if line.startswith('#'):
                level = len(line) - len(line.lstrip('#'))
                heading_text = line.lstrip('#').strip()
                if heading_text and level <= 3:
                    doc.add_heading(heading_text, level=level)
                    
            # Handle tables
            elif '|' in line and line.count('|') >= 2:
                # Collect table lines
                table_lines = []
                table_start = i
                
                while i < len(lines) and lines[i].strip() and '|' in lines[i] and lines[i].count('|') >= 2:
                    table_lines.append(lines[i].strip())
                    i += 1
                
                # Create table
                if agent._validate_table_structure(table_lines):
                    agent._create_docx_table(doc, table_lines)
                    tables_created += 1
                    print(f"✅ Created table {tables_created} with {len(table_lines)} rows")
                else:
                    print(f"❌ Invalid table structure at line {table_start}")
                
                i -= 1  # Adjust for loop increment
                
            # Handle regular paragraphs
            else:
                if line:
                    para = doc.add_paragraph()
                    agent._convert_markdown_to_docx_runs(para, line)
                    
            i += 1
        
        print(f"\n📊 Results:")
        print(f"  Tables created: {tables_created}")
        print(f"  Total paragraphs: {len(doc.paragraphs)}")
        print(f"  Total tables: {len(doc.tables)}")
        
        # Verify table content
        for table_idx, table in enumerate(doc.tables):
            print(f"  Table {table_idx + 1}: {len(table.rows)} rows × {len(table.columns)} columns")
            
            # Check header formatting
            header_row = table.rows[0]
            bold_cells = 0
            for cell in header_row.cells:
                for paragraph in cell.paragraphs:
                    for run in paragraph.runs:
                        if run.bold:
                            bold_cells += 1
                            break
            print(f"    Header cells with bold formatting: {bold_cells}")
        
        if tables_created > 0:
            print("✅ SUCCESS: Tables created and formatted correctly!")
        else:
            print("❌ ISSUE: No tables were created")
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
    
    print()


def test_prompt_table_guidelines():
    """Test that the prompt contains proper table guidelines."""
    
    from app.agents.workers.docx_generator_agent import docx_section_prompt
    
    print("📝 Testing Table Prompt Guidelines...")
    print("=" * 50)
    
    prompt_text = docx_section_prompt.template
    
    required_phrases = [
        "Table Formatting Rules",
        "When to Use Tables",
        "Table Syntax",
        "| Header 1 | Header 2 | Header 3 |",
        "|----------|----------|----------|",
        "pipe character",
        "separator row with dashes",
        "**bold** for important data"
    ]
    
    missing_phrases = []
    for phrase in required_phrases:
        if phrase in prompt_text:
            print(f"✅ Found: '{phrase}'")
        else:
            print(f"❌ Missing: '{phrase}'")
            missing_phrases.append(phrase)
    
    if not missing_phrases:
        print("\n✅ SUCCESS: All table guidelines are present in prompt!")
    else:
        print(f"\n❌ ISSUE: Missing {len(missing_phrases)} required phrases")
    
    print()


def test_edge_cases():
    """Test edge cases and error handling."""
    
    if not DOCX_AVAILABLE:
        return
    
    agent = DOCXGeneratorAgent("test_agent", "test_chat")
    
    print("🔍 Testing Edge Cases...")
    print("=" * 50)
    
    edge_cases = [
        {
            "name": "Empty Table",
            "lines": ["| |", "|-|", "| |"],
            "should_create": True
        },
        {
            "name": "Single Column",
            "lines": ["| Header |", "|--------|", "| Data |"],
            "should_create": True
        },
        {
            "name": "Very Wide Table",
            "lines": [
                "| A | B | C | D | E | F | G | H |",
                "|---|---|---|---|---|---|---|---|",
                "| 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 |"
            ],
            "should_create": True
        },
        {
            "name": "Malformed Separator",
            "lines": ["| A | B |", "| --- | *** |", "| 1 | 2 |"],
            "should_create": False
        }
    ]
    
    for case in edge_cases:
        is_valid = agent._validate_table_structure(case["lines"])
        expected = case["should_create"]
        status = "✅ PASS" if is_valid == expected else "❌ FAIL"
        print(f"{status} {case['name']}: Valid={is_valid}, Expected={expected}")
    
    print()


if __name__ == "__main__":
    print("DOCXGeneratorAgent Table Support Test Suite")
    print("=" * 60)
    
    if DOCX_AVAILABLE:
        test_prompt_table_guidelines()
        test_table_validation()
        test_table_creation()
        test_edge_cases()
        print("🎉 Table support tests completed!")
        print("Tables should now be properly generated in DOCX documents.")
    else:
        print("Install python-docx to run these tests: pip install python-docx")
