#!/usr/bin/env python3
"""
Test script for XLSXGeneratorAgent

This script tests the Excel generation functionality to ensure the agent
works correctly with JSON data structures and creates valid Excel files.
"""

import sys
import asyncio
import json
from pathlib import Path

# Add the app directory to the Python path
sys.path.insert(0, str(Path(__file__).parent / "app"))

try:
    import openpyxl
    from app.agents.workers.xlsx_generator_agent import XLSXGeneratorAgent
    XLSX_AVAILABLE = True
except ImportError as e:
    XLSX_AVAILABLE = False
    print(f"⚠️  Required dependencies not available: {e}")


def test_json_parsing():
    """Test JSON parsing functionality."""
    print("🧪 Testing JSON parsing...")
    
    if not XLSX_AVAILABLE:
        return False
    
    agent = XLSXGeneratorAgent("test_agent", "test_chat")
    
    # Test valid JSON
    valid_json = '{"title": "Test", "worksheets": []}'
    result = agent._extract_json_from_response(valid_json)
    assert result is not None, "Should parse valid JSON"
    assert result["title"] == "Test", "Should extract correct data"
    
    # Test JSON in code blocks
    json_in_blocks = '''
    Here's the JSON:
    ```json
    {"title": "Test Report", "worksheets": [{"name": "Data"}]}
    ```
    '''
    result = agent._extract_json_from_response(json_in_blocks)
    assert result is not None, "Should extract JSON from code blocks"
    assert result["title"] == "Test Report", "Should extract correct data from blocks"
    
    print("✅ JSON parsing tests passed")
    return True


def test_fallback_data():
    """Test fallback data generation."""
    print("🧪 Testing fallback data generation...")
    
    if not XLSX_AVAILABLE:
        return False
    
    agent = XLSXGeneratorAgent("test_agent", "test_chat")
    
    outline = {
        "title": "Test Report",
        "worksheets": [
            {"name": "Data", "purpose": "Main data"},
            {"name": "Summary", "purpose": "Summary info"}
        ]
    }
    
    fallback_data = agent._get_fallback_data(outline)
    
    assert "worksheets" in fallback_data, "Should have worksheets"
    assert len(fallback_data["worksheets"]) == 2, "Should have 2 worksheets"
    assert fallback_data["worksheets"][0]["name"] == "Data", "Should preserve worksheet names"
    
    print("✅ Fallback data tests passed")
    return True


def test_excel_creation():
    """Test Excel file creation from JSON data."""
    print("🧪 Testing Excel file creation...")
    
    if not XLSX_AVAILABLE:
        return False
    
    agent = XLSXGeneratorAgent("test_agent", "test_chat")
    
    # Sample data structure
    excel_data = {
        "worksheets": [
            {
                "name": "Sales Data",
                "tables": [
                    {
                        "headers": [
                            {"value": "Product", "data_type": "text"},
                            {"value": "Q1 Sales", "data_type": "currency"},
                            {"value": "Q2 Sales", "data_type": "currency"},
                            {"value": "Total", "data_type": "formula"}
                        ],
                        "rows": [
                            [
                                {"value": "Software", "data_type": "text"},
                                {"value": 10000, "data_type": "currency"},
                                {"value": 12000, "data_type": "currency"},
                                {"value": "=B2+C2", "data_type": "formula"}
                            ],
                            [
                                {"value": "Hardware", "data_type": "text"},
                                {"value": 8000, "data_type": "currency"},
                                {"value": 9000, "data_type": "currency"},
                                {"value": "=B3+C3", "data_type": "formula"}
                            ]
                        ]
                    }
                ]
            }
        ]
    }
    
    outline_plan = {
        "title": "Test Sales Report",
        "formatting": {
            "currency_format": "$#,##0.00",
            "header_style": {
                "bold": True,
                "background_color": "CCCCCC"
            }
        }
    }
    
    # Create test file
    test_file = Path("test_output.xlsx")
    try:
        agent.create_xlsx_from_json(excel_data, outline_plan, test_file)
        
        # Verify file was created
        assert test_file.exists(), "Excel file should be created"
        
        # Verify file can be opened
        workbook = openpyxl.load_workbook(test_file)
        assert "Sales Data" in workbook.sheetnames, "Should have Sales Data worksheet"
        
        ws = workbook["Sales Data"]
        assert ws["A1"].value == "Product", "Should have correct header"
        assert ws["B1"].value == "Q1 Sales", "Should have correct header"
        assert ws["A2"].value == "Software", "Should have correct data"
        assert ws["B2"].value == 10000, "Should have correct numeric data"
        
        print("✅ Excel creation tests passed")
        return True
        
    finally:
        # Clean up
        if test_file.exists():
            test_file.unlink()


async def test_full_workflow():
    """Test the complete workflow without LLM calls."""
    print("🧪 Testing full workflow (without LLM)...")
    
    if not XLSX_AVAILABLE:
        return False
    
    agent = XLSXGeneratorAgent("test_agent", "test_chat")
    
    # Test outline generation fallback
    outline = agent._get_fallback_outline("Create a sales report")
    assert outline["title"] == "Data Report", "Should have default title"
    assert len(outline["worksheets"]) == 2, "Should have 2 worksheets"
    
    # Test data generation fallback
    excel_data = agent._get_fallback_data(outline)
    assert "worksheets" in excel_data, "Should have worksheets in data"
    
    print("✅ Full workflow tests passed")
    return True


def main():
    """Run all tests."""
    print("🚀 Starting XLSXGeneratorAgent tests...\n")
    
    if not XLSX_AVAILABLE:
        print("❌ Cannot run tests - missing dependencies")
        print("Please install: pip install openpyxl")
        return False
    
    tests = [
        test_json_parsing,
        test_fallback_data,
        test_excel_creation,
    ]
    
    async_tests = [
        test_full_workflow,
    ]
    
    # Run synchronous tests
    for test in tests:
        try:
            if not test():
                print(f"❌ Test {test.__name__} failed")
                return False
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with error: {e}")
            return False
        print()
    
    # Run async tests
    for test in async_tests:
        try:
            if not asyncio.run(test()):
                print(f"❌ Test {test.__name__} failed")
                return False
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with error: {e}")
            return False
        print()
    
    print("🎉 All tests passed! XLSXGeneratorAgent is working correctly.")
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
