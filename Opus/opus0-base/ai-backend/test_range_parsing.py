#!/usr/bin/env python3
"""
Test script for range parsing functionality.
"""

import sys
from pathlib import Path

# Add the app directory to the Python path
sys.path.insert(0, str(Path(__file__).parent / "app"))

try:
    from openpyxl.utils import range_boundaries
    
    def test_range_parsing():
        """Test the range parsing logic"""
        print("Testing Range Parsing Logic...")
        
        # Test cases
        test_cases = [
            "A2:A5",                    # Simple range
            "'Sales Data'!A2:A4",       # Worksheet qualified with quotes
            "Sales Data!B2:E4",         # Worksheet qualified without quotes
            "'Sheet Name'!C1:C10",      # Sheet name with spaces
            "B1:E1",                    # Header range
        ]
        
        def parse_range_fixed(range_string: str):
            """Fixed version of range parsing"""
            # Handle worksheet-qualified ranges like 'Sales Data'!A2:A4
            if '!' in range_string:
                # Split on the last '!' to handle sheet names with exclamation marks
                parts = range_string.rsplit('!', 1)
                if len(parts) == 2:
                    range_string = parts[1]  # Use only the cell range part
            
            # Remove any quotes around the range
            range_string = range_string.strip("'\"")
            
            return range_boundaries(range_string)
        
        for test_range in test_cases:
            try:
                result = parse_range_fixed(test_range)
                print(f"✅ '{test_range}' -> {result}")
            except Exception as e:
                print(f"❌ '{test_range}' -> Error: {e}")
        
        return True
    
    if __name__ == "__main__":
        print("Range Parsing Test")
        print("=" * 30)
        test_range_parsing()
        print("\n✅ Range parsing test completed!")
        
except ImportError as e:
    print(f"⚠️  Required dependencies not available: {e}")
    print("This test requires openpyxl to be installed.")
