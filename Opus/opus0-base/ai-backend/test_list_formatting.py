#!/usr/bin/env python3
"""
Test script for list formatting in DOCXGeneratorAgent

This script tests that the updated prompt and parsing logic
correctly handles both numbered lists and bullet points.
"""

import sys
from pathlib import Path

# Add the app directory to the Python path
sys.path.insert(0, str(Path(__file__).parent / "app"))

try:
    from docx import Document
    from app.agents.workers.docx_generator_agent import DOCXGeneratorAgent
    DOCX_AVAILABLE = True
except ImportError:
    DOCX_AVAILABLE = False
    print("⚠️  python-docx not available, skipping DOCX tests")


def test_list_parsing():
    """Test that both numbered lists and bullet points are parsed correctly."""
    
    if not DOCX_AVAILABLE:
        print("❌ Cannot test - python-docx not installed")
        return
    
    agent = DOCXGeneratorAgent(
        agent_id="test_list_parser",
        chat_id="test_chat_list"
    )
    
    # Test markdown content with both list types
    test_markdown = """# Test Document

## Sequential Steps (Should be numbered)
1. First step in the process
2. Second step that follows
3. Third and final step

## Features (Should be bullets)
- Feature one that's important
- Another feature to consider
- Final feature in the list

## Mixed Content
Here's a paragraph with some text.

1. This is a numbered item
2. This is another numbered item

And here are some bullet points:
- Bullet point one
- Bullet point two

## Legacy Format (asterisks - should become bullets)
*   Old style bullet with spaces
*   Another old style bullet
* Regular asterisk bullet

## Complex Lists
1. **Step One**: Do this important thing
2. *Step Two*: Do this emphasized thing
3. Step Three: Do this **bold** and *italic* thing

- **Feature A**: This is a key feature
- *Feature B*: This is an emphasized feature
- Feature C: This is a **mixed** *format* feature
"""
    
    print("🧪 Testing List Parsing with Updated Logic...")
    print("=" * 60)
    
    try:
        # Create document and parse the markdown
        doc = Document()
        agent._setup_docx_styles(doc)
        
        # Parse the markdown content
        lines = test_markdown.strip().split('\n')
        i = 0
        current_list = None
        list_type = None
        
        numbered_lists_found = 0
        bullet_lists_found = 0
        
        while i < len(lines):
            line = lines[i].strip()
            
            if not line:
                current_list = None
                list_type = None
                i += 1
                continue
                
            # Handle headings
            if line.startswith('#'):
                current_list = None
                list_type = None
                level = len(line) - len(line.lstrip('#'))
                heading_text = line.lstrip('#').strip()
                if heading_text and level <= 3:
                    doc.add_heading(heading_text, level=level)
                    
            # Handle bullet lists
            elif line.startswith('- ') or line.startswith('* '):
                if line.startswith('- '):
                    list_item_text = line[2:].strip()
                elif line.startswith('*   '):
                    list_item_text = line[4:].strip()
                else:
                    list_item_text = line[2:].strip()
                    
                if current_list is None or list_type != 'bullet':
                    current_list = []
                    list_type = 'bullet'
                current_list.append(list_item_text)
                
                # Check if next line continues the list
                next_line = lines[i + 1].strip() if i + 1 < len(lines) else ""
                if not (next_line.startswith('- ') or next_line.startswith('* ')):
                    # End of list, add all items
                    bullet_lists_found += 1
                    print(f"✅ Found bullet list with {len(current_list)} items")
                    for item in current_list:
                        para = doc.add_paragraph(style='List Bullet')
                        agent._convert_markdown_to_docx_runs(para, item)
                    current_list = None
                    list_type = None
                    
            # Handle numbered lists
            elif line.startswith(('1. ', '2. ', '3. ', '4. ', '5. ')):
                import re
                match = re.match(r'^(\d+)\.\s(.+)', line)
                if match:
                    list_item_text = match.group(2)
                    
                    if current_list is None or list_type != 'number':
                        current_list = []
                        list_type = 'number'
                    current_list.append(list_item_text)
                    
                    # Check if next line continues the list
                    next_line = lines[i + 1].strip() if i + 1 < len(lines) else ""
                    if not re.match(r'^\d+\.\s', next_line):
                        # End of list, add all items
                        numbered_lists_found += 1
                        print(f"✅ Found numbered list with {len(current_list)} items")
                        for item in current_list:
                            para = doc.add_paragraph(style='List Number')
                            agent._convert_markdown_to_docx_runs(para, item)
                        current_list = None
                        list_type = None
                        
            # Handle regular paragraphs
            else:
                current_list = None
                list_type = None
                if line:
                    para = doc.add_paragraph()
                    agent._convert_markdown_to_docx_runs(para, line)
                    
            i += 1
        
        print(f"\n📊 Results:")
        print(f"  Numbered lists found: {numbered_lists_found}")
        print(f"  Bullet lists found: {bullet_lists_found}")
        print(f"  Total paragraphs: {len(doc.paragraphs)}")
        
        if numbered_lists_found > 0 and bullet_lists_found > 0:
            print("✅ SUCCESS: Both numbered and bullet lists detected correctly!")
        else:
            print("❌ ISSUE: Missing list types")
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 60)


def test_prompt_guidelines():
    """Test that the prompt contains the right guidelines."""
    
    from app.agents.workers.docx_generator_agent import docx_section_prompt
    
    print("📝 Testing Updated Prompt Guidelines...")
    print("=" * 60)
    
    prompt_text = docx_section_prompt.template
    
    # Check for key phrases in the updated prompt
    required_phrases = [
        "List Formatting Rules",
        "Sequential/Ordered Content",
        "1. `, `2. `, `3. `",
        "Non-Sequential Content",
        "- ` (dash + space)",
        "EXACT format",
        "numbered lists (`1. `, `2. `)",
        "bullet points (`- `)"
    ]
    
    missing_phrases = []
    for phrase in required_phrases:
        if phrase in prompt_text:
            print(f"✅ Found: '{phrase}'")
        else:
            print(f"❌ Missing: '{phrase}'")
            missing_phrases.append(phrase)
    
    if not missing_phrases:
        print("\n✅ SUCCESS: All required prompt guidelines are present!")
    else:
        print(f"\n❌ ISSUE: Missing {len(missing_phrases)} required phrases")
    
    print("\n" + "=" * 60)


if __name__ == "__main__":
    print("DOCXGeneratorAgent List Formatting Test Suite")
    print("=" * 70)
    
    # Test the updated prompt
    test_prompt_guidelines()
    
    # Test the parsing logic
    if DOCX_AVAILABLE:
        test_list_parsing()
        print("\n🎉 List formatting tests completed!")
        print("The LLM should now generate proper numbered lists when appropriate.")
    else:
        print("Install python-docx to run parsing tests: pip install python-docx")
