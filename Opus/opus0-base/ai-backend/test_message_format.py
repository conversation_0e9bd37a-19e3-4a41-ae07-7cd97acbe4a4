#!/usr/bin/env python3
# test_message_format.py
"""
Test script to verify the send_message format is correct.
"""

import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_message_validation():
    """Test that our message format passes validation."""
    try:
        from app.utils.communication import send_message
        import tempfile
        import os
        
        # Create a temporary directory for testing
        with tempfile.TemporaryDirectory() as temp_dir:
            # Test the correct message format
            test_message = {
                "subtask_id": "test_123",
                "output_file": "/path/to/test/file.md"
            }
            
            print("🧪 Testing message format validation...")
            
            # This should not raise an error
            try:
                # We'll catch the file creation error but the validation should pass
                send_message(
                    chat_id="test_chat",
                    sender="worker", 
                    receiver="manager",
                    message=test_message
                )
                print("✅ Message format validation passed")
                return True
                
            except ValueError as ve:
                if "missing required keys" in str(ve):
                    print(f"❌ Message format validation failed: {ve}")
                    return False
                else:
                    # Other errors (like file creation) are expected in test environment
                    print("✅ Message format validation passed (file creation error expected)")
                    return True
            except Exception as e:
                # File creation errors are expected in test environment
                if "No such file or directory" in str(e) or "FileNotFoundError" in str(type(e).__name__):
                    print("✅ Message format validation passed (file creation error expected)")
                    return True
                else:
                    print(f"❌ Unexpected error: {e}")
                    return False
                    
    except Exception as e:
        print(f"❌ Test setup failed: {e}")
        return False

def test_invalid_message():
    """Test that invalid message format fails validation."""
    try:
        from app.utils.communication import send_message
        
        # Test invalid message format (missing required keys)
        invalid_message = {
            "wrong_key": "test_123",
            "another_wrong_key": "/path/to/test/file.md"
        }
        
        print("\n🧪 Testing invalid message format...")
        
        try:
            send_message(
                chat_id="test_chat",
                sender="worker", 
                receiver="manager",
                message=invalid_message
            )
            print("❌ Invalid message format should have failed validation")
            return False
            
        except ValueError as ve:
            if "missing required keys" in str(ve):
                print("✅ Invalid message format correctly rejected")
                return True
            else:
                print(f"❌ Unexpected validation error: {ve}")
                return False
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
            return False
            
    except Exception as e:
        print(f"❌ Test setup failed: {e}")
        return False

def main():
    """Run message format tests."""
    print("🚀 Testing Message Format for SlidesGeneratorAgent\n")
    
    tests = [
        ("Valid Message Format", test_message_validation),
        ("Invalid Message Format", test_invalid_message),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"🔍 {test_name}:")
        if test_func():
            passed += 1
        else:
            print(f"   Test failed!")
    
    print(f"\n📊 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All message format tests passed!")
        return True
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
