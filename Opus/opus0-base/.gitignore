# General .env files
.env
ai-backend/.env
web-client/.env

data/
ai-backend/data

# ai-backend (Python/Poetry with Node.js dependencies)
ai-backend/.venv
ai-backend/node_modules
ai-backend/dist
ai-backend/.pytest_cache
ai-backend/.mypy_cache
ai-backend/.coverage
ai-backend/build
ai-backend/coverage
ai-backend/*.pyc
ai-backend/*.log
ai-backend/__pycache__/
ai-backend/.idea
ai-backend/.DS_Store

# web-client (React TypeScript Vite)
web-client/node_modules
web-client/dist
web-client/.DS_Store
web-client/*.pem
web-client/.env.local
web-client/.env.development.local
web-client/.env.production.local
web-client/.cache
web-client/.vite