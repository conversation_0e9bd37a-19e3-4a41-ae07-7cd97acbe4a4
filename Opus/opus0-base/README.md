<p align="center">
  <a href="https://synagi.ai" target="_blank" rel="noopener noreferrer">
    <img src="https://synagi.ai/logo.webp" alt="Synagi Logo" width="250" />
  </a>
</p>

<h1 align="center">Opus0 — Distributed Multi‑Agent Platform</h1>
<p align="center"><em>The infrastructure behind smarter AI: a distributed network of specialised agents.</em></p>

---

## Overview

**Opus0** is the first implementation of Synagi’s general distributed multi‑agent architecture. Built around a robust **manager‑worker framework**, it coordinates specialised agents through custom communication and collaboration protocols. The result: dramatic gains in **execution speed**, **cost‑efficiency**, and **task accuracy** compared with conventional single‑agent or loosely‑coupled systems.

> **Quality‑Efficiency Index (QEI)**  — Higher is better
>
> Opus0 delivers **2×** the QEI of Manus AI on identical internal workloads.\*
>
> <sub>\*Early‑stage, in‑house figures; not a public benchmark.</sub>

---

## Architecture & Key Technologies

| Component                    | Purpose                                                                                       |
| ---------------------------- | --------------------------------------------------------------------------------------------- |
| **Manager‑Worker Framework** | Orchestrates a flexible pool of specialised worker agents under one or more manager agents.   |
| **Contextual Collaboration** | Shares relevant task context between agents without over‑loading any single agent.            |
| **Custom Agent Protocols**   | Lightweight, purpose‑built message formats for coordination, negotiation, and error handling. |

---

## Operational Capabilities

| Capability                   | Description                                                                                                        |
| ---------------------------- | ------------------------------------------------------------------------------------------------------------------ |
| **Dynamic Agent Allocation** | Spawns or retires agents in real time based on task complexity and runtime signals.                                |
| **Scalable Execution**       | Seamlessly scales compute utilisation up or down as workloads change.                                              |
| **Autonomous Operation**     | Manager agents plan, schedule, and validate sub‑tasks end‑to‑end, reducing manual oversight.                       |
| **Robustness Under Load**    | Fine‑grained context control mitigates hallucination, maintaining output quality even without external validators. |

---

## Repository Layout

```text
├─ ai-backend/    # FastAPI + Poetry project (agents, task queue, comms)
├─ web-client/    # React + Vite front-end (chat UI, runtime logs)
└─ README.md      # You are here
```

---

## Quick Start

### Prerequisites

- **Python 3.10** (or 3.11) (prefer v3.10.12)
- **Poetry** ≥ 1.8 (prefer 1.8.3)
- **Node.js** ≥ 18 (includes `npm`) (prefer v20.19.0)

---

### 1 · Clone the repo

```bash
git clone https://github.com/garv-2501/opus0-v2-latest.git
cd opus0
```

### 2 · Back-end (`ai-backend`)

```bash
cd ai-backend
poetry install               # install Python deps
poetry run playwright install # download headless browsers
poetry run python run.py      # FastAPI server → http://localhost:8000
```

#### Environment Variables

Copy `ai-backend/.env.example` to `ai-backend/.env` and fill in your API keys.
The research agent now uses the Brave Search API for URL discovery, so set
`BRAVE_SEARCH_API` to your subscription token in the `.env` file.

### 3 · Front-end (`web-client`)

```bash
cd ../web-client
npm install   # install JS deps
npm run dev   # Vite dev server → http://localhost:5173
```

Open **[http://localhost:5173](http://localhost:5173)** and start chatting – the UI connects to the back-end at **[http://localhost:8000](http://localhost:8000)** automatically.

For Google login, copy `web-client/.env.local.example` to `web-client/.env.local` and set `VITE_GOOGLE_CLIENT_ID`, `VITE_GOOGLE_CLIENT_SECRET`, and `VITE_GOOGLE_REDIRECT_URI` to match your OAuth configuration.
Login buttons now redirect straight to Google; the `/auth` route only processes the returned token.

### 4 · Back-end via Docker

```bash
docker build -t opus0-backend ./ai-backend
docker run -p 8000:8000
  -v $(pwd)/sessions:/app/sessions
  -v $(pwd)/app.log:/app/app.log
  -v $(pwd)/agent_analysis.log:/app/agent_analysis.log
  --env-file ai-backend/.env
  opus0-backend
```

### 5 · Back-end + Front-end via Docker Compose

```bash
docker compose build
docker compose up
```

This launches both services on the default network so the front‑end (port `5173`) can proxy API calls to the back‑end (port `8000`). Ensure your `.env` file inside `ai-backend/` sets `FRONTEND_URL=http://localhost:5173`.

### Accessing Generated PDFs

Any PDFs produced by the system are stored in Cloudflare R2. You can download a
file at `http://<server>:8000/docs/<filename>` which redirects to a presigned
link.

---

## Contributing

We welcome PRs that improve agent robustness, add new worker types, or enhance the front‑end UX. Please open an issue first to discuss major changes.

---

<p align="center">
  Made with ❤️ & distributed intelligence by <a href="https://synagi.ai">Synagi AI</a>
</p>
```
