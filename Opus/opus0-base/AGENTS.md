# Repository Guidelines for opus0-v2

---

---

## Project Overview

Opus0 is a **decentralised network** of collaborative AI agents. Instead of one
monolithic service, it runs a fleet of specialised workers coordinated by
manager agents. These agents share task context through lightweight protocols,
yielding faster execution, higher accuracy and better fault tolerance compared
with traditional centralised systems.

### Key folders

- `ai-backend/` – FastAPI services powering the agents.
  - `app/agents/` – base classes, worker agents and manager modules.
  - `app/api/` – REST endpoints for chat, auth and file uploads.
  - `app/db/` – database models and Mongo helpers.
  - `app/utils/` – logging, token counting and other utilities.
  - `app/services/` – integrations such as <PERSON><PERSON>hai<PERSON>.
- `web-client/` – React + Vite front end for the chat UI.
  - `src/components/chat/` – chat area, input box and message list.
  - `src/components/layout/` – sidebar menu and layout controls.
  - `src/components/Login/` – login page.
  - `src/lib/chat/` – state store and hooks for socket chat.
  - `src/lib/r2/` – helpers for file uploads.
- `assets/` – static style sheets shared by the UI.
- `data/` – session logs and generated output (ignored by Git).

---

### Tree structure

**ai-backend**

```text
app/
├── agents
│   ├── agent_factory.py
│   ├── aggregator_agent.py
│   ├── base_agent.py
│   ├── managers/
│   │   ├── operations_manager.py
│   │   └── task_planner.py
│   └── workers/
│       ├── code_interpreter_agent.py
│       ├── link_analysis_agent.py
│       ├── llm_worker_agent.py
│       ├── pdf_generator_agent.py
│       ├── reasoning_worker_agent.py
│       ├── researcher_agent.py
│       └── web_scraper_agent.py
├── api/routes/
│   ├── auth.py
│   ├── chat.py
│   ├── docs.py
│   └── files.py
├── core/config.py
├── db/
│   └── files.py
└── services/langchain_service.py
```

**web-client/src**

```text
components/
├── chat/
│   ├── ChatArea/ChatArea.tsx
│   ├── ChatInput/ChatInput.tsx
│   ├── Examples/ExampleCard.tsx
│   ├── Messages/MessageList.tsx
│   └── Welcome/Welcome.tsx
├── layout/Sidebar/Sidebar.tsx
└── Login/Login.tsx
lib/
├── chat/hooks.ts
├── r2/uploadFile.ts
└── socket.ts
```

---

---

## Requirements

- Python 3.10 or 3.11
- Poetry >= 1.8
- Node.js >= 18

---

---

## Running Locally

```bash
# Back-end
cd ai-backend
poetry install
poetry run playwright install
poetry run python run.py  # http://localhost:8000

# Front-end
cd ../web-client
npm install
npm run dev  # http://localhost:5173
```

Docker builds are available via `docker compose`.

Environment variables are defined in `.env` (see `ai-backend/.env.example` for full list).

---

---

## Code Style and Conventions (ai-backend)

### 1. File Header

Every source file **must** begin with:

```python
# <relative_file_path>
"""
Module: <filename>.py

A brief description of the module’s purpose and functionality.
"""
```

**Example**:

```python
# app/agents/task_planner.py
"""
Module: task_planner.py

This module defines the TaskPlanner class, a manager bot responsible for generating the
`task_status.json` file based on the user input. It uses GPT-4 (via LangChain) to break down
the main task into subtasks and assignment criteria, and then initializes the task status using
the generated plan. The class inherits from BaseAgent.
"""
```

---

### 2. Section Separators

Group logically related code blocks with a consistent separator:

```python
#──────────────────────────────────────────────────
#                         SECTION
#──────────────────────────────────────────────────
```

- Replace `SECTION` with descriptive titles, e.g., **INTERNAL HELPERS**, **CLASS DEFINITIONS**, **PUBLIC API**, **UNIT TESTS**.

**Example**:

```python
#──────────────────────────────────────────────────
#                         INTERNAL HELPERS
#──────────────────────────────────────────────────
```

---

### 3. Comments & Docstrings

- **Module docstrings** at the top of each file.
- **Class docstrings** immediately below the class definition.
- **Function docstrings** for all public functions/methods.
- **Inline comments** sparingly to clarify non-obvious logic.
- Follow [PEP 257](https://www.python.org/dev/peps/pep-0257/) conventions:

  - Triple-quoted strings
  - One-line summary, blank line, detailed description (if needed)

---

### 4. Functional Programming Practices

- Favor **pure functions**: no side effects, inputs → outputs only.
- Use **immutable** data structures when possible.
- Keep functions **small** and **single-purpose**.
- Prefer **higher-order functions** (`map`, `filter`, `reduce`) or comprehensions for transformations.

---

### 5. Object-Oriented Practices

- All agents inherit from `BaseAgent`.
- Apply the **Single Responsibility Principle**: classes should have one clear responsibility.
- Use **composition over inheritance** where multiple behaviors are needed.
- Keep methods **short** (ideally < 50 lines).

---

### 6. Naming Conventions

| Entity    | Style              | Example           |
| --------- | ------------------ | ----------------- |
| Files     | `snake_case.py`    | `task_planner.py` |
| Classes   | `CamelCase`        | `TaskPlanner`     |
| Functions | `snake_case`       | `plan_tasks()`    |
| Variables | `snake_case`       | `task_list`       |
| Constants | `UPPER_SNAKE_CASE` | `MAX_CONCURRENCY` |

---

### 7. Imports

1. **Standard library**
2. **Third-party packages**
3. **Local application imports**

All imports should be **absolute**, not relative.

```python
import asyncio
import logging

from langchain_openai import ChatOpenAI

from app.utils.task_status import read_task_status
```

---

### 8. Logging

- Initialize logger at the module level:

  ```python
  import logging
  logger = logging.getLogger(__name__)
  ```

- Use appropriate log levels (`DEBUG`, `INFO`, `WARNING`, `ERROR`, `CRITICAL`).

---

### 9. Type Hinting

- **Mandatory** for function signatures and return values.
- Use `typing` (e.g., `List`, `Dict`, `Optional`) for complex types.

```python
def plan_tasks(input_data: Dict[str, Any]) -> List[Task]:
    ...
```

---

### 10. Linting & Formatting

- **Black** for automatic code formatting.
- **Flake8** for linting (configurable via `.flake8`).
- Run both tools pre-commit or integrate with CI.

---

By adhering to these standards, we ensure our codebase remains clean, consistent, and scalable as the project grows.

---

---

## Environment Configuration

`ai-backend/app/core/config.py` loads settings via Pydantic. Important variables include API keys (`OPENAI_API_KEY`, `GOOGLE_API_KEY`, etc.), MongoDB credentials, and Cloudflare R2 details. Customise `.env` accordingly before running the server.

## Repository Notes

- Generated data and session files are stored under `data/` and ignored by Git.
- PDFs produced by the system can be downloaded via `/docs/<filename>` on the back-end.
- The project license forbids redistribution without written consent.
