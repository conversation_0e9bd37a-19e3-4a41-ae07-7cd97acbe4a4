# .env.local

# Your R2 account ID (12–24 character alphanumeric string)
VITE_R2_ACCOUNT_ID=abcdef1234567890abcd1234

# Your R2 endpoint (usually https://<ACCOUNT_ID>.r2.cloudflarestorage.com/)
VITE_R2_ENDPOINT=https://abcdef1234567890abcd1234.r2.cloudflarestorage.com/

# The name of your R2 bucket
VITE_R2_BUCKET_NAME=opus0-uploads

# From Dashboard → Storage → R2 → Manage R2 API Tokens:
# Your R2 access key ID (starts with “R2” or “AKIA…”)
VITE_R2_ACCESS_KEY_ID=R2ABCDEF1234567890

# Your R2 secret access key (40+ characters)
VITE_R2_SECRET_ACCESS_KEY=abcd1234efgh5678ijkl9012mnop3456qrst7890uvwx

# Use this token for authenticating against the Cloudflare API:
TOKEN_VALUE=TgBFW--2RP9XcV8SigqzDGqyvIlVl

# The URL where your FastAPI server is running (e.g., local dev or production)
VITE_API_URL=http://localhost:8000


# Google OAuth
VITE_GOOGLE_CLIENT_ID=google-auth-clientID
VITE_GOOGLE_CLIENT_SECRET=google-auth-key
# Full redirect URL registered with your Google OAuth client
VITE_GOOGLE_REDIRECT_URI=http://localhost:5173/auth
