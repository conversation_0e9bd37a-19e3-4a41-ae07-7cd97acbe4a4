@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:wght@400;500;600;700&display=swap");
@import "tailwindcss";
@plugin "@tailwindcss/typography";

@theme {
  /* Font Families */
  --font-sans: "SF Pro Display", system-ui, -apple-system, BlinkMacSystemFont,
    "Segoe UI", Roboto, Arial, sans-serif;
  --font-display: "Plus Jakarta Sans", "SF Pro Display", system-ui, sans-serif;
  --font-mono: "SF Mono", Monaco, Consolas, "Liberation Mono", "Courier New",
    monospace;

  /* Font Sizes */
  --text-sm: 0.8125rem; /* 13px */
  --text-base: 0.875rem; /* 14px */
  --text-lg: 0.9375rem; /* 15px */

  /* Spacing */
  --spacing-4_5: 1.125rem;
  --spacing-5_5: 1.375rem;

  /* Line Heights */
  --leading-snug: 1.375;
  --leading-relaxed: 1.625;

  /* Colors: Sidebar Light */
  --color-sidebar-light-bg: #efefef;
  --color-sidebar-light-hover: #e4e4e4;
  --color-sidebar-light-active: #dbdbdb;
  --color-sidebar-light-text: #101010;
  --color-sidebar-light-text-secondary: #4a4a4a;
  --color-sidebar-light-border: #cfcfcf;
  --color-sidebar-light-button-bg: #ffffff;
  --color-sidebar-light-button-hover: #ededed;
  --color-sidebar-light-scrollbar: #c7c7c7;
  --color-sidebar-light-scrollbar-hover: #adadad;

  /* Colors: Chat Light */
  --color-chat-light-bg: #fcfcfc;
  --color-chat-light-secondary-bg: #f3f3f3;
  --color-chat-light-input-bg: #e6e6e6;
  --color-chat-light-input-border: #c0c0c0;
  --color-chat-light-input-focus: #8c8c8c;
  --color-chat-light-button-hover: #dddddd;
  --color-chat-light-text: #131313;
  --color-chat-light-text-secondary: #4f4f4f;
  --color-chat-light-border: #c0c0c0;
  --color-chat-light-accent: #686868;
  --color-attachment-card-bg-light: #e4e4e4;

  /* Colors: Titlebar Light */
  --color-title-light-bg: var(--color-chat-light-bg);
  --color-title-light-button-hover: var(--color-chat-light-button-hover);
  --color-title-light-text: var(--color-chat-light-text);
  --color-title-light-text-secondary: var(--color-chat-light-text-secondary);
  --color-title-light-border: var(--color-chat-light-border);

  /* Colors: Runtime Logs Light */
  --color-runtime-light-card-bg: var(--color-chat-light-secondary-bg);
  --color-runtime-light-stream-bg: var(--color-chat-light-input-bg);

  /* -------------------------------------- */
  /* -------------------------------------- */

  /* Colors: Sidebar Dark */
  --color-sidebar-dark-bg: #151515;
  --color-sidebar-dark-hover: #242424;
  --color-sidebar-dark-active: #242424;
  --color-sidebar-dark-text: #e6e6e6;
  --color-sidebar-dark-text-secondary: #8e8e8e;
  --color-sidebar-dark-border: #242424;
  --color-sidebar-dark-button-bg: #242424;
  --color-sidebar-dark-button-hover: #303030;
  --color-sidebar-dark-scrollbar: #242424;
  --color-sidebar-dark-scrollbar-hover: #3c3c3c;

  /* Colors: Chat Dark */
  --color-chat-dark-bg: #1f1f1f;
  --color-chat-dark-secondary-bg: #2e2e2e;
  --color-chat-dark-input-bg: #272727;
  --color-chat-dark-input-border: #404040;
  --color-chat-dark-input-focus: #151515;
  --color-chat-dark-button-hover: #404040;
  --color-chat-dark-text: #f3f4f6;
  --color-chat-dark-text-secondary: #9ca3af;
  --color-chat-dark-border: #404040;
  --color-chat-dark-accent: #151515;
  --color-attachment-card-bg-dark: #323232;

  /* Colors: Titlebar Dark */
  --color-title-dark-bg: var(--color-chat-dark-bg);
  --color-title-dark-button-hover: var(--color-chat-dark-button-hover);
  --color-title-dark-text: var(--color-chat-dark-text);
  --color-title-dark-text-secondary: var(--color-chat-dark-text-secondary);
  --color-title-dark-border: var(--color-chat-dark-border);

  /* Colors: Runtime Logs Dark */
  --color-runtime-dark-card-bg: var(--color-chat-dark-secondary-bg);
  --color-runtime-dark-stream-bg: var(--color-chat-dark-input-bg);

  /* Attachment icon backgrounds by extension */
  --color-attachment-icon-pdf: #ef4444; /* red-500 */
  --color-attachment-icon-doc: #3b82f6; /* blue-500 */
  --color-attachment-icon-csv: #10b981; /* green-500 */
  --color-attachment-icon-jpg: #8b5cf6; /* violet-500 */
  --color-attachment-icon-png: #8b5cf6; /* violet-500 */
  --color-attachment-icon-default: var(--color-chat-light-text-secondary);
}

@layer base {
  html {
    font-family: var(--font-sans);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    min-height: 100%;
    overflow-x: hidden;
    overflow-y: auto;
  }

  body {
    min-height: 100%;
    overflow-x: hidden;
    overflow-y: auto;
  }

  /* Webkit browsers */
  ::-webkit-scrollbar {
    width: 6px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  .dark ::-webkit-scrollbar-thumb {
    background-color: var(--color-sidebar-dark-scrollbar);
    border-radius: 9999px;
  }

  .dark ::-webkit-scrollbar-thumb:hover {
    background-color: var(--color-sidebar-dark-scrollbar-hover);
  }

  ::-webkit-scrollbar-thumb {
    background-color: var(--color-sidebar-light-scrollbar);
    border-radius: 9999px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: var(--color-sidebar-light-scrollbar-hover);
  }

  /* Firefox */
  * {
    scrollbar-width: thin;
    scrollbar-color: var(--scrollbar-thumb) transparent;
  }

  :root {
    --scrollbar-thumb: var(--color-sidebar-light-scrollbar);
  }

  .dark {
    --scrollbar-thumb: var(--color-sidebar-dark-scrollbar);
  }
}

@layer utilities {
  /* Custom font utility */
  .font-inter {
    font-family: "Inter", sans-serif;
  }
  /* Always reserve space for the scrollbar to avoid layout shift */
  .attachment-scroll {
    scrollbar-gutter: stable;
  }

  /* Hide thumb by default, show on hover of container */
  .attachment-scroll::-webkit-scrollbar-thumb {
    opacity: 0;
    transition: opacity 0.2s;
  }
  .attachment-scroll:hover::-webkit-scrollbar-thumb {
    opacity: 1;
  }

  /* For Firefox */
  .attachment-scroll {
    scrollbar-width: thin;
    scrollbar-color: var(--scrollbar-thumb) transparent;
  }
  .attachment-scroll:hover {
    scrollbar-color: var(--scrollbar-thumb) transparent;
  }

  /* Dark‐mode gutter thumb color is already set by your existing .dark rules */
}
