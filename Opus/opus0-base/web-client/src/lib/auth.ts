export function getGoogleOAuthUrl(): string {
  const clientId = import.meta.env.VITE_GOOGLE_CLIENT_ID;
  const redirect =
    import.meta.env.VITE_GOOGLE_REDIRECT_URI || `${window.location.origin}/auth`;
  const scope = encodeURIComponent("openid profile email");
  return (
    `https://accounts.google.com/o/oauth2/v2/auth?client_id=${clientId}` +
    `&redirect_uri=${encodeURIComponent(redirect)}` +
    `&response_type=token&scope=${scope}`
  );
}
