// src/lib/chat/transform.ts

import type { <PERSON><PERSON>, ChatSummary } from "./types";
import type { ChatMessage } from "../../components/chat/Messages/MessageList";
import type { RuntimeLog } from "./types";
import type { Attachment } from "../../components/chat/ChatInput/types";

function toRuntimeLog(obj: unknown): RuntimeLog {
  const o = obj as Record<string, unknown>;
  return {
    id: (o.id as string | undefined) ?? "",
    subtaskId: (o.subtaskId as string | undefined) ?? "",
    description: (o.description as string | undefined) ?? "",
    stream: (o.stream as string | undefined) ?? "",
    status: (o.status as RuntimeLog["status"] | undefined) ?? "running",
    tsStart: new Date(o.tsStart as string | number | Date),
    tsEnd: o.tsEnd ? new Date(o.tsEnd as string | number | Date) : undefined,
  };
}

function toMessage(obj: unknown): ChatMessage {
  const o = obj as Record<string, unknown>;
  return {
    id: o.id as string,
    content: (o.content as string | undefined) ?? "",
    attachments: ((o.attachments as unknown[]) ?? []).map((a) => {
      const att = a as Partial<Attachment>;
      return {
        name: att.name ?? "",
        size: att.size ?? "",
        url: att.url ?? "",
      } as Attachment;
    }),
    isUser: Boolean(o.isUser),
    feedback: (o.feedback as ChatMessage["feedback"] | undefined) ?? undefined,
    timestamp: new Date(o.timestamp as string | number | Date),
    runtimeLogs: o.runtimeLogs
      ? (o.runtimeLogs as unknown[]).map(toRuntimeLog)
      : undefined,
  };
}

export function toChat(data: unknown): Chat {
  const d = data as Record<string, unknown>;
  return {
    id: d.id as Chat["id"],
    title: (d.title as string | undefined) ?? "",
    messages: ((d.messages as unknown[]) ?? []).map(toMessage),
    runtime: [],
    createdAt: new Date(d.createdAt as string | number | Date),
    updatedAt: new Date(d.updatedAt as string | number | Date),
    lastMessageAt: d.lastMessageAt
      ? new Date(d.lastMessageAt as string | number | Date)
      : null,
    isFavorite: (d.isFavorite as boolean | undefined) ?? false,
    isTask: (d.isTask as boolean | undefined) ?? false,
  };
}

export function toSummary(data: unknown): ChatSummary {
  const d = data as Record<string, unknown>;
  return {
    id: d.id as ChatSummary["id"],
    title: (d.title as string | undefined) ?? "",
    preview: (d.preview as string | undefined) ?? "",
    isFavorite: (d.isFavorite as boolean | undefined) ?? false,
  };
}
