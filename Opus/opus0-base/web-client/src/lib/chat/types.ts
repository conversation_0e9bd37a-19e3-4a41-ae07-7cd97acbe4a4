// src/lib/chat/types.ts

import { ChatMessage } from "../../components/chat/Messages/MessageList";
import type { Attachment } from "../../components/chat/ChatInput/types";

/* ---------- NEW ---------- */
export interface RuntimeLog {
  /** Synthetic id so React can key efficiently (subtaskId + "_rt") */
  id: string;
  subtaskId: string;
  description: string;
  /** Realtime streamed chunks for this subtask */
  stream: string;
  status: "running" | "done";
  /** When manager emitted subtask_started */
  tsStart: Date;
  /** When manager emitted subtask_done (undefined until then) */
  tsEnd?: Date;
}

export type ChatId = `chat_${string}`;
export type MessageId = `msg_${string}`;

export interface Chat {
  id: ChatId;
  title: string;
  messages: ChatMessage[];
  runtime: RuntimeLog[]; // ← NEW: list of live‑update cards
  createdAt: Date;
  updatedAt: Date;
  lastMessageAt: Date | null;
  isFavorite: boolean;
  isTask?: boolean;
}

export interface ChatSummary {
  id: ChatId;
  title: string;
  preview: string;
  isFavorite: boolean;
}

export type MessageType = "user" | "ai";

export interface MessageLog {
  id: MessageId;
  type: "user" | "ai";
  content: string;
  attachments?: Attachment[];
  feedback?: "up" | "down" | null;
  timestamp: Date;
  chatId: ChatId;
  /** Runtime logs produced while generating this message */
  runtimeLogs?: RuntimeLog[];
  messageMode?: "chat" | "command"; // can expand later
}

export interface ChatUpdate {
  title?: string;
  isFavorite?: boolean;
  isTask?: boolean;
  preview?: string;
}
