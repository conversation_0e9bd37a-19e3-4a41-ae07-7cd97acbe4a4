import { ChatId, MessageId, MessageLog } from './types';

/**
 * Generates a unique chat ID with timestamp and random string
 */
export function generateChatId(): ChatId {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 11);
  return `chat_${timestamp}_${random}`;
}

/**
 * Generates a unique message ID with timestamp and random string
 */
export function generateMessageId(): MessageId {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 11);
  return `msg_${timestamp}_${random}`;
}

/**
 * Formats a chat timestamp for display
 */
export function formatChatTimestamp(date: Date): string {
  return new Intl.DateTimeFormat('en-US', {
    hour: 'numeric',
    minute: 'numeric',
    hour12: true,
  }).format(date);
}

/**
 * Formats a date for chat grouping
 */
export function formatChatGroupDate(date: Date): string {
  const now = new Date();
  const yesterday = new Date(now);
  yesterday.setDate(yesterday.getDate() - 1);
  
  if (date.toDateString() === now.toDateString()) {
    return 'Today';
  } else if (date.toDateString() === yesterday.toDateString()) {
    return 'Yesterday';
  } else {
    return new Intl.DateTimeFormat('en-US', {
      month: 'long',
      day: 'numeric',
      year: 'numeric'
    }).format(date);
  }
}

/**
 * Logs chat messages to console with formatting
 */
export function logChatMessage(messageLog: MessageLog): void {
  const timestamp = formatChatTimestamp(messageLog.timestamp);
  console.log(
    `[${timestamp}] Chat ${messageLog.chatId}\n` +
    `Message ID: ${messageLog.id}\n` +
    `Type: ${messageLog.type}\n` +
    `Content: ${messageLog.content}\n` +
    '----------------------------------------'
  );
}

/**
 * Validates a chat ID format
 */
export function isValidChatId(id: string): id is ChatId {
  return /^chat_\d+_[a-z0-9]+$/.test(id);
}

/**
 * Validates a message ID format
 */
export function isValidMessageId(id: string): id is MessageId {
  return /^msg_\d+_[a-z0-9]+$/.test(id);
}

export function getFirstWords(text: string, count: number): string {
  return text.split(/\s+/).slice(0, count).join(" ");
}