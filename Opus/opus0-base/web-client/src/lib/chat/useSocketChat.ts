// src/lib/chat/useSocketChat.ts
import { useEffect, useCallback } from "react";
import { socket } from "../socket";
import { useChatStore } from "./store";
import { generateMessageId } from "./utils";
import type { ChatId, MessageId } from "./types";

let listenerAttached = false;

/** utility – detect & strip the LOG envelope */
function parseRuntimeEnvelope(raw: string) {
  const startTag = "[LOG_START]";
  const endTag = "[LOG_END]";
  if (!raw.startsWith(startTag) || !raw.endsWith(endTag)) return null;
  try {
    return JSON.parse(raw.slice(startTag.length, raw.length - endTag.length));
  } catch {
    console.warn("Failed to parse runtime envelope:", raw);
    return null;
  }
}

export function useSocketChat() {
  // We no longer read currentChatId here
  const currentMessageMode = useChatStore((s) => s.currentMessageMode);
  const addMessage = useChatStore((s) => s.addMessage);
  const updateLastAiMessage = useChatStore((s) => s.updateLastAiMessage);
  const addRuntimeLog = useChatStore((s) => s.addRuntimeLog);
  const appendRuntimeStream = useChatStore((s) => s.appendRuntimeStream);
  const finishRuntimeLog = useChatStore((s) => s.finishRuntimeLog);
  const bumpLastAiTimestamp = useChatStore((s) => s.bumpLastAiTimestamp);
  const archiveRuntimeLogs = useChatStore((s) => s.archiveRuntimeLogs);
  const updateSummary = useChatStore((s) => s.updateSummary);

  // Processing state per chat
  useChatStore((s) => s.processingMap);
  const setProcessing = useChatStore((s) => s.setProcessing);
  // ← NEW: planning state to control "Planning..." placeholder
  useChatStore((s) => s.planningMap);
  const setPlanningForChat = useChatStore((s) => s.setPlanningForChat);

  useEffect(() => {
    if (listenerAttached) return;

    const handleChatResponse = (payload: { chatId: ChatId; data: string }) => {
      const state = useChatStore.getState();
      const chatId = payload.chatId;
      const data = payload.data;
      if (!chatId) return;

      // 1) Handle the control frames FIRST
      if (data === "[START]") {
        // → mark "in progress"
        setProcessing(chatId, true);
        addMessage(chatId, {
          type: "ai",
          content: "",
          timestamp: new Date(),
          chatId,
        });
        updateSummary(chatId, { preview: "In progress..." });
        return;
      }
      if (data === "[END]") {
        // → done processing
        setProcessing(chatId, false);
        // nudge the AI bubble so it stays after the logs
        bumpLastAiTimestamp(chatId, new Date());

        const chat = state.chats.find((c) => c.id === chatId)!;
        const runtimeLogs = chat.runtime;
        const lastAi = chat.messages.filter((m) => !m.isUser).slice(-1)[0];
        const lastUser = [...chat.messages].reverse().find((m) => m.isUser)!;

        socket.emit("store_ai_message", {
          chatId,
          messageId: lastAi.id,
          timestamp: new Date(),
          reasoningTitle: "Inference reasoning done by the swarm of agents",
          runtimeLogs,
          content: lastAi.content,
        });
        archiveRuntimeLogs(chatId, {
          userMessageId: lastUser.id as MessageId,
          logs: runtimeLogs,
        });
        setPlanningForChat(chatId, null);
        const finalize = useChatStore.getState().finalizeSummary;
        finalize(chatId, lastAi.content);
        return;
      }

      // 2) Runtime logs
      const envelope = parseRuntimeEnvelope(data);
      if (envelope) {
        console.log("runtime envelope", envelope);
        const ts = new Date(envelope.ts);
        // receiving any runtime log means planning has finished
        setPlanningForChat(chatId, null);

        if (envelope.event === "subtask_started") {
          // force creation of a fresh card
          addRuntimeLog(chatId, {
            subtaskId: envelope.subtask_id,
            description: envelope.subtask_description,
            ts,
            isStart: true, // ← new flag
          });
        } else if (envelope.event === "subtask_progress") {
          // append to the *most recent* card for that subtask
          addRuntimeLog(chatId, {
            subtaskId: envelope.subtask_id,
            description: envelope.subtask_description,
            ts,
            isStart: false, // ← new flag
          });
        } else if (envelope.event === "subtask_stream") {
          const raw = envelope.subtask_description as string;
          const match = raw.match(
            /^<runtimeStream>([\s\S]*)<\/runtimeStream>$/
          );
          const chunk = match ? match[1] : raw;
          console.log("stream chunk", {
            subtaskId: envelope.subtask_id,
            chunk,
          });
          if (chunk !== "stream") {
            appendRuntimeStream(chatId, {
              subtaskId: envelope.subtask_id,
              chunk,
            });
          }
        } else if (envelope.event === "subtask_done") {
          finishRuntimeLog(chatId, {
            subtaskId: envelope.subtask_id,
            ts,
          });
        }

        return;
      }

      // 3) AI streaming chunk
      if (state.planningMap[chatId]) {
        setPlanningForChat(chatId, null);
      }
      updateLastAiMessage(chatId, data);
    };

    socket.on("chat_response", handleChatResponse);
    listenerAttached = true;
    return () => {
      socket.off("chat_response", handleChatResponse);
      listenerAttached = false;
    };
  }, [
    addMessage,
    updateLastAiMessage,
    addRuntimeLog,
    appendRuntimeStream,
    finishRuntimeLog,
    bumpLastAiTimestamp,
    archiveRuntimeLogs,
    updateSummary,
    setProcessing,
    setPlanningForChat,
  ]);

  /**
   * Emits a chat_message frame with an explicit chatId.
   */
  const sendMessage = useCallback(
    (
      chatId: ChatId,
      message: string,
      attachments: { key: string; name: string; size: string }[] = [],
      model: string
    ) => {
      const messageId = generateMessageId();
      socket.emit("chat_message", {
        chatId,
        messageId,
        content: message,
        messageMode: currentMessageMode,
        attachments, // now full metadata
        model,
        userId: localStorage.getItem("user_id"),
      });
    },
    [currentMessageMode]
  );

  const sendFeedback = useCallback(
    (chatId: ChatId, messageId: MessageId, value: "up" | "down") => {
      socket.emit("feedback", {
        chatId,
        messageId,
        value,
        userId: localStorage.getItem("user_id"),
      });
    },
    []
  );
  return { sendMessage, sendFeedback };
}
