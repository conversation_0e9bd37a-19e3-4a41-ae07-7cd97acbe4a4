// src/lib/chat/api.ts

import { toChat, toSummary } from "./transform";
import type { Chat, ChatSummary } from "./types";

export async function fetchChat(id: string): Promise<Chat> {
  const uid = localStorage.getItem("user_id") || "";
  const resp = await fetch(`/chats/${id}?user_id=${uid}`);
  if (!resp.ok) throw new Error("Chat not found");
  const data = await resp.json();
  return toChat(data);
}

export async function fetchSummaries(): Promise<ChatSummary[]> {
  const uid = localStorage.getItem("user_id") || "";
  const resp = await fetch(`/chats?user_id=${uid}`);
  if (!resp.ok) throw new Error("Failed to fetch chats");
  const data = await resp.json();
  return data.map(toSummary);
}

export async function deleteChatRecord(id: string): Promise<void> {
  const uid = localStorage.getItem("user_id") || "";
  const resp = await fetch(`/chats/${id}?user_id=${uid}`, { method: "DELETE" });
  if (!resp.ok) throw new Error("Failed to delete chat");
}
