// src/lib/chat/store.ts

import { create } from "zustand";
import { googleLogout } from "@react-oauth/google";
import {
  Chat,
  ChatId,
  MessageLog,
  ChatUpdate,
  RuntimeLog,
  MessageId,
  ChatSummary,
} from "./types";
import { generateChatId, generateMessageId, getFirstWords } from "./utils";
import { logChatCreation, logChatMessage, logChatChange } from "./logger";
import { socket } from "../socket";

/* ---------- NEW helper to fabricate runtime id ---------- */
function runtimeId(subtaskId: string) {
  return `rt_${subtaskId}` as const;
}

interface ChatStore {
  chats: Chat[];
  summaries: ChatSummary[];
  currentChatId: ChatId | null;
  pendingChatId: ChatId | null;
  currentMessageMode: "chat" | "command";

  createChat: () => ChatId;
  updateChat: (chatId: ChatId, updates: ChatUpdate) => void;
  deleteChat: (chatId: ChatId) => void;
  setCurrentChat: (chatId: ChatId | null) => void;
  addMessage: (chatId: ChatId, message: Omit<MessageLog, "id">) => MessageId;
  confirmPendingChat: (chatId: ChatId) => void;
  updateLastAiMessage: (chatId: ChatId, chunk: string) => void;
  setCurrentMessageMode: (mode: "chat" | "command") => void;
  initSummary: (chatId: ChatId, text: string) => void;
  finalizeSummary: (chatId: ChatId, aiText: string) => void;
  updateSummary: (
    chatId: ChatId,
    updates: Partial<Pick<ChatSummary, "title" | "isFavorite" | "preview">>,
  ) => void;
  setSummaries: (list: ChatSummary[]) => void;
  hydrateChat: (chat: Chat) => void;

  /* ---------- NEW actions ---------- */
  addRuntimeLog: (
    chatId: ChatId,
    payload: {
      subtaskId: string;
      description: string;
      ts: Date;
      isStart: boolean;
    },
  ) => void;
  appendRuntimeStream: (
    chatId: ChatId,
    payload: { subtaskId: string; chunk: string },
  ) => void;
  finishRuntimeLog: (
    chatId: ChatId,
    payload: { subtaskId: string; ts: Date },
  ) => void;
  archiveRuntimeLogs: (
    chatId: ChatId,
    payload: { userMessageId: MessageId; logs: RuntimeLog[] },
  ) => void;
  /** Remove all runtime logs for the given chat */
  clearRuntimeLogs: (chatId: ChatId) => void;
  bumpLastAiTimestamp: (chatId: ChatId, ts: Date) => void;
  setFeedback: (
    chatId: ChatId,
    messageId: MessageId,
    value: "up" | "down",
  ) => void;

  /** Draft text per chat */
  inputMap: Record<ChatId, string>;
  setInput: (chatId: ChatId, value: string) => void;

  /** Per chat processing state */
  processingMap: Record<ChatId, boolean>;
  setProcessing: (chatId: ChatId, flag: boolean) => void;

  // Track which message is currently waiting for runtime logs per chat
  planningMap: Record<ChatId, MessageId | null>;
  setPlanningForChat: (chatId: ChatId, messageId: MessageId | null) => void;
}

interface AuthStore {
  isLoggedIn: boolean;
  userName: string | null;
  userPicture: string | null;
  userEmail: string | null;
  login: (token: string) => Promise<boolean>;
  logout: () => void;
}

export const useAuthStore = create<AuthStore>((set) => ({
  isLoggedIn: Boolean(localStorage.getItem("auth_token")),
  userName: localStorage.getItem("user_name"),
  userPicture: localStorage.getItem("user_picture"),
  userEmail: localStorage.getItem("user_email"),

  login: async (token: string) => {
    const resp = await fetch(`/auth/google`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ token }),
    });
    if (resp.ok) {
      const data = await resp.json();
      localStorage.setItem("auth_token", "1");
      if (data.user_id) {
        localStorage.setItem("user_id", data.user_id);
      }
      if (data.name) {
        localStorage.setItem("user_name", data.name);
      }
      if (data.picture) {
        localStorage.setItem("user_picture", data.picture);
      }
      if (data.email) {
        localStorage.setItem("user_email", data.email);
      }
      set({
        isLoggedIn: true,
        userName: data.name ?? null,
        userPicture: data.picture ?? null,
        userEmail: data.email ?? null,
      });
      return true;
    }
    return false;
  },

  logout: () => {
    googleLogout();
    localStorage.removeItem("auth_token");
    localStorage.removeItem("user_id");
    localStorage.removeItem("user_name");
    localStorage.removeItem("user_picture");
    localStorage.removeItem("user_email");
    set({ isLoggedIn: false, userName: null, userPicture: null, userEmail: null });
  },
}));

export const useChatStore = create<ChatStore>((set) => {
  const savedMode = localStorage.getItem("message_mode");
  return {
    chats: [],
    summaries: [],
    currentChatId: null,
    pendingChatId: null,

    // ➊ Load last mode from storage or default to "command"
    currentMessageMode: savedMode === "chat" ? "chat" : "command",

    // Per chat input and processing state
    inputMap: {},
    setInput: (chatId, value) =>
      set((state) => ({
        inputMap: { ...state.inputMap, [chatId]: value },
      })),

    processingMap: {},
    setProcessing: (chatId, flag) =>
      set((state) => ({
        processingMap: { ...state.processingMap, [chatId]: flag },
      })),

    // Track per-chat message waiting for logs
    planningMap: {},
    setPlanningForChat: (chatId: ChatId, messageId: MessageId | null) =>
      set((state) => ({
        planningMap: { ...state.planningMap, [chatId]: messageId },
      })),

    initSummary: (chatId, text) =>
      set((state) => {
        if (state.summaries.some((s) => s.id === chatId)) return state;
        const summary: ChatSummary = {
          id: chatId,
          title: getFirstWords(text, 10),
          preview: "",
          isFavorite: false,
        };
        return { summaries: [summary, ...state.summaries] };
      }),

    finalizeSummary: (chatId, aiText) => {
      const preview = getFirstWords(aiText, 10);
      set((state) => ({
        summaries: state.summaries.map((s) =>
          s.id === chatId ? { ...s, preview } : s,
        ),
      }));
      socket.emit("chat_update", { chatId, updates: { preview } });
    },

    updateSummary: (chatId, updates) => {
      set((state) => ({
        summaries: state.summaries.map((s) =>
          s.id === chatId ? { ...s, ...updates } : s,
        ),
      }));
      const serverUpdates: Record<string, unknown> = { ...updates };
      if (Object.prototype.hasOwnProperty.call(updates, "isFavorite")) {
        serverUpdates["is_favorite"] = updates.isFavorite;
        delete (serverUpdates as Record<string, unknown>)["isFavorite"];
      }
      socket.emit("chat_update", { chatId, updates: serverUpdates });
    },

    setSummaries: (list) => {
      set(() => ({ summaries: list }));
    },

    hydrateChat: (chat) => {
      set((state) => {
        const chats = state.chats.some((c) => c.id === chat.id)
          ? state.chats.map((c) => (c.id === chat.id ? chat : c))
          : [chat, ...state.chats];
        const summaries = state.summaries.some((s) => s.id === chat.id)
          ? state.summaries.map((s) =>
              s.id === chat.id
                ? { ...s, title: chat.title, isFavorite: chat.isFavorite }
                : s,
            )
          : [
              {
                id: chat.id,
                title: chat.title,
                preview: "",
                isFavorite: chat.isFavorite,
              },
              ...state.summaries,
            ];
        return { chats, summaries };
      });
    },

    createChat: () => {
      const newChatId = generateChatId();
      const now = new Date();

      // Log chat creation
      logChatCreation(newChatId, now);

      // Set as pending chat but don't add to chats list yet
      set({
        currentChatId: newChatId,
        pendingChatId: newChatId,
      });

      return newChatId;
    },

    confirmPendingChat: (chatId) => {
      set((state) => {
        // Only add to chats if it's the pending chat
        if (state.pendingChatId === chatId) {
          const now = new Date();
          const newChat: Chat = {
            id: chatId,
            title: "Untitled Chat",
            messages: [],
            runtime: [],
            createdAt: now,
            updatedAt: now,
            lastMessageAt: null,
            isFavorite: false,
          };

          return {
            chats: [newChat, ...state.chats],
            pendingChatId: null,
          };
        }
        return state;
      });
    },

    updateChat: (chatId, updates) => {
      set((state) => ({
        chats: state.chats.map((chat) =>
          chat.id === chatId
            ? { ...chat, ...updates, updatedAt: new Date() }
            : chat,
        ),
        summaries: state.summaries.map((s) =>
          s.id === chatId ? { ...s, ...updates } : s,
        ),
      }));
      const serverUpdates: Record<string, unknown> = { ...updates };
      if (Object.prototype.hasOwnProperty.call(updates, "isFavorite")) {
        serverUpdates["is_favorite"] = updates.isFavorite;
        delete (serverUpdates as Record<string, unknown>)["isFavorite"];
      }

      socket.emit("chat_update", { chatId, updates: serverUpdates });
    },

    deleteChat: (chatId) => {
      set((state) => ({
        chats: state.chats.filter((chat) => chat.id !== chatId),
        summaries: state.summaries.filter((s) => s.id !== chatId),
        currentChatId:
          state.currentChatId === chatId ? null : state.currentChatId,
        pendingChatId:
          state.pendingChatId === chatId ? null : state.pendingChatId,
      }));
    },

    setCurrentChat: (chatId) => {
      // Log chat change
      logChatChange(chatId, new Date());
      set({ currentChatId: chatId });
    },

    addMessage: (chatId, message) => {
      const messageId = generateMessageId();
      const now = new Date();

      // Log the message
      logChatMessage({ ...message, id: messageId });

      set((state) => {
        // If this is a pending chat, confirm it first
        if (state.pendingChatId === chatId) {
          const newChat: Chat = {
            id: chatId,
            title: getFirstWords(message.content, 10),
            messages: [],
            runtime: [],
            createdAt: now,
            updatedAt: now,
            lastMessageAt: now,
            isFavorite: false,
          };

          // Add the chat and clear pending state
          return {
            pendingChatId: null,
            chats: [
              {
                ...newChat,
                messages: [
                  {
                    id: messageId,
                    content: message.content,
                    attachments: message.attachments,
                    isUser: message.type === "user",
                    feedback: message.type === "ai" ? null : undefined,
                    timestamp: message.timestamp,
                  },
                ],
              },
              ...state.chats,
            ],
            summaries: [
              {
                id: chatId,
                title: getFirstWords(message.content, 10),
                preview: "",
                isFavorite: false,
              },
              ...state.summaries,
            ],
          };
        }

        // Otherwise just add the message to existing chat
        const summaries = state.summaries.some((s) => s.id === chatId)
          ? state.summaries
          : [
              {
                id: chatId,
                title: getFirstWords(message.content, 10),
                preview: "",
                isFavorite: false,
              },
              ...state.summaries,
            ];

        return {
          chats: state.chats.map((chat) =>
            chat.id === chatId
              ? {
                  ...chat,
                  messages: [
                    ...chat.messages,
                    {
                      id: messageId,
                      content: message.content,
                      attachments: message.attachments,
                      isUser: message.type === "user",
                      feedback: message.type === "ai" ? null : undefined,
                      timestamp: message.timestamp,
                    },
                  ],
                  updatedAt: now,
                  lastMessageAt: now,
                }
              : chat,
          ),
          summaries,
        };
      });
      return messageId;
    },

    updateLastAiMessage: (chatId, chunk) => {
      set((state) => {
        // Loop through chats to find the correct one
        const updatedChats = state.chats.map((chat) => {
          if (chat.id !== chatId) return chat;
          if (chat.messages.length === 0) return chat; // no messages yet

          const lastIndex = chat.messages.length - 1;
          const lastMessage = chat.messages[lastIndex];

          // Only append if last message is AI
          if (!lastMessage.isUser) {
            const updatedContent = lastMessage.content + chunk;
            const updatedMessage = {
              ...lastMessage,
              content: updatedContent,
            };

            const newMessages = [...chat.messages];
            newMessages[lastIndex] = updatedMessage;

            return {
              ...chat,
              messages: newMessages,
            };
          }
          return chat;
        });

        return { chats: updatedChats };
      });
    },

    // ➋ The setter that updates currentMessageMode
    setCurrentMessageMode: (mode) => {
      localStorage.setItem("message_mode", mode);
      set(() => ({ currentMessageMode: mode }));
    },

    /* ---------- NEW: add runtime‑card ---------- */
    addRuntimeLog: (
      chatId,
      {
        subtaskId,
        description,
        ts,
        isStart, // ← new
      }: { subtaskId: string; description: string; ts: Date; isStart: boolean },
    ) => {
      console.log("addRuntimeLog", { chatId, subtaskId, description, isStart });
      set((state) => ({
        chats: state.chats.map((chat) => {
          if (chat.id !== chatId) return chat;

          const runtime = chat.runtime ?? [];

          if (isStart) {
            //  ➔ always push a brand-new card on "start"
            const newEntry: RuntimeLog = {
              id: runtimeId(subtaskId) + "__" + ts.getTime(), // add timestamp to keep it unique
              subtaskId,
              description,
              stream: "",
              status: "running",
              tsStart: ts,
            };
            return { ...chat, runtime: [...runtime, newEntry] };
          }

          // otherwise (progress) find the *most recent* card for that subtask
          const idx = runtime
            .map((rt) => rt.subtaskId === subtaskId)
            .lastIndexOf(true);

          if (idx >= 0) {
            const updated = [...runtime];
            const rt = updated[idx];
            updated[idx] = {
              ...rt,
              description: rt.description + "\n" + description,
            };
            return { ...chat, runtime: updated };
          }

          // fallback: if for some reason there's no card yet, act like a start
          const fallback: RuntimeLog = {
            id: runtimeId(subtaskId) + "__" + ts.getTime(),
            subtaskId,
            description,
            stream: "",
            status: "running",
            tsStart: ts,
          };
          return { ...chat, runtime: [...runtime, fallback] };
        }),
      }));
    },

    appendRuntimeStream: (chatId, { subtaskId, chunk }) => {
      console.log("appendRuntimeStream", { chatId, subtaskId, chunk });
      set((state) => ({
        chats: state.chats.map((chat) => {
          if (chat.id !== chatId) return chat;

          const runtime = chat.runtime ?? [];
          const idx = runtime
            .map((rt) => rt.subtaskId === subtaskId)
            .lastIndexOf(true);
          if (idx >= 0) {
            const updated = [...runtime];
            const rt = updated[idx];
            updated[idx] = { ...rt, stream: rt.stream + chunk };
            return { ...chat, runtime: updated };
          }
          return chat;
        }),
      }));
    },

    /* ---------- NEW: mark runtime‑card as done ---------- */
    finishRuntimeLog: (chatId, { subtaskId, ts }) => {
      console.log("finishRuntimeLog", { chatId, subtaskId });
      set((state) => ({
        chats: state.chats.map((chat) => {
          if (chat.id !== chatId) return chat;

          // --- make the type explicit so inference is correct ---
          const updatedRuntime: RuntimeLog[] = (chat.runtime ?? []).map((rt) =>
            rt.subtaskId === subtaskId
              ? { ...rt, status: "done", tsEnd: ts }
              : rt,
          );

          return { ...chat, runtime: updatedRuntime };
        }),
      }));
    },

    archiveRuntimeLogs: (chatId, { userMessageId, logs }) => {
      set((state) => ({
        chats: state.chats.map((chat) => {
          if (chat.id !== chatId) return chat;
          const updatedMessages = chat.messages.map((m) =>
            m.id === userMessageId ? { ...m, runtimeLogs: logs } : m,
          );
          return { ...chat, messages: updatedMessages, runtime: [] };
        }),
      }));
    },

    clearRuntimeLogs: (chatId) => {
      set((state) => ({
        chats: state.chats.map((chat) =>
          chat.id === chatId ? { ...chat, runtime: [] } : chat,
        ),
      }));
    },

    /* ---------- NEW: bump AI bubble timestamp to keep it below the logs ---------- */
    bumpLastAiTimestamp: (chatId, ts) => {
      set((state) => ({
        chats: state.chats.map((chat) => {
          if (chat.id !== chatId) return chat;
          // Walk through messages, only shift the last AI one
          const msgs = chat.messages.map((m, i, arr) => {
            const isLastAi = i === arr.length - 1 && !m.isUser;
            return isLastAi ? { ...m, timestamp: ts } : m;
          });
          return { ...chat, messages: msgs };
        }),
      }));
    },

    setFeedback: (chatId, messageId, value) => {
      set((state) => ({
        chats: state.chats.map((chat) => {
          if (chat.id !== chatId) return chat;
          return {
            ...chat,
            messages: chat.messages.map((m) =>
              m.id === messageId ? { ...m, feedback: value } : m,
            ),
          };
        }),
      }));
    },
  };
});
