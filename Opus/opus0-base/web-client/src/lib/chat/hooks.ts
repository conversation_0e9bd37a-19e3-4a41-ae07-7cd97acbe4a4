import { useCallback } from 'react';
import { MessageLog } from './types';
import { generateChatId, logChatMessage } from './utils';

/**
 * Hook for managing chat messages
 * Implementation will be added later
 */
export function useChat() {
  const createNewChat = useCallback(() => {
    return generateChatId();
  }, []);

  const logMessage = useCallback((message: MessageLog) => {
    logChatMessage(message);
  }, []);

  return {
    createNewChat,
    logMessage,
  };
}