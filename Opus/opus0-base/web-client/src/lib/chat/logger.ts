import { ChatId, MessageLog } from './types';
import { formatChatTimestamp } from './utils';

/**
 * Formats a message for console output
 */
function formatMessage(message: string): string {
  // Split long messages for better readability
  if (message.length > 100) {
    return message.substring(0, 97) + '...';
  }
  return message;
}

/**
 * Logs chat creation
 */
export function logChatCreation(chatId: ChatId, timestamp: Date): void {
  const formattedTime = formatChatTimestamp(timestamp);
  console.log(
    '\n🆕 New Chat Created\n' +
    `Time: ${formattedTime}\n` +
    `Chat ID: ${chatId}\n` +
    '----------------------------------------'
  );
}

/**
 * Logs chat change
 */
export function logChatChange(chatId: ChatId | null, timestamp: Date): void {
  const formattedTime = formatChatTimestamp(timestamp);
  console.log(
    '\n🔄 Chat Changed\n' +
    `Time: ${formattedTime}\n` +
    `Chat ID: ${chatId || 'None (Welcome Screen)'}\n` +
    '----------------------------------------'
  );
}

/**
 * Logs user message
 */
export function logUserMessage(chatId: ChatId, message: string, timestamp: Date): void {
  const formattedTime = formatChatTimestamp(timestamp);
  console.log(
    '\n👤 User Message\n' +
    `Time: ${formattedTime}\n` +
    `Chat ID: ${chatId}\n` +
    `Message: ${formatMessage(message)}\n` +
    '----------------------------------------'
  );
}

/**
 * Logs AI message
 */
export function logAIMessage(chatId: ChatId, message: string, timestamp: Date): void {
  const formattedTime = formatChatTimestamp(timestamp);
  console.log(
    '\n🤖 AI Response\n' +
    `Time: ${formattedTime}\n` +
    `Chat ID: ${chatId}\n` +
    `Message: ${formatMessage(message)}\n` +
    '----------------------------------------'
  );
}

/**
 * Logs any chat message
 */
export function logChatMessage(messageLog: MessageLog): void {
  if (messageLog.type === 'user') {
    logUserMessage(messageLog.chatId, messageLog.content, messageLog.timestamp);
  } else {
    logAIMessage(messageLog.chatId, messageLog.content, messageLog.timestamp);
  }
}