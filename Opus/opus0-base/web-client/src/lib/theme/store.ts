import { create } from "zustand";

interface ThemeStore {
  isDark: boolean;
  toggleTheme: () => void;
}

export const useThemeStore = create<ThemeStore>((set) => {
  const isDark = (localStorage.getItem("is_dark") ?? "true") === "true";
  document.documentElement.classList.toggle("dark", isDark);

  return {
    isDark,
    toggleTheme: () =>
      set((state) => {
        const nextValue = !state.isDark;
        localStorage.setItem("is_dark", String(nextValue));
        document.documentElement.classList.toggle("dark", nextValue);
        return { isDark: nextValue };
      }),
  };
});
