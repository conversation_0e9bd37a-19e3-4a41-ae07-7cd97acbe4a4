import { useEffect } from "react";
import NavBar from "./NavBar/NavBar";
import Hero from "./Hero/Hero";
import ChatDemo from "./ChatDemo/ChatDemo";
import Features from "./Features/Features";
import AppFeatures from "./Features/AppFeatures";
import FAQ from "./FAQ/FAQ";
import Footer from "./Footer/Footer";

const Home = () => {
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);
  return (
    <div className="min-h-screen text-gray-900 relative font-inter">
      {/* Cerulean gradient background - Much lighter cerulean to white */}
      <div
        className="absolute inset-0 bg-gradient-to-b from-blue-200 via-blue-100 to-white"
        style={{
          background:
            "linear-gradient(180deg, rgb(224 242 254) 0%, rgb(240 249 255) 20%, rgb(248 252 255) 40%, rgb(252 254 255) 60%, rgb(255 255 255) 100%)",
        }}
      ></div>

      {/* Organic Cerulean Blobs - Responsive sizing */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Blob 1 - Top left, organic teardrop shape */}
        <div
          className="absolute opacity-25 blur-3xl"
          style={{
            top: "8%",
            left: "12%",
            width: "200px",
            height: "240px",
            background:
              "linear-gradient(135deg, rgb(186 230 253) 0%, rgb(125 211 252) 40%, rgb(56 189 248) 100%)",
            borderRadius: "60% 40% 30% 70% / 60% 30% 70% 40%",
            transform: "rotate(-15deg)",
            animation: "float 20s ease-in-out infinite",
          }}
        ></div>

        {/* Blob 2 - Top right, flowing organic shape */}
        <div
          className="absolute opacity-20 blur-2xl"
          style={{
            top: "15%",
            right: "8%",
            width: "180px",
            height: "210px",
            background:
              "linear-gradient(225deg, rgb(199 210 254) 0%, rgb(165 180 252) 50%, rgb(129 140 248) 100%)",
            borderRadius: "40% 60% 70% 30% / 40% 70% 30% 60%",
            transform: "rotate(25deg)",
            animation: "float 25s ease-in-out infinite reverse",
          }}
        ></div>

        {/* Blob 3 - Center, subtle accent blob */}
        <div
          className="absolute opacity-15 blur-3xl"
          style={{
            top: "35%",
            left: "45%",
            width: "150px",
            height: "180px",
            background:
              "linear-gradient(45deg, rgb(224 242 254) 0%, rgb(186 230 253) 60%, rgb(125 211 252) 100%)",
            borderRadius: "70% 30% 50% 50% / 30% 70% 50% 50%",
            transform: "rotate(-35deg)",
            animation: "float 30s ease-in-out infinite",
          }}
        ></div>

        {/* Desktop-only blobs */}
        <div className="hidden md:block">
          <div
            className="absolute opacity-25 blur-3xl"
            style={{
              top: "8%",
              left: "12%",
              width: "280px",
              height: "320px",
              background:
                "linear-gradient(135deg, rgb(186 230 253) 0%, rgb(125 211 252) 40%, rgb(56 189 248) 100%)",
              borderRadius: "60% 40% 30% 70% / 60% 30% 70% 40%",
              transform: "rotate(-15deg)",
              animation: "float 20s ease-in-out infinite",
            }}
          ></div>

          <div
            className="absolute opacity-20 blur-2xl"
            style={{
              top: "15%",
              right: "8%",
              width: "240px",
              height: "280px",
              background:
                "linear-gradient(225deg, rgb(199 210 254) 0%, rgb(165 180 252) 50%, rgb(129 140 248) 100%)",
              borderRadius: "40% 60% 70% 30% / 40% 70% 30% 60%",
              transform: "rotate(25deg)",
              animation: "float 25s ease-in-out infinite reverse",
            }}
          ></div>

          <div
            className="absolute opacity-15 blur-3xl"
            style={{
              top: "35%",
              left: "45%",
              width: "200px",
              height: "240px",
              background:
                "linear-gradient(45deg, rgb(224 242 254) 0%, rgb(186 230 253) 60%, rgb(125 211 252) 100%)",
              borderRadius: "70% 30% 50% 50% / 30% 70% 50% 50%",
              transform: "rotate(-35deg)",
              animation: "float 30s ease-in-out infinite",
            }}
          ></div>
        </div>
      </div>

      <div className="relative z-10">
        <NavBar />
        <Hero />
        <ChatDemo />
        <Features />
        <AppFeatures />
        <FAQ />
        <Footer />
      </div>

      <style>{`
          @keyframes float {
            0%, 100% {
              transform: translateY(0px) rotate(var(--rotation, 0deg));
            }
          33% {
            transform: translateY(-20px) rotate(calc(var(--rotation, 0deg) + 5deg));
          }
          66% {
            transform: translateY(10px) rotate(calc(var(--rotation, 0deg) - 3deg));
          }
        }
      `}</style>
    </div>
  );
};

export default Home;
