import { useState, useEffect, useRef } from "react";
import { X, Menu } from "lucide-react";
import { Link, useLocation } from "react-router-dom";
import { getGoogleOAuthUrl } from "../../../lib/auth";
import { motion, AnimatePresence } from "framer-motion";
import logoImage from "/images/logo.webp";

const NavBar = () => {
  const [isVisible, setIsVisible] = useState(true);
  const [isAtTop, setIsAtTop] = useState(true);
  const lastScrollYRef = useRef(0);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const location = useLocation();

  // Check if we're on the home page
  const isHomePage = location.pathname === "/home";

  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      const atTop = currentScrollY <= 10;
      setIsAtTop(atTop);

      if (!atTop) {
        if (currentScrollY > lastScrollYRef.current && currentScrollY > 100) {
          setIsVisible(false);
        } else if (currentScrollY < lastScrollYRef.current) {
          setIsVisible(true);
        }
      } else {
        setIsVisible(true);
      }

      lastScrollYRef.current = currentScrollY;
    };

    window.addEventListener("scroll", handleScroll, { passive: true });
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Close mobile menu when clicking outside or on scroll
  useEffect(() => {
    const handleClickOutside = () => {
      setIsMobileMenuOpen(false);
    };

    const handleScroll = () => {
      setIsMobileMenuOpen(false);
    };

    if (isMobileMenuOpen) {
      document.addEventListener("click", handleClickOutside);
      window.addEventListener("scroll", handleScroll);
    }

    return () => {
      document.removeEventListener("click", handleClickOutside);
      window.removeEventListener("scroll", handleScroll);
    };
  }, [isMobileMenuOpen]);

  const toggleMobileMenu = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const scrollToFeatures = () => {
    if (!isHomePage) {
      // If not on home page, navigate to home first then scroll to features
      window.location.href = "/home#features";
      return;
    }

    // Find the features section and scroll to it
    const featuresSection = document.getElementById("features");
    if (featuresSection) {
      featuresSection.scrollIntoView({ behavior: "smooth" });
    }
    setIsMobileMenuOpen(false);
  };

  // Determine background and border styles based on page and scroll position
  const getNavbarStyles = () => {
    if (isAtTop) {
      // Attached state: transparent background with no visible border or shadow
      return {
        background: "bg-transparent",
        border: "border-transparent",
        shadow: "shadow-none",
      };
    }

    // Detached state: floating navbar with opaque background and shadow
    return {
      background: "bg-white/95 backdrop-blur-xl",
      border: "border-gray-200/50",
      shadow: "shadow-2xl shadow-gray-900/20",
    };
  };

  const navbarStyles = getNavbarStyles();

  // Determine hover background for mobile toggle button
  const getToggleHoverStyle = () => {
    if (isAtTop) {
      // When at top on any page, use blueish tone
      return "hover:bg-blue-100/50";
    } else {
      // When scrolled, use grey
      return "hover:bg-gray-100/80";
    }
  };

  return (
    <>
      {/* Fixed spacer that maintains consistent spacing */}
      <div className="h-20" />

      <nav
        className={`
          fixed top-0 left-0 right-0 z-50 transition-all duration-300 ease-out
          ${
            isVisible
              ? "translate-y-0 opacity-100"
              : "-translate-y-full opacity-0"
          }
        `}
      >
        <div
          className={`flex justify-center px-4 ${isAtTop ? "pt-4" : "pt-4"}`}
        >
          <div
            className={`
              w-full max-w-[88rem] px-4 py-3 flex items-center justify-between relative
              transition-all duration-300 ease-out rounded-3xl border
              ${navbarStyles.background} ${navbarStyles.border} ${navbarStyles.shadow}
            `}
          >
            {/* Logo */}
            <Link
              to="/home"
              className="flex items-center space-x-3 flex-shrink-0"
            >
              <img
                src={logoImage}
                alt="Opus0 Logo"
                className="w-8 h-8 object-contain"
              />
              <span className="font-inter text-xl font-semibold text-gray-900">
                Opus0
              </span>
            </Link>

            {/* Navigation Links - Centered in the page (Desktop only) */}
            <div className="hidden md:flex items-center space-x-12 absolute left-1/2 transform -translate-x-1/2">
              <button
                onClick={scrollToFeatures}
                className="font-inter cursor-pointer text-gray-800 hover:text-gray-900 transition-colors duration-200 font-semibold text-sm whitespace-nowrap"
              >
                Why Opus0?
              </button>
              <Link
                to="/plans-pricing"
                className="font-inter text-gray-800 hover:text-gray-900 transition-colors duration-200 font-semibold text-sm whitespace-nowrap"
              >
                Plans & Pricing
              </Link>
              <Link
                to="/contact"
                className="font-inter text-gray-800 hover:text-gray-900 transition-colors duration-200 font-semibold text-sm whitespace-nowrap"
              >
                Contact
              </Link>
            </div>

            {/* Auth Buttons (Desktop only) */}
            <div className="hidden md:flex items-center space-x-3 flex-shrink-0">
              <a
                href={getGoogleOAuthUrl()}
                className="font-inter text-gray-800 hover:text-gray-900 px-4 py-2 rounded-2xl transition-all duration-200 font-semibold text-sm"
              >
                Login
              </a>
              <a
                href={getGoogleOAuthUrl()}
                className="font-inter bg-gray-900 hover:bg-gray-800 text-white px-5 py-2 rounded-2xl transition-colors duration-200 font-semibold text-sm border border-gray-900"
              >
                Get started
              </a>
            </div>

            {/* Mobile Menu Button */}
            <div className="md:hidden ml-3">
              <button
                onClick={toggleMobileMenu}
                className={`text-gray-700 hover:text-gray-900 transition-colors p-2.5 rounded-2xl ${getToggleHoverStyle()} relative`}
              >
                <AnimatePresence mode="wait">
                  {isMobileMenuOpen ? (
                    <motion.div
                      key="close"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      transition={{ duration: 0.15 }}
                    >
                      <X className="w-5 h-5" />
                    </motion.div>
                  ) : (
                    <motion.div
                      key="menu"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      transition={{ duration: 0.15 }}
                    >
                      <Menu className="w-5 h-5" />
                    </motion.div>
                  )}
                </AnimatePresence>
              </button>
            </div>
          </div>
        </div>

        {/* Mobile Menu Dropdown */}
        <AnimatePresence>
          {isMobileMenuOpen && (
            <motion.div
              className="md:hidden absolute top-full left-0 right-0 mt-2 px-4"
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{
                duration: 0.2,
                ease: [0.4, 0.0, 0.2, 1],
              }}
            >
              <motion.div
                className={`
                  w-full max-w-7xl mx-auto bg-white/95 backdrop-blur-xl border border-gray-200/50
                  shadow-2xl shadow-gray-900/20 rounded-3xl overflow-hidden
                `}
                onClick={(e) => e.stopPropagation()}
                initial={{ scale: 0.95 }}
                animate={{ scale: 1 }}
                exit={{ scale: 0.95 }}
                transition={{
                  duration: 0.2,
                  ease: [0.4, 0.0, 0.2, 1],
                }}
              >
                <div className="p-6 space-y-6">
                  {/* Navigation Links */}
                  <motion.div
                    className="space-y-4"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.1, duration: 0.2 }}
                  >
                    <button
                      onClick={scrollToFeatures}
                      className="block w-full text-left font-inter text-gray-800 hover:text-gray-900 transition-colors duration-200 font-semibold text-base py-2"
                    >
                      Why Opus0?
                    </button>
                    <Link
                      to="/plans-pricing"
                      className="block font-inter text-gray-800 hover:text-gray-900 transition-colors duration-200 font-semibold text-base py-2"
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      Plans & Pricing
                    </Link>
                    <Link
                      to="/contact"
                      className="block font-inter text-gray-800 hover:text-gray-900 transition-colors duration-200 font-semibold text-base py-2"
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      Contact
                    </Link>
                  </motion.div>

                  {/* Divider */}
                  <motion.div
                    className="border-t border-gray-200"
                    initial={{ opacity: 0, scaleX: 0 }}
                    animate={{ opacity: 1, scaleX: 1 }}
                    transition={{ delay: 0.15, duration: 0.2 }}
                  />

                  {/* Auth Buttons */}
                  <motion.div
                    className="space-y-3"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.2, duration: 0.2 }}
                  >
                    <a
                      href={getGoogleOAuthUrl()}
                      className="w-full font-inter text-gray-800 hover:text-gray-900 px-4 py-3 rounded-2xl transition-all duration-200 font-semibold text-base border border-gray-200 hover:bg-gray-50"
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      Login
                    </a>
                    <a
                      href={getGoogleOAuthUrl()}
                      className="w-full font-inter bg-gray-900 hover:bg-gray-800 text-white px-4 py-3 rounded-2xl transition-colors duration-200 font-semibold text-base border border-gray-900"
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      Get started
                    </a>
                  </motion.div>
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </nav>
    </>
  );
};

export default NavBar;
