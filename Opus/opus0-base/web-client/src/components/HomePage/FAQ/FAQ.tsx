import { useState } from "react";
import { ChevronDown, ExternalLink } from "lucide-react";

const FAQ = () => {
  const [openItems, setOpenItems] = useState<number[]>([]);

  const toggleItem = (index: number) => {
    setOpenItems((prev) =>
      prev.includes(index) ? prev.filter((i) => i !== index) : [...prev, index]
    );
  };

  const faqItems = [
    {
      icon: "🔒",
      title: "Is my data secure with Opus0?",
      description:
        "Yes, absolutely. Opus0 uses end-to-end encryption for all conversations and data. Your intellectual property and sensitive information never leave your control.",
    },
    {
      icon: "🤖",
      title: "Which AI models does Opus0 support?",
      description:
        "Opus0 provides access to all leading AI models including GPT-4, Claude, Gemini, and more. You can switch between models based on your specific task requirements.",
    },
    {
      icon: "💼",
      title: "Can Opus0 integrate with my existing workflow?",
      description:
        "Yes, Opus0 is designed to seamlessly integrate with your current tools and processes. It can monitor your screen, listen to calls, and provide real-time assistance without disrupting your workflow.",
    },
    {
      icon: "📊",
      title: "How does Opus0 improve productivity?",
      description:
        "Opus0 provides real-time insights and answers before you even ask, reducing research time by up to 48% and improving task completion speed significantly.",
    },
  ];

  return (
    <div className="w-full px-4 md:px-6 py-12 md:py-24 flex justify-center">
      <div className="w-full max-w-7xl">
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl md:rounded-[32px] p-8 md:p-16 shadow-sm border border-white/50">
          <div className="flex flex-col md:flex-row md:items-start justify-between mb-12 md:mb-16">
            {/* Left side - FAQ label and title */}
            <div className="flex-1 md:text-left mb-8 md:mb-0">
              <div className="mb-4 md:mb-6">
                <span className="font-inter text-sm text-gray-500 font-medium tracking-wide">
                  FAQ
                </span>
              </div>
              <h2 className="font-inter text-3xl sm:text-4xl md:text-5xl font-medium text-gray-900 leading-tight max-w-md md:mx-0">
                Important questions
                <br />
                and answers
              </h2>
            </div>

            {/* Right side - Learn more button */}
            <div className="flex-shrink-0 md:flex md:justify-start">
              <button className="bg-gray-900 hover:bg-gray-800 text-white px-6 py-3 rounded-full transition-colors duration-200 font-inter text-sm font-medium flex items-center space-x-2">
                <span>Learn more</span>
                <ExternalLink className="w-4 h-4" />
              </button>
            </div>
          </div>

          {/* FAQ Items */}
          <div className="space-y-3 md:space-y-4">
            {faqItems.map((item, index) => (
              <div
                key={index}
                className="border border-gray-200/70 rounded-xl md:rounded-2xl overflow-hidden bg-white/60 backdrop-blur-sm"
              >
                <button
                  onClick={() => toggleItem(index)}
                  className="w-full flex items-center justify-between p-4 md:p-6 text-left hover:bg-white/80 transition-colors duration-200"
                >
                  <div className="flex items-center space-x-3 md:space-x-4">
                    {/* Icon */}
                    <div className="w-10 md:w-12 h-10 md:h-12 rounded-lg md:rounded-xl bg-gray-100/80 flex items-center justify-center text-lg md:text-xl flex-shrink-0">
                      {item.icon}
                    </div>

                    {/* Title */}
                    <h3 className="font-inter text-base md:text-lg font-medium text-gray-900 pr-4">
                      {item.title}
                    </h3>
                  </div>

                  {/* Expand/Collapse Icon */}
                  <div className="flex items-center space-x-4">
                    <ChevronDown
                      className={`w-5 h-5 text-gray-400 transition-transform duration-200 flex-shrink-0 ${
                        openItems.includes(index) ? "rotate-180" : ""
                      }`}
                    />
                  </div>
                </button>

                {/* Expandable Content */}
                {openItems.includes(index) && (
                  <div className="px-4 md:px-6 pb-4 md:pb-6">
                    <div className="ml-13 md:ml-16">
                      <p className="font-inter text-gray-600 leading-relaxed text-sm md:text-base">
                        {item.description}
                      </p>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default FAQ;
