const Hero = () => {
  return (
    <div className="w-full max-w-7xl px-4 md:px-6 pt-32 md:pt-36 pb-10 md:pb-14 flex justify-center mx-auto">
      <div className="max-w-4xl w-full text-center">
        {/* Trust Callout Pill */}
        <div className="inline-flex items-center space-x-2 bg-black text-white rounded-full px-3 md:px-4 py-1.5 md:py-2 mb-8 md:mb-12">
          <div className="flex -space-x-1">
            <div className="w-3 md:w-4 h-3 md:h-4 bg-gray-300 rounded-full border border-white"></div>
            <div className="w-3 md:w-4 h-3 md:h-4 bg-gray-400 rounded-full border border-white"></div>
            <div className="w-3 md:w-4 h-3 md:h-4 bg-gray-500 rounded-full border border-white"></div>
          </div>
          <span className="font-inter text-xs font-medium">
            trusted by 100+ customers
          </span>
        </div>

        {/* Main Headline */}
        <h1 className="font-inter text-5xl sm:text-6xl md:text-5xl lg:text-6xl font-medium text-gray-900 leading-tight mb-6 md:mb-8 px-2">
          Think Faster. Work Smarter.
          <br />
          Stay Private.
        </h1>

        {/* Subtext */}
        <p className="font-inter text-lg sm:text-xl md:text-lg lg:text-xl text-gray-600 mb-8 md:mb-12 max-w-3xl mx-auto leading-relaxed font-normal px-4">
          Advanced AI that automates research, refines writing, and renders
          documents in real-time — with complete privacy and security.
        </p>
      </div>
    </div>
  );
};

export default Hero;
