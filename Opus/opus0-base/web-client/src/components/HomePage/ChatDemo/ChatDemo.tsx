import {
  Send,
  User,
  Bo<PERSON>,
  MessageSquare,
  Settings,
  Plus,
  Search,
} from "lucide-react";

const ChatDemo = () => {
  return (
    <div className="w-full px-6 py-20 flex justify-center">
      <div className="max-w-6xl w-full">
        {/* Chat Demo Window - Hidden on mobile (below md) */}
        <div
          className="hidden md:block relative w-full mx-auto"
          style={{ aspectRatio: "16/9" }}
        >
          {/* Window Frame */}
          <div
            className="bg-white rounded-[32px] shadow-2xl overflow-hidden h-full flex border border-gray-100"
            style={{
              boxShadow:
                "0 32px 64px -12px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(0, 0, 0, 0.05)",
            }}
          >
            {/* Sidebar */}
            <div className="w-72 bg-gray-50 flex flex-col border-r border-gray-100">
              {/* Sidebar Header */}
              <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="font-inter text-gray-900 font-semibold text-lg">
                    Opus0
                  </h2>
                  <button className="p-2.5 hover:bg-gray-100 rounded-xl transition-colors">
                    <Plus className="w-4 h-4 text-gray-500" />
                  </button>
                </div>
                <div className="relative">
                  <Search className="w-4 h-4 text-gray-400 absolute left-4 top-1/2 transform -translate-y-1/2" />
                  <input
                    type="text"
                    placeholder="Search..."
                    className="w-full bg-white border border-gray-200 rounded-xl pl-11 pr-4 py-3 text-gray-900 placeholder-gray-400 text-sm focus:outline-none focus:border-gray-300 transition-colors"
                  />
                </div>
              </div>

              {/* Conversation List */}
              <div className="flex-1 overflow-y-auto px-3">
                <div className="space-y-2">
                  {/* Active Conversation */}
                  <div className="bg-white border border-gray-200 rounded-xl p-4 cursor-pointer">
                    <div className="flex items-start space-x-3">
                      <MessageSquare className="w-4 h-4 text-gray-600 mt-1 flex-shrink-0" />
                      <div className="flex-1 min-w-0">
                        <h3 className="font-inter text-gray-900 text-sm font-medium truncate mb-1">
                          Market Research Analysis
                        </h3>
                        <p className="font-inter text-gray-500 text-xs truncate">
                          Help me analyze the latest trends...
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Other Conversations */}
                  <div className="hover:bg-white rounded-xl p-4 cursor-pointer transition-colors">
                    <div className="flex items-start space-x-3">
                      <MessageSquare className="w-4 h-4 text-gray-400 mt-1 flex-shrink-0" />
                      <div className="flex-1 min-w-0">
                        <h3 className="font-inter text-gray-700 text-sm font-medium truncate mb-1">
                          Document Creation
                        </h3>
                        <p className="font-inter text-gray-400 text-xs truncate">
                          Create a professional proposal...
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="hover:bg-white rounded-xl p-4 cursor-pointer transition-colors">
                    <div className="flex items-start space-x-3">
                      <MessageSquare className="w-4 h-4 text-gray-400 mt-1 flex-shrink-0" />
                      <div className="flex-1 min-w-0">
                        <h3 className="font-inter text-gray-700 text-sm font-medium truncate mb-1">
                          Data Visualization
                        </h3>
                        <p className="font-inter text-gray-400 text-xs truncate">
                          Turn this data into charts...
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Sidebar Footer */}
              <div className="p-3">
                <button className="flex items-center space-x-3 w-full p-3 hover:bg-white rounded-xl transition-colors">
                  <Settings className="w-4 h-4 text-gray-400" />
                  <span className="font-inter text-gray-500 text-sm">
                    Settings
                  </span>
                </button>
              </div>
            </div>

            {/* Main Chat Area */}
            <div className="flex-1 flex flex-col">
              {/* Chat Header */}
              <div className="px-8 py-6 border-b border-gray-100 flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="w-10 h-10 bg-gray-900 rounded-xl flex items-center justify-center">
                    <Bot className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <h3 className="font-inter text-gray-900 font-semibold">
                      Opus0
                    </h3>
                    <p className="font-inter text-gray-500 text-sm">
                      Claude 3.5 Sonnet
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                  <span className="font-inter text-gray-500 text-sm">Live</span>
                </div>
              </div>

              {/* Chat Messages */}
              <div className="flex-1 overflow-y-auto p-8 space-y-8">
                {/* User Message */}
                <div className="flex items-start space-x-4">
                  <div className="w-8 h-8 bg-gray-200 rounded-xl flex items-center justify-center flex-shrink-0">
                    <User className="w-4 h-4 text-gray-600" />
                  </div>
                  <div className="flex-1">
                    <div className="bg-gray-50 border border-gray-100 rounded-2xl px-6 py-4 max-w-2xl">
                      <p className="font-inter text-gray-900 text-sm leading-relaxed">
                        I need to create a comprehensive market analysis report
                        for our Q4 strategy meeting. Can you help me research
                        competitors and identify key trends?
                      </p>
                    </div>
                  </div>
                </div>

                {/* AI Response */}
                <div className="flex items-start space-x-4">
                  <div className="w-8 h-8 bg-gray-900 rounded-xl flex items-center justify-center flex-shrink-0">
                    <Bot className="w-4 h-4 text-white" />
                  </div>
                  <div className="flex-1">
                    <div className="bg-gray-50 border border-gray-100 rounded-2xl px-6 py-5 max-w-4xl">
                      <p className="font-inter text-gray-900 text-sm leading-relaxed mb-4">
                        I'll help you create a comprehensive market analysis.
                        I'm analyzing current market data and competitor
                        information in real-time. Here's what I've found:
                      </p>
                      <div className="space-y-3">
                        <div className="flex items-center space-x-3 p-4 bg-white border border-gray-100 rounded-xl">
                          <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                          <div>
                            <span className="font-inter text-gray-900 font-medium text-sm">
                              Market Growth
                            </span>
                            <p className="font-inter text-gray-600 text-sm">
                              15% YoY growth in your sector with emerging
                              opportunities
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-3 p-4 bg-white border border-gray-100 rounded-xl">
                          <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                          <div>
                            <span className="font-inter text-gray-900 font-medium text-sm">
                              Competitive Landscape
                            </span>
                            <p className="font-inter text-gray-600 text-sm">
                              3 key competitors identified with detailed
                              positioning analysis
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* User Follow-up */}
                <div className="flex items-start space-x-4">
                  <div className="w-8 h-8 bg-gray-200 rounded-xl flex items-center justify-center flex-shrink-0">
                    <User className="w-4 h-4 text-gray-600" />
                  </div>
                  <div className="flex-1">
                    <div className="bg-gray-50 border border-gray-100 rounded-2xl px-6 py-4 max-w-2xl">
                      <p className="font-inter text-gray-900 text-sm leading-relaxed">
                        Perfect! Now create a professional presentation with
                        these insights formatted for executive review.
                      </p>
                    </div>
                  </div>
                </div>

                {/* AI Typing */}
                <div className="flex items-start space-x-4">
                  <div className="w-8 h-8 bg-gray-900 rounded-xl flex items-center justify-center flex-shrink-0">
                    <Bot className="w-4 h-4 text-white" />
                  </div>
                  <div className="flex-1">
                    <div className="bg-gray-50 border border-gray-100 rounded-2xl px-6 py-4">
                      <div className="flex items-center space-x-3">
                        <div className="flex space-x-1">
                          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                          <div
                            className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                            style={{ animationDelay: "0.1s" }}
                          ></div>
                          <div
                            className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                            style={{ animationDelay: "0.2s" }}
                          ></div>
                        </div>
                        <span className="font-inter text-gray-500 text-sm">
                          Creating presentation...
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Chat Input */}
              <div className="p-8 border-t border-gray-100">
                <div className="flex items-end space-x-4">
                  <div className="flex-1 relative">
                    <textarea
                      placeholder="Ask Opus0 anything..."
                      className="w-full bg-gray-50 border border-gray-200 rounded-2xl px-6 py-4 pr-14 text-gray-900 placeholder-gray-400 focus:outline-none focus:border-gray-300 font-inter text-sm resize-none transition-colors min-h-[56px] max-h-32"
                      rows={1}
                    />
                    <div className="absolute right-4 bottom-4">
                      <div className="text-gray-400 text-xs font-inter">⌘↵</div>
                    </div>
                  </div>
                  <button className="bg-gray-900 hover:bg-gray-800 text-white p-4 rounded-2xl transition-all duration-200 flex-shrink-0">
                    <Send className="w-5 h-5" />
                  </button>
                </div>
                <div className="flex items-center justify-between mt-4">
                  <div className="flex items-center space-x-6 text-xs text-gray-500 font-inter">
                    <span className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                      <span>Screen monitoring</span>
                    </span>
                    <span className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                      <span>Audio listening</span>
                    </span>
                  </div>
                  <div className="text-xs text-gray-500 font-inter">
                    AI can make mistakes. Verify important information.
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatDemo;
