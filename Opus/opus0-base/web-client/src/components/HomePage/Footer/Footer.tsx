import { ArrowUpR<PERSON>, <PERSON> } from "lucide-react";
import logoImage from "/images/logo.webp";

const Footer = () => {
  return (
    <div className="w-full px-4 md:px-6 py-20 md:py-32 flex justify-center">
      <div className="w-full max-w-7xl">
        <div className="bg-white/80 backdrop-blur-sm rounded-[32px] md:rounded-[48px] p-8 md:p-16 lg:p-20 relative overflow-hidden border border-white/50 shadow-lg">
          {/* Subtle grid pattern overlay */}
          <div
            className="absolute inset-0 opacity-[0.02]"
            style={{
              backgroundImage: `
                linear-gradient(rgba(0,0,0,0.05) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0,0,0,0.05) 1px, transparent 1px)
              `,
              backgroundSize: "32px 32px",
            }}
          />

          {/* Main content */}
          <div className="relative z-10">
            {/* Top section - Logo and main CTA */}
            <div className="flex flex-col lg:flex-row items-start justify-between mb-16 md:mb-24 space-y-12 lg:space-y-0">
              {/* Left - Logo and tagline */}
              <div className="flex-1">
                <div className="flex items-center space-x-4 mb-6">
                  <div className="w-12 h-12 rounded-2xl bg-gray-100/80 backdrop-blur-sm border border-gray-200/50 flex items-center justify-center">
                    <img
                      src={logoImage}
                      alt="Opus0"
                      className="w-6 h-6 object-contain"
                    />
                  </div>
                  <span className="font-inter text-2xl font-medium text-gray-900">
                    Opus0
                  </span>
                </div>
                <p className="font-inter text-gray-600 text-lg leading-relaxed max-w-md font-normal">
                  The future of knowledge work.
                  <br />
                  Private, powerful, personal.
                </p>
              </div>

              {/* Right - Main CTA */}
              <div className="flex-shrink-0">
                <button className="group bg-gray-900 hover:bg-gray-800 text-white px-8 py-4 rounded-2xl font-inter font-medium text-base transition-all duration-300 hover:scale-105 flex items-center space-x-3 shadow-lg">
                  <span>Start building</span>
                  <ArrowUpRight className="w-5 h-5 group-hover:translate-x-0.5 group-hover:-translate-y-0.5 transition-transform duration-300" />
                </button>
              </div>
            </div>

            {/* Middle section - Navigation grid */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 md:gap-12 mb-16 md:mb-20">
              {/* Product */}
              <div className="space-y-4">
                <h3 className="font-inter text-gray-500 text-xs font-medium uppercase tracking-wider">
                  Product
                </h3>
                <div className="space-y-3">
                  <a
                    href="#"
                    className="block font-inter text-gray-700 hover:text-gray-900 transition-colors text-sm font-normal"
                  >
                    Features
                  </a>
                  <a
                    href="#"
                    className="block font-inter text-gray-700 hover:text-gray-900 transition-colors text-sm font-normal"
                  >
                    Security
                  </a>
                  <a
                    href="#"
                    className="block font-inter text-gray-700 hover:text-gray-900 transition-colors text-sm font-normal"
                  >
                    API
                  </a>
                </div>
              </div>

              {/* Company */}
              <div className="space-y-4">
                <h3 className="font-inter text-gray-500 text-xs font-medium uppercase tracking-wider">
                  Company
                </h3>
                <div className="space-y-3">
                  <a
                    href="#"
                    className="block font-inter text-gray-700 hover:text-gray-900 transition-colors text-sm font-normal"
                  >
                    About
                  </a>
                  <a
                    href="#"
                    className="block font-inter text-gray-700 hover:text-gray-900 transition-colors text-sm font-normal"
                  >
                    Careers
                  </a>
                  <a
                    href="#"
                    className="block font-inter text-gray-700 hover:text-gray-900 transition-colors text-sm font-normal"
                  >
                    Blog
                  </a>
                </div>
              </div>

              {/* Resources */}
              <div className="space-y-4">
                <h3 className="font-inter text-gray-500 text-xs font-medium uppercase tracking-wider">
                  Resources
                </h3>
                <div className="space-y-3">
                  <a
                    href="#"
                    className="block font-inter text-gray-700 hover:text-gray-900 transition-colors text-sm font-normal"
                  >
                    Documentation
                  </a>
                  <a
                    href="#"
                    className="block font-inter text-gray-700 hover:text-gray-900 transition-colors text-sm font-normal"
                  >
                    Support
                  </a>
                  <a
                    href="#"
                    className="block font-inter text-gray-700 hover:text-gray-900 transition-colors text-sm font-normal"
                  >
                    Status
                  </a>
                </div>
              </div>

              {/* Connect */}
              <div className="space-y-4">
                <h3 className="font-inter text-gray-500 text-xs font-medium uppercase tracking-wider">
                  Connect
                </h3>
                <div className="space-y-3">
                  <a
                    href="#"
                    className="group flex items-center space-x-2 font-inter text-gray-700 hover:text-gray-900 transition-colors text-sm font-normal"
                  >
                    <span>Twitter</span>
                    <ArrowUpRight className="w-3 h-3 group-hover:translate-x-0.5 group-hover:-translate-y-0.5 transition-transform duration-200" />
                  </a>
                  <a
                    href="#"
                    className="group flex items-center space-x-2 font-inter text-gray-700 hover:text-gray-900 transition-colors text-sm font-normal"
                  >
                    <span>LinkedIn</span>
                    <ArrowUpRight className="w-3 h-3 group-hover:translate-x-0.5 group-hover:-translate-y-0.5 transition-transform duration-200" />
                  </a>
                  <a
                    href="#"
                    className="group flex items-center space-x-2 font-inter text-gray-700 hover:text-gray-900 transition-colors text-sm font-normal"
                  >
                    <span>GitHub</span>
                    <ArrowUpRight className="w-3 h-3 group-hover:translate-x-0.5 group-hover:-translate-y-0.5 transition-transform duration-200" />
                  </a>
                </div>
              </div>
            </div>

            {/* Bottom section - Legal and contact */}
            <div className="pt-8 border-t border-gray-200/50">
              <div className="flex flex-col md:flex-row items-start md:items-center justify-between space-y-6 md:space-y-0">
                {/* Left - Legal */}
                <div className="flex flex-col md:flex-row md:items-center space-y-3 md:space-y-0 md:space-x-6">
                  <div className="flex items-center space-x-1">
                    <span className="font-inter text-gray-500 text-xs font-normal">
                      © 2025
                    </span>
                    <Dot className="w-3 h-3 text-gray-400" />
                    <span className="font-inter text-gray-500 text-xs font-normal">
                      All rights reserved
                    </span>
                  </div>
                  <div className="flex items-center space-x-4">
                    <a
                      href="#"
                      className="font-inter text-gray-500 hover:text-gray-700 transition-colors text-xs font-normal"
                    >
                      Privacy
                    </a>
                    <a
                      href="#"
                      className="font-inter text-gray-500 hover:text-gray-700 transition-colors text-xs font-normal"
                    >
                      Terms
                    </a>
                  </div>
                </div>

                {/* Right - Contact */}
                <div className="flex items-center space-x-1">
                  <span className="font-inter text-gray-500 text-xs font-normal">
                    Contact:
                  </span>
                  <a
                    href="mailto:<EMAIL>"
                    className="font-inter text-gray-600 hover:text-gray-900 transition-colors text-xs font-normal underline underline-offset-2"
                  >
                    <EMAIL>
                  </a>
                </div>
              </div>
            </div>
          </div>

          {/* Ambient glow effects - subtle for light mode */}
          <div className="absolute top-0 right-0 w-96 h-96 bg-gradient-radial from-blue-100/20 to-transparent rounded-full blur-3xl" />
          <div className="absolute bottom-0 left-0 w-64 h-64 bg-gradient-radial from-gray-100/30 to-transparent rounded-full blur-2xl" />
        </div>
      </div>
    </div>
  );
};

export default Footer;
