const Features = () => {
  return (
    <div className="w-full px-4 md:px-6 py-16 md:py-24 flex justify-center">
      <div className="w-full max-w-7xl">
        <div className="bg-white rounded-2xl md:rounded-[32px] p-6 md:p-12 shadow-sm md:border md:border-gray-100">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 md:gap-12 items-start">
            {/* Left Column - Title and Description */}
            <div className="lg:col-span-1">
              <div className="mb-8 md:mb-6 text-center md:text-left">
                <p className="font-inter text-sm text-gray-500 font-medium mb-3 md:mb-4 tracking-wide">
                  Performance metrics
                </p>
                <h2 className="font-inter text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 leading-tight">
                  The Future of
                  <br />
                  Knowledge Work
                  <br />
                  is Here
                </h2>
                <p className="font-inter text-xs text-gray-400 mt-4 md:mt-6 leading-relaxed">
                  *Metrics based on internal testing and benchmarks. Results may
                  vary in production environments.
                </p>
              </div>
            </div>

            {/* Right Columns - Statistics */}
            <div className="lg:col-span-3">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                {/* 39% Statistic */}
                <div className="space-y-4 md:space-y-6">
                  <div className="text-center md:text-left">
                    <div className="flex items-baseline justify-center md:justify-start space-x-1 mb-3 md:mb-4">
                      <span className="font-inter text-4xl md:text-6xl font-bold text-gray-900">
                        39
                      </span>
                      <span className="font-inter text-xl md:text-2xl font-bold text-gray-900">
                        %
                      </span>
                    </div>
                    <p className="font-inter text-gray-600 text-sm md:text-base leading-relaxed">
                      Better output quality
                      <br />
                      than leading competitors
                    </p>
                  </div>

                  {/* Mobile: Enhanced Horizontal bars */}
                  <div className="md:hidden bg-gray-50 rounded-xl p-4">
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-700 font-inter font-medium">
                            Opus0
                          </span>
                          <span className="text-sm text-gray-500 font-inter">
                            100%
                          </span>
                        </div>
                        <div className="bg-gray-200 rounded-full h-3">
                          <div
                            className="bg-gradient-to-r from-green-400 to-green-300 h-3 rounded-full shadow-sm"
                            style={{ width: "100%" }}
                          ></div>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-700 font-inter font-medium">
                            ManusAI
                          </span>
                          <span className="text-sm text-gray-500 font-inter">
                            72%
                          </span>
                        </div>
                        <div className="bg-gray-200 rounded-full h-3">
                          <div
                            className="bg-gray-400 h-3 rounded-full"
                            style={{ width: "72%" }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Desktop: Vertical bars */}
                  <div className="hidden md:block">
                    <div className="flex items-end justify-start h-56 mb-4 space-x-6">
                      <div className="flex flex-col items-center">
                        <div
                          className="w-14 bg-gradient-to-t from-green-300 to-green-200 rounded-t-lg mb-2"
                          style={{ height: "160px" }}
                        ></div>
                        <span className="text-xs text-gray-500 font-inter font-medium">
                          Opus0
                        </span>
                      </div>
                      <div className="flex flex-col items-center">
                        <div
                          className="w-14 bg-gray-300 rounded-t-lg mb-2"
                          style={{ height: "110px" }}
                        ></div>
                        <span className="text-xs text-gray-500 font-inter font-medium">
                          ManusAI
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 78% Statistic */}
                <div className="space-y-4 md:space-y-6">
                  <div className="text-center md:text-left">
                    <div className="flex items-baseline justify-center md:justify-start space-x-1 mb-3 md:mb-4">
                      <span className="font-inter text-4xl md:text-6xl font-bold text-gray-900">
                        78
                      </span>
                      <span className="font-inter text-xl md:text-2xl font-bold text-gray-900">
                        %
                      </span>
                    </div>
                    <p className="font-inter text-gray-600 text-sm md:text-base leading-relaxed">
                      Most cost-effective for
                      <br />
                      long knowledge tasks
                    </p>
                  </div>

                  {/* Mobile: Enhanced Horizontal bars */}
                  <div className="md:hidden bg-gray-50 rounded-xl p-4">
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-700 font-inter font-medium">
                            Opus0
                          </span>
                          <span className="text-sm text-gray-500 font-inter">
                            100%
                          </span>
                        </div>
                        <div className="bg-gray-200 rounded-full h-3">
                          <div
                            className="bg-gradient-to-r from-green-400 to-green-300 h-3 rounded-full shadow-sm"
                            style={{ width: "100%" }}
                          ></div>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-700 font-inter font-medium">
                            ManusAI
                          </span>
                          <span className="text-sm text-gray-500 font-inter">
                            56%
                          </span>
                        </div>
                        <div className="bg-gray-200 rounded-full h-3">
                          <div
                            className="bg-gray-400 h-3 rounded-full"
                            style={{ width: "56%" }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Desktop: Vertical bars */}
                  <div className="hidden md:block">
                    <div className="flex items-end justify-start h-56 mb-4 space-x-6">
                      <div className="flex flex-col items-center">
                        <div
                          className="w-14 bg-gradient-to-t from-green-300 to-green-200 rounded-t-lg mb-2"
                          style={{ height: "160px" }}
                        ></div>
                        <span className="text-xs text-gray-500 font-inter font-medium">
                          Opus0
                        </span>
                      </div>
                      <div className="flex flex-col items-center">
                        <div
                          className="w-14 bg-gray-300 rounded-t-lg mb-2"
                          style={{ height: "86px" }}
                        ></div>
                        <span className="text-xs text-gray-500 font-inter font-medium">
                          ManusAI
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 48% Statistic */}
                <div className="space-y-4 md:space-y-6">
                  <div className="text-center md:text-left">
                    <div className="flex items-baseline justify-center md:justify-start space-x-1 mb-3 md:mb-4">
                      <span className="font-inter text-4xl md:text-6xl font-bold text-gray-900">
                        48
                      </span>
                      <span className="font-inter text-xl md:text-2xl font-bold text-gray-900">
                        %
                      </span>
                    </div>
                    <p className="font-inter text-gray-600 text-sm md:text-base leading-relaxed">
                      Faster task completion
                      <br />
                      with real-time insights
                    </p>
                  </div>

                  {/* Mobile: Enhanced Horizontal bars */}
                  <div className="md:hidden bg-gray-50 rounded-xl p-4">
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-700 font-inter font-medium">
                            Opus0
                          </span>
                          <span className="text-sm text-gray-500 font-inter">
                            100%
                          </span>
                        </div>
                        <div className="bg-gray-200 rounded-full h-3">
                          <div
                            className="bg-gradient-to-r from-green-400 to-green-300 h-3 rounded-full shadow-sm"
                            style={{ width: "100%" }}
                          ></div>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-700 font-inter font-medium">
                            ManusAI
                          </span>
                          <span className="text-sm text-gray-500 font-inter">
                            68%
                          </span>
                        </div>
                        <div className="bg-gray-200 rounded-full h-3">
                          <div
                            className="bg-gray-400 h-3 rounded-full"
                            style={{ width: "68%" }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Desktop: Vertical bars */}
                  <div className="hidden md:block">
                    <div className="flex items-end justify-start h-56 mb-4 space-x-6">
                      <div className="flex flex-col items-center">
                        <div
                          className="w-14 bg-gradient-to-t from-green-300 to-green-200 rounded-t-lg mb-2"
                          style={{ height: "160px" }}
                        ></div>
                        <span className="text-xs text-gray-500 font-inter font-medium">
                          Opus0
                        </span>
                      </div>
                      <div className="flex flex-col items-center">
                        <div
                          className="w-14 bg-gray-300 rounded-t-lg mb-2"
                          style={{ height: "105px" }}
                        ></div>
                        <span className="text-xs text-gray-500 font-inter font-medium">
                          ManusAI
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Features;
