import { <PERSON>, <PERSON>, Zap, Crown } from "lucide-react";
import { useEffect } from "react";
import NavBar from "../NavBar/NavBar";
import Footer from "../Footer/Footer";

const PlansAndPricing = () => {
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);
  const plans = [
    {
      name: "Starter",
      icon: <Star className="w-6 h-6" />,
      price: "Free",
      period: "forever",
      description: "Perfect for trying out Opus0 and light usage",
      features: [
        "5 conversations per day",
        "Access to GPT-3.5",
        "Basic document creation",
        "Email support",
        "End-to-end encryption",
      ],
      buttonText: "Get Started",
      buttonStyle: "border border-gray-300 text-gray-700 hover:bg-gray-50",
      popular: false,
    },
    {
      name: "Professional",
      icon: <Zap className="w-6 h-6" />,
      price: "$29",
      period: "per month",
      description: "For professionals who need reliable AI assistance",
      features: [
        "Unlimited conversations",
        "Access to all AI models (GPT-4, <PERSON>, <PERSON>)",
        "Advanced document creation",
        "Screen monitoring & audio listening",
        "Priority support",
        "Custom AI agents",
        "Real-time collaboration",
      ],
      buttonText: "Start Free Trial",
      buttonStyle: "bg-gray-900 text-white hover:bg-gray-800",
      popular: true,
    },
    {
      name: "Enterprise",
      icon: <Crown className="w-6 h-6" />,
      price: "Custom",
      period: "pricing",
      description: "For teams and organizations with advanced needs",
      features: [
        "Everything in Professional",
        "Custom AI model training",
        "Advanced security & compliance",
        "Dedicated account manager",
        "Custom integrations",
        "SLA guarantees",
        "On-premise deployment options",
      ],
      buttonText: "Contact Sales",
      buttonStyle: "border border-gray-300 text-gray-700 hover:bg-gray-50",
      popular: false,
    },
  ];

  return (
    <div className="min-h-screen text-gray-900 relative">
      {/* Cerulean gradient background - Much lighter cerulean to white */}
      <div
        className="absolute inset-0 bg-gradient-to-b from-blue-200 via-blue-100 to-white"
        style={{
          background:
            "linear-gradient(180deg, rgb(224 242 254) 0%, rgb(240 249 255) 20%, rgb(248 252 255) 40%, rgb(252 254 255) 60%, rgb(255 255 255) 100%)",
        }}
      ></div>

      {/* Organic Cerulean Blobs - Responsive sizing */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Blob 1 - Top left, organic teardrop shape */}
        <div
          className="absolute opacity-25 blur-3xl"
          style={{
            top: "8%",
            left: "12%",
            width: "200px",
            height: "240px",
            background:
              "linear-gradient(135deg, rgb(186 230 253) 0%, rgb(125 211 252) 40%, rgb(56 189 248) 100%)",
            borderRadius: "60% 40% 30% 70% / 60% 30% 70% 40%",
            transform: "rotate(-15deg)",
            animation: "float 20s ease-in-out infinite",
          }}
        ></div>

        {/* Blob 2 - Top right, flowing organic shape */}
        <div
          className="absolute opacity-20 blur-2xl"
          style={{
            top: "15%",
            right: "8%",
            width: "180px",
            height: "210px",
            background:
              "linear-gradient(225deg, rgb(199 210 254) 0%, rgb(165 180 252) 50%, rgb(129 140 248) 100%)",
            borderRadius: "40% 60% 70% 30% / 40% 70% 30% 60%",
            transform: "rotate(25deg)",
            animation: "float 25s ease-in-out infinite reverse",
          }}
        ></div>

        {/* Blob 3 - Center, subtle accent blob */}
        <div
          className="absolute opacity-15 blur-3xl"
          style={{
            top: "35%",
            left: "45%",
            width: "150px",
            height: "180px",
            background:
              "linear-gradient(45deg, rgb(224 242 254) 0%, rgb(186 230 253) 60%, rgb(125 211 252) 100%)",
            borderRadius: "70% 30% 50% 50% / 30% 70% 50% 50%",
            transform: "rotate(-35deg)",
            animation: "float 30s ease-in-out infinite",
          }}
        ></div>

        {/* Desktop-only blobs */}
        <div className="hidden md:block">
          <div
            className="absolute opacity-25 blur-3xl"
            style={{
              top: "8%",
              left: "12%",
              width: "280px",
              height: "320px",
              background:
                "linear-gradient(135deg, rgb(186 230 253) 0%, rgb(125 211 252) 40%, rgb(56 189 248) 100%)",
              borderRadius: "60% 40% 30% 70% / 60% 30% 70% 40%",
              transform: "rotate(-15deg)",
              animation: "float 20s ease-in-out infinite",
            }}
          ></div>

          <div
            className="absolute opacity-20 blur-2xl"
            style={{
              top: "15%",
              right: "8%",
              width: "240px",
              height: "280px",
              background:
                "linear-gradient(225deg, rgb(199 210 254) 0%, rgb(165 180 252) 50%, rgb(129 140 248) 100%)",
              borderRadius: "40% 60% 70% 30% / 40% 70% 30% 60%",
              transform: "rotate(25deg)",
              animation: "float 25s ease-in-out infinite reverse",
            }}
          ></div>

          <div
            className="absolute opacity-15 blur-3xl"
            style={{
              top: "35%",
              left: "45%",
              width: "200px",
              height: "240px",
              background:
                "linear-gradient(45deg, rgb(224 242 254) 0%, rgb(186 230 253) 60%, rgb(125 211 252) 100%)",
              borderRadius: "70% 30% 50% 50% / 30% 70% 50% 50%",
              transform: "rotate(-35deg)",
              animation: "float 30s ease-in-out infinite",
            }}
          ></div>
        </div>
      </div>

      <div className="relative z-10">
        <NavBar />

        {/* Hero Section - Same structure as home page */}
        <div className="w-full max-w-7xl px-4 md:px-6 pt-32 md:pt-36 pb-10 md:pb-14 flex justify-center mx-auto">
          <div className="max-w-4xl w-full text-center">
            {/* Main Headline - Same style as hero */}
            <h1 className="font-inter text-5xl sm:text-6xl md:text-5xl lg:text-6xl font-medium text-gray-900 leading-tight mb-6 md:mb-8 px-2">
              Simple, Transparent Pricing
            </h1>

            {/* Subtext - Same style as hero */}
            <p className="font-inter text-lg sm:text-xl md:text-lg lg:text-xl text-gray-600 mb-8 md:mb-12 max-w-3xl mx-auto leading-relaxed font-normal px-4">
              Choose the plan that fits your needs. Start free, upgrade when
              you're ready.
            </p>
          </div>
        </div>

        {/* Pricing Cards - Clean white cards on gradient background */}
        <div className="w-full px-4 md:px-6 pb-16 md:pb-24 flex justify-center">
          <div className="w-full max-w-7xl">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8">
              {plans.map((plan, index) => (
                <div
                  key={index}
                  className={`
                    relative bg-white/90 backdrop-blur-sm rounded-2xl md:rounded-3xl p-6 md:p-8 shadow-lg border transition-all duration-200 hover:shadow-xl hover:bg-white/95
                    ${
                      plan.popular
                        ? "border-gray-900 ring-2 ring-gray-900 ring-opacity-10"
                        : "border-gray-200/50 hover:border-gray-300/70"
                    }
                  `}
                >
                  {/* Popular Badge */}
                  {plan.popular && (
                    <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                      <div className="bg-gray-900 text-white px-4 py-1 rounded-full text-xs font-inter font-semibold">
                        Most Popular
                      </div>
                    </div>
                  )}

                  {/* Plan Header */}
                  <div className="text-center mb-8">
                    <div className="flex items-center justify-center mb-4">
                      <div
                        className={`
                        w-12 h-12 rounded-xl flex items-center justify-center
                        ${
                          plan.popular
                            ? "bg-gray-900 text-white"
                            : "bg-gray-100 text-gray-600"
                        }
                      `}
                      >
                        {plan.icon}
                      </div>
                    </div>

                    <h3 className="font-inter text-2xl font-semibold text-gray-900 mb-2">
                      {plan.name}
                    </h3>

                    <div className="mb-4">
                      <span className="font-inter text-4xl md:text-5xl font-bold text-gray-900">
                        {plan.price}
                      </span>
                      {plan.period && (
                        <span className="font-inter text-gray-500 text-base ml-2">
                          {plan.period}
                        </span>
                      )}
                    </div>

                    <p className="font-inter text-gray-600 text-sm leading-relaxed">
                      {plan.description}
                    </p>
                  </div>

                  {/* Features List */}
                  <div className="space-y-4 mb-8">
                    {plan.features.map((feature, featureIndex) => (
                      <div
                        key={featureIndex}
                        className="flex items-start space-x-3"
                      >
                        <div className="flex-shrink-0 mt-0.5">
                          <Check className="w-5 h-5 text-green-500" />
                        </div>
                        <span className="font-inter text-gray-700 text-sm leading-relaxed">
                          {feature}
                        </span>
                      </div>
                    ))}
                  </div>

                  {/* CTA Button */}
                  <button
                    className={`
                    w-full py-3 px-6 rounded-2xl font-inter font-semibold text-sm transition-all duration-200
                    ${plan.buttonStyle}
                  `}
                  >
                    {plan.buttonText}
                  </button>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* FAQ Section - Minimal and clean */}
        <div className="w-full px-4 md:px-6 py-16 md:py-20 flex justify-center">
          <div className="w-full max-w-7xl">
            <div className="text-center mb-16">
              <h2 className="font-inter text-3xl md:text-4xl font-medium text-gray-900 mb-4">
                Frequently Asked Questions
              </h2>
              <p className="font-inter text-gray-600 text-lg max-w-2xl mx-auto leading-relaxed font-normal">
                Everything you need to know about our pricing and plans.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 md:gap-12">
              <div className="space-y-8">
                <div className="bg-white/70 backdrop-blur-sm rounded-2xl p-6 border border-white/50">
                  <h3 className="font-inter text-lg font-semibold text-gray-900 mb-3">
                    Can I change plans anytime?
                  </h3>
                  <p className="font-inter text-gray-600 leading-relaxed">
                    Yes, you can upgrade or downgrade your plan at any time.
                    Changes take effect immediately.
                  </p>
                </div>

                <div className="bg-white/70 backdrop-blur-sm rounded-2xl p-6 border border-white/50">
                  <h3 className="font-inter text-lg font-semibold text-gray-900 mb-3">
                    Is there a free trial?
                  </h3>
                  <p className="font-inter text-gray-600 leading-relaxed">
                    Yes! Professional plan comes with a 14-day free trial. No
                    credit card required to start.
                  </p>
                </div>

                <div className="bg-white/70 backdrop-blur-sm rounded-2xl p-6 border border-white/50">
                  <h3 className="font-inter text-lg font-semibold text-gray-900 mb-3">
                    What payment methods do you accept?
                  </h3>
                  <p className="font-inter text-gray-600 leading-relaxed">
                    We accept all major credit cards and PayPal.
                  </p>
                </div>
              </div>

              <div className="space-y-8">
                <div className="bg-white/70 backdrop-blur-sm rounded-2xl p-6 border border-white/50">
                  <h3 className="font-inter text-lg font-semibold text-gray-900 mb-3">
                    Is my data secure?
                  </h3>
                  <p className="font-inter text-gray-600 leading-relaxed">
                    Absolutely. All plans include end-to-end encryption, and we
                    never store or access your conversations.
                  </p>
                </div>

                <div className="bg-white/70 backdrop-blur-sm rounded-2xl p-6 border border-white/50">
                  <h3 className="font-inter text-lg font-semibold text-gray-900 mb-3">
                    Can I cancel anytime?
                  </h3>
                  <p className="font-inter text-gray-600 leading-relaxed">
                    Yes, you can cancel your subscription at any time. You'll
                    continue to have access until the end of your billing
                    period.
                  </p>
                </div>

                <div className="bg-white/70 backdrop-blur-sm rounded-2xl p-6 border border-white/50">
                  <h3 className="font-inter text-lg font-semibold text-gray-900 mb-3">
                    Do you offer discounts for students?
                  </h3>
                  <p className="font-inter text-gray-600 leading-relaxed">
                    Yes! We offer special pricing for students and educators.
                    Contact us for details.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <Footer />
      </div>

      <style>{`
        @keyframes float {
          0%, 100% {
            transform: translateY(0px) rotate(var(--rotation, 0deg));
          }
          33% {
            transform: translateY(-20px) rotate(calc(var(--rotation, 0deg) + 5deg));
          }
          66% {
            transform: translateY(10px) rotate(calc(var(--rotation, 0deg) - 3deg));
          }
        }
      `}</style>
    </div>
  );
};

export default PlansAndPricing;
