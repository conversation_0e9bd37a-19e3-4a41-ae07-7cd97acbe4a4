import React from "react";
import { MoreVertical, Edit2, Star, Trash2 } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { useThemeStore } from "../../../lib/theme/store";

interface ChatOptionsMenuProps {
  isFavorite: boolean;
  onEdit: () => void;
  onToggleFavorite: () => void;
  onDelete: () => void;
}

export function ChatOptionsMenu({
  isFavorite,
  onEdit,
  onToggleFavorite,
  onDelete,
}: ChatOptionsMenuProps) {
  const [isOpen, setIsOpen] = React.useState(false);
  const menuRef = React.useRef<HTMLDivElement>(null);
  const isDark = useThemeStore((s) => s.isDark);

  // <PERSON>le click outside to close menu
  React.useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  // Menu items configuration
  const menuItems = [
    { icon: Edit2, label: "Rename", onClick: onEdit },
    {
      icon: Star,
      label: isFavorite ? "Remove from favorites" : "Add to favorites",
      onClick: onToggleFavorite,
    },
    { icon: Trash2, label: "Delete chat", onClick: onDelete, danger: true },
  ];

  return (
    <div className="relative" ref={menuRef}>
      <motion.button
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        onClick={(e) => {
          e.stopPropagation();
          setIsOpen(!isOpen);
        }}
        className={`opacity-0 group-hover:opacity-100 p-1 rounded-md transition-all duration-200 ${
          isDark ? "hover:bg-sidebar-dark-button-hover" : "hover:bg-sidebar-light-button-hover"
        }`}
      >
        <MoreVertical
          size={14}
          className={
            isDark ? "text-sidebar-dark-text-secondary" : "text-sidebar-light-text-secondary"
          }
        />
      </motion.button>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: -10 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: -10 }}
            transition={{ type: "spring", stiffness: 400, damping: 30 }}
            className={`absolute right-0 top-full mt-2 rounded-lg shadow-md border p-2 min-w-[170px] z-50 space-y-1 ${
              isDark
                ? "bg-sidebar-dark-button-bg border-sidebar-dark-border"
                : "bg-sidebar-light-button-bg border-sidebar-light-border"
            }`}
          >
            {menuItems.map((item) => (
              <button
                key={item.label}
                onClick={(e) => {
                  e.stopPropagation();
                  item.onClick();
                  setIsOpen(false);
                }}
                className={`w-full flex items-center gap-2 px-4 py-2 text-left rounded-md text-sm transition-colors ${
                  item.danger
                    ? isDark
                      ? "text-red-400 hover:bg-red-900/10"
                      : "text-red-600 hover:bg-red-50"
                    : isDark
                      ? "text-sidebar-dark-text-secondary hover:bg-sidebar-dark-button-hover"
                      : "text-sidebar-light-text-secondary hover:bg-sidebar-light-button-hover"
                }`}
              >
                <item.icon size={14} />
                <span>{item.label}</span>
              </button>
            ))}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
