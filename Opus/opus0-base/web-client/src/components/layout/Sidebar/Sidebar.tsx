// src/components/layout/Sidebar/Sidebar.tsx
import { useCallback } from "react";
import { useNavigate } from "react-router-dom";
import {
  Pin,
  PinOff,
  Search,
  Plus,
  Star,
  History,
  User,
} from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { ChatOptionsMenu } from "./ChatOptionsMenu";
import { UserOptionsMenu } from "../User/UserOptionsMenu";
import { useChatStore, useAuthStore } from "../../../lib/chat/store";
import { useThemeStore } from "../../../lib/theme/store";
import { deleteChatRecord } from "../../../lib/chat/api";
import { socket } from "../../../lib/socket";

// Import the Chat and ChatId types so we can type things correctly
import type { ChatSummary, ChatId } from "../../../lib/chat/types";

interface SidebarProps {
  className?: string;
  isDocked: boolean;
  onDockChange: (isDocked: boolean) => void;
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
}

export function Sidebar({
  className = "",
  isDocked,
  onDockChange,
  isOpen,
  onOpenChange,
}: SidebarProps) {
  const { logout, userName, userPicture } = useAuthStore();
  const navigate = useNavigate();
  const isDark = useThemeStore((s) => s.isDark);

  // Chat Store
  const summaries = useChatStore((s) => s.summaries);
  const currentChatId = useChatStore((s) => s.currentChatId);
  const createChat = useChatStore((s) => s.createChat);
  const deleteChat = useChatStore((s) => s.deleteChat);
  const updateChat = useChatStore((s) => s.updateChat);
  const setCurrentChat = useChatStore((s) => s.setCurrentChat);

  // per-chat processing state
  const isProcessing = useChatStore((s) => {
    const id = s.currentChatId;
    return id ? s.processingMap[id] ?? false : false;
  });

  // Hover behavior unchanged...
  const handleMouseEnter = useCallback(() => {
    if (!isDocked) onOpenChange(true);
  }, [isDocked, onOpenChange]);

  const handleMouseLeave = useCallback(() => {
    if (!isDocked) onOpenChange(false);
  }, [isDocked, onOpenChange]);

  const toggleDocked = useCallback(() => {
    onDockChange(!isDocked);
    onOpenChange(false);
  }, [isDocked, onDockChange, onOpenChange]);

  // Create a brand-new chat
  const handleNewChat = useCallback(() => {
    if (isProcessing) return; // guard just in case
    const newChatId = createChat();
    setCurrentChat(newChatId);
    socket.emit("chat_created", {
      chatId: newChatId,
      userId: localStorage.getItem("user_id"),
    });
    navigate(`/chat/${newChatId}`);
  }, [createChat, setCurrentChat, isProcessing, navigate]);

  // Now correctly typed to ChatId
  const handleSelectChat = useCallback(
    (chatId: ChatId) => {
      setCurrentChat(chatId);
      navigate(`/chat/${chatId}`);
    },
    [setCurrentChat, navigate]
  );

  const handleEditChat = useCallback(
    (chatId: ChatId, newTitle: string) => {
      updateChat(chatId, { title: newTitle });
    },
    [updateChat]
  );

  const handleToggleFavorite = useCallback(
    (chatId: ChatId) => {
      const chat = summaries.find((c) => c.id === chatId);
      if (chat) updateChat(chatId, { isFavorite: !chat.isFavorite });
    },
    [summaries, updateChat]
  );

  const handleDeleteChat = useCallback(
    async (chatId: ChatId) => {
      try {
        await deleteChatRecord(chatId);
      } catch {
        // swallow errors for now
      }
      deleteChat(chatId);

      if (currentChatId === chatId) {
        const newChatId = createChat();
        setCurrentChat(newChatId);
        socket.emit("chat_created", {
          chatId: newChatId,
          userId: localStorage.getItem("user_id"),
        });
        navigate(`/chat/${newChatId}`);
      }
    },
    [currentChatId, deleteChat, createChat, setCurrentChat, navigate]
  );

  const handleLogout = useCallback(() => {
    logout();
    navigate("/home", { replace: true });
  }, [logout, navigate]);

  // Split favorites vs recent
  const favoriteChats = summaries.filter((c) => c.isFavorite);
  const recentChats = summaries.filter((c) => !c.isFavorite);

  // Section header
  const SectionHeader = ({ title }: { title: string }) => (
    <h2
      className={`text-sm font-medium uppercase px-3 mb-2 tracking-wide ${
        isDark ? "text-sidebar-dark-text-secondary" : "text-sidebar-light-text-secondary"
      }`}
    >
      {title}
    </h2>
  );

  // Chat item now takes a ChatSummary
  const ChatItem = ({ chat }: { chat: ChatSummary }) => {
    const isGenerating = useChatStore((s) => s.processingMap[chat.id] ?? false);
    return (
      <div
        onClick={() => handleSelectChat(chat.id)}
        className={`group px-3 py-2 rounded-md cursor-pointer transition-colors duration-150 ${
          isDark ? "hover:bg-sidebar-dark-hover" : "hover:bg-sidebar-light-hover"
        } ${
          isDark && currentChatId === chat.id ? "bg-sidebar-dark-hover" : ""
        } ${
          !isDark && currentChatId === chat.id ? "bg-sidebar-light-hover" : ""
        }`}
      >
        <div className="flex items-start gap-2.5">
          {chat.isFavorite ? (
            <Star
              size={16}
              className={`mt-1 flex-shrink-0 ${
                isDark ? "text-sidebar-dark-text-secondary" : "text-sidebar-light-text-secondary"
              }`}
            />
          ) : (
            <History
              size={16}
              className={`mt-1 flex-shrink-0 ${
                isDark ? "text-sidebar-dark-text-secondary" : "text-sidebar-light-text-secondary"
              }`}
            />
          )}
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between gap-1.5">
              <h3
                className={`text-base font-medium truncate leading-snug pr-1 ${
                  isDark ? "text-sidebar-dark-text" : "text-sidebar-light-text"
                }`}
              >
                {chat.title}
              </h3>
              <ChatOptionsMenu
                isFavorite={chat.isFavorite}
                onEdit={() => handleEditChat(chat.id, chat.title)}
                onToggleFavorite={() => handleToggleFavorite(chat.id)}
                onDelete={() => handleDeleteChat(chat.id)}
              />
            </div>
            <p
              className={`text-sm truncate mt-0.5 leading-relaxed min-h-[1rem] ${
                isDark ? "text-sidebar-dark-text-secondary" : "text-sidebar-light-text-secondary"
              }`}
            >
              {isGenerating ? (
                <span className="animate-pulse">
                  {chat.preview || "\u00A0"}
                </span>
              ) : (
                chat.preview || "\u00A0"
              )}
            </p>
          </div>
        </div>
      </div>
    );
  };

  return (
    <>
      {/* Hover area */}
      {!isDocked && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onMouseEnter={handleMouseEnter}
          className="hidden lg:block fixed top-0 left-0 w-20 h-full z-[50]"
        />
      )}

      {/* Sidebar */}
      <motion.aside
        layout
        className={`${
          isDocked ? "w-80 xl:w-96" : "w-0"
        } flex-shrink-0 relative ${className}`}
      >
        <motion.div
          initial={false}
          animate={{
            x: !isDocked && !isOpen ? "-100%" : 0,
            opacity: !isDocked && !isOpen ? 0 : 1,
          }}
          transition={{
            type: "spring",
            stiffness: 400,
            damping: 30,
            mass: 0.8,
          }}
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
          className={`fixed top-0 left-0 h-full w-[280px] md:w-80 xl:w-96 ${
            isDark
              ? "bg-sidebar-dark-bg text-sidebar-dark-text"
              : "bg-sidebar-light-bg text-sidebar-light-text"
          } z-[50] flex flex-col`}
        >
          <div className="flex-1 overflow-hidden flex flex-col">
            {/* Header */}
            <div className="p-3 space-y-3">
              <div className="flex items-center justify-between px-0.5">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={toggleDocked}
                  className={`hidden lg:block p-2 cursor-pointer rounded-md transition-colors ${
                    isDark ? "hover:bg-sidebar-dark-button-hover" : "hover:bg-sidebar-light-button-hover"
                  }`}
                >
                  {isDocked ? <PinOff size={20} /> : <Pin size={20} />}
                </motion.button>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className={`p-2 cursor-pointer rounded-md transition-colors ml-auto ${
                    isDark ? "hover:bg-sidebar-dark-button-hover" : "hover:bg-sidebar-light-button-hover"
                  }`}
                >
                  <Search size={20} />
                </motion.button>
              </div>

              {isProcessing ? (
                <div className="relative group w-full">
                  <motion.button
                    disabled
                    className={`w-full flex items-center justify-center gap-2 opacity-50 cursor-not-allowed py-2.5 px-3 rounded-md transition-colors text-base font-medium ${
                      isDark
                        ? "bg-sidebar-dark-button-bg"
                        : "bg-sidebar-light-button-bg"
                    }`}
                  >
                    <Plus size={18} />
                    <span>New chat</span>
                  </motion.button>

                  {/* Tooltip (hidden by default, shown on hover) */}
                  <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 pointer-events-none opacity-0 group-hover:opacity-100 transition-opacity">
                    <div
                      className={`text-xs py-1.5 px-2.5 rounded shadow-lg whitespace-nowrap ${
                        isDark
                          ? "bg-chat-dark-secondary-bg text-chat-dark-text"
                          : "bg-chat-light-secondary-bg text-chat-light-text"
                      }`}
                    >
                      Multiple parallel chats are disabled in the closed beta.
                    </div>
                  </div>
                </div>
              ) : (
                <motion.button
                  whileHover={{ scale: 1.01 }}
                  whileTap={{ scale: 0.99 }}
                  onClick={handleNewChat}
                  className={`w-full flex items-center justify-center gap-2 cursor-pointer py-2.5 px-3 rounded-md transition-colors text-base font-medium ${
                    isDark
                      ? "bg-sidebar-dark-button-bg hover:bg-sidebar-dark-button-hover"
                      : "bg-sidebar-light-button-bg hover:bg-sidebar-light-button-hover"
                  }`}
                >
                  <Plus size={18} />
                  <span>New chat</span>
                </motion.button>
              )}
            </div>

            {/* Chat lists */}
            <div className="flex-1 overflow-y-auto px-3">
              {favoriteChats.length > 0 && (
                <div className="mb-4">
                  <SectionHeader title="Favorites" />
                  <div className="space-y-1">
                    {favoriteChats.map((c) => (
                      <ChatItem key={c.id} chat={c} />
                    ))}
                  </div>
                </div>
              )}
              {recentChats.length > 0 && (
                <div>
                  <SectionHeader title="Recent" />
                  <div className="space-y-1">
                    {recentChats.map((c) => (
                      <ChatItem key={c.id} chat={c} />
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Footer */}
          <div
            className={`p-3 border-t ${
              isDark ? "border-sidebar-dark-border" : "border-sidebar-light-border"
            }`}
          >
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center gap-2.5">
                {userPicture ? (
                  <img
                    src={userPicture}
                    alt="User avatar"
                    className="w-8 h-8 rounded-full object-cover"
                  />
                ) : (
                  <div
                    className={`w-8 h-8 rounded-full flex items-center justify-center ${
                      isDark ? "bg-sidebar-dark-button-bg" : "bg-sidebar-light-button-bg"
                    }`}
                  >
                    <User size={18} />
                  </div>
                )}
                <span
                  className={`text-base font-medium truncate ${
                    isDark ? "text-sidebar-dark-text" : "text-sidebar-light-text"
                  }`}
                  title={userName || "Guest"}
                >
                  {userName || "Guest"}
                </span>
              </div>
              <UserOptionsMenu onLogout={handleLogout} />
            </div>
          </div>
        </motion.div>
      </motion.aside>

      {/* Mobile overlay */}
      <AnimatePresence>
        {isOpen && !isDocked && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="lg:hidden cursor-pointer fixed inset-0 bg-black bg-opacity-50 z-[40]"
            onClick={() => onOpenChange(false)}
          />
        )}
      </AnimatePresence>
    </>
  );
}
