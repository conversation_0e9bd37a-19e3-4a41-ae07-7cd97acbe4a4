import { motion } from "framer-motion";

interface MenuToggleProps {
  isOpen: boolean;
  onClick: () => void;
}

export function MenuToggle({ isOpen, onClick }: MenuToggleProps) {
  const variants = {
    open: {
      transition: { staggerChildren: 0.07, delayChildren: 0.1 },
    },
    closed: {
      transition: { staggerChildren: 0.05, staggerDirection: -1 },
    },
  };

  return (
    <motion.button
      onClick={onClick}
      className="relative w-10 h-10 cursor-pointer rounded-lg bg-transparent hover:bg-chat-light-button-hover dark:hover:bg-chat-dark-button-hover transition-colors"
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
    >
      <motion.div
        className="flex flex-col items-center justify-center w-full h-full gap-[3px]"
        variants={variants}
        initial="closed"
        animate={isOpen ? "open" : "closed"}
      >
        <motion.div
          className={`w-5 h-[2px] rounded-full transform transition-all duration-300 ${
            isOpen
              ? "rotate-45 translate-y-[7px] bg-chat-light-text dark:bg-chat-dark-text"
              : "bg-chat-light-text-secondary dark:bg-chat-dark-text-secondary"
          }`}
        />
        <motion.div
          className={`w-5 h-[2px] rounded-full transform transition-all duration-300 ${
            isOpen
              ? "opacity-0 bg-chat-light-text dark:bg-chat-dark-text"
              : "opacity-100 bg-chat-light-text-secondary dark:bg-chat-dark-text-secondary"
          }`}
        />
        <motion.div
          className={`w-5 h-[2px] rounded-full transform transition-all duration-300 ${
            isOpen
              ? "-rotate-45 -translate-y-[7px] bg-chat-light-text dark:bg-chat-dark-text"
              : "bg-chat-light-text-secondary dark:bg-chat-dark-text-secondary"
          }`}
        />
      </motion.div>
    </motion.button>
  );
}
