// src/components/chat/TitleBar/TitleBar.tsx

import React, { useState, useCallback, useEffect, useRef } from "react";
import { Share2, <PERSON>, Pencil, Clock } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { useChatStore } from "../../../lib/chat/store";
import { MenuToggle } from "../../layout/Sidebar/MenuToggle";
import { useThemeStore } from "../../../lib/theme/store";
import logo from "/logo.webp";

interface TitleBarProps {
  isMobile?: boolean;
  isMenuOpen?: boolean;
  onMenuToggle?: () => void;
  showTitle?: boolean;
  title?: string;
  onTitleChange?: (newTitle: string) => void;
}

export function TitleBar({
  isMobile = false,
  isMenuOpen = false,
  onMenuToggle,
  showTitle = false,
  title = "",
  onTitleChange,
}: TitleBarProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [tempTitle, setTempTitle] = useState(title);
  const [showShareTooltip, setShowShareTooltip] = useState(false);
  const [showTempTooltip, setShowTempTooltip] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  const isDark = useThemeStore((s) => s.isDark);

  // Zustand store
  const { currentChatId, chats, updateChat } = useChatStore();
  const currentChat = chats.find((c) => c.id === currentChatId);
  const isFavorite = currentChat?.isFavorite || false;

  // keep local tempTitle in sync
  useEffect(() => {
    setTempTitle(title);
  }, [title]);

  // auto-focus when editing
  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
      inputRef.current.select();
    }
  }, [isEditing]);

  const handleTitleSubmit = useCallback(() => {
    if (tempTitle.trim() && onTitleChange) {
      onTitleChange(tempTitle.trim());
    }
    setIsEditing(false);
  }, [tempTitle, onTitleChange]);

  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent) => {
      if (e.key === "Enter") {
        e.preventDefault();
        handleTitleSubmit();
      } else if (e.key === "Escape") {
        setIsEditing(false);
        setTempTitle(title);
      }
    },
    [handleTitleSubmit, title]
  );

  const handleShare = useCallback(() => {
    console.log("Share clicked");
  }, []);

  const handleFavoriteToggle = useCallback(() => {
    if (currentChatId) {
      updateChat(currentChatId, { isFavorite: !isFavorite });
    }
  }, [currentChatId, isFavorite, updateChat]);

  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`px-6 py-4 flex items-center ${
        isDark ? "bg-title-dark-bg" : "bg-title-light-bg"
      }`}
    >
      {isMobile && onMenuToggle && (
        <div className="mr-4">
          <MenuToggle isOpen={isMenuOpen} onClick={onMenuToggle} />
        </div>
      )}

      <div className="flex items-center gap-4">
        <img
          src={logo}
          alt="Opus Logo"
          className={`w-10 h-10 object-contain flex-shrink-0 ${
            isDark ? "invert" : ""
          }`}
        />

        <AnimatePresence>
          {showTitle && !isMobile && (
            <motion.div
              className="flex items-center"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
            >
              {isEditing ? (
                <div className="relative max-w-full sm:max-w-[200px] lg:max-w-[480px]">
                  <input
                    ref={inputRef}
                    type="text"
                    value={tempTitle}
                    onChange={(e) => setTempTitle(e.target.value)}
                    onBlur={handleTitleSubmit}
                    onKeyDown={handleKeyDown}
                    className={`w-full py-1.5 px-2.5 rounded-md text-lg font-medium focus:outline-none truncate ${
                      isDark
                        ? "bg-chat-dark-input-bg text-chat-dark-text"
                        : "bg-chat-light-input-bg text-chat-light-text"
                    }`}
                    placeholder="Enter chat title..."
                  />
                </div>
              ) : (
                <div className="flex items-center gap-2 group">
                  <h1
                    className={`text-lg font-medium truncate xl:max-w-full md:max-w-[350px] lg:max-w-[600px] max-w-[150px] ${
                      isDark ? "text-title-dark-text" : "text-title-light-text"
                    }`}
                  >
                    {title || "Untitled Chat"}
                  </h1>
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => setIsEditing(true)}
                    className={`p-1 rounded-md transition-colors opacity-0 group-hover:opacity-100 focus:opacity-100 ${
                      isDark
                        ? "hover:bg-title-dark-button-hover"
                        : "hover:bg-title-light-button-hover"
                    }`}
                  >
                    <Pencil
                      size={14}
                      className={
                        isDark
                          ? "text-title-dark-text-secondary"
                          : "text-title-light-text-secondary"
                      }
                    />
                  </motion.button>
                </div>
              )}
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      <div className="flex-1" />

      <span className="mr-2 rounded-md border border-yellow-400 bg-yellow-300 px-2 py-0.5 text-xs font-semibold text-yellow-900">
        Beta
      </span>

      <AnimatePresence>
        {showTitle && (
          <motion.div
            className="flex items-center gap-1"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 20 }}
          >
            <div className="relative">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onMouseEnter={() => setShowTempTooltip(true)}
                onMouseLeave={() => setShowTempTooltip(false)}
                className={`p-2.5 rounded-md transition-colors ${
                  isDark
                    ? "hover:bg-title-dark-button-hover text-title-dark-text-secondary hover:text-title-dark-text"
                    : "hover:bg-title-light-button-hover text-title-light-text-secondary hover:text-title-light-text"
                }`}
              >
                <Clock size={18} strokeWidth={1.5} />
              </motion.button>

              <AnimatePresence>
                {showTempTooltip && (
                  <motion.div
                    initial={{ opacity: 0, x: 5 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 5 }}
                    className={`absolute right-full mr-2 top-1/2 -translate-y-1/2 px-3 py-1.5 text-sm rounded-lg whitespace-nowrap shadow-lg z-50 ${
                      isDark
                        ? "bg-chat-dark-secondary-bg text-chat-dark-text border-title-dark-border"
                        : "bg-chat-light-secondary-bg text-chat-light-text border-title-light-border"
                    }`}
                  >
                    All chats are temporary. <br /> Refresh to delete chats.
                    <div
                      className={`absolute top-1/2 right-0 translate-x-1/2 -translate-y-1/2 rotate-45 w-2 h-2 ${
                        isDark
                          ? "bg-chat-dark-secondary-bg border-t border-r border-title-dark-border"
                          : "bg-chat-light-secondary-bg border-t border-r border-title-light-border"
                      }`}
                    />
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
            <div className="relative">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={handleShare}
                onMouseEnter={() => setShowShareTooltip(true)}
                onMouseLeave={() => setShowShareTooltip(false)}
                className={`p-2.5 rounded-md transition-colors ${
                  isDark
                    ? "hover:bg-title-dark-button-hover text-title-dark-text-secondary hover:text-title-dark-text"
                    : "hover:bg-title-light-button-hover text-title-light-text-secondary hover:text-title-light-text"
                }`}
              >
                <Share2 size={18} strokeWidth={1.5} />
              </motion.button>

              <AnimatePresence>
                {showShareTooltip && (
                  <motion.div
                    initial={{ opacity: 0, x: 5 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 5 }}
                    className={`absolute right-full mr-2 top-1/2 -translate-y-1/2 px-3 py-1.5 text-sm rounded-lg whitespace-nowrap shadow-lg z-50 ${
                      isDark
                        ? "bg-chat-dark-secondary-bg text-chat-dark-text border-title-dark-border"
                        : "bg-chat-light-secondary-bg text-chat-light-text border-title-light-border"
                    }`}
                  >
                    Share feature currently unavailable
                    <div
                      className={`absolute top-1/2 right-0 translate-x-1/2 -translate-y-1/2 rotate-45 w-2 h-2 ${
                        isDark
                          ? "bg-chat-dark-secondary-bg border-t border-r border-title-dark-border"
                          : "bg-chat-light-secondary-bg border-t border-r border-title-light-border"
                      }`}
                    />
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={handleFavoriteToggle}
              className={`p-2.5 rounded-md transition-colors ${
                isFavorite
                  ? isDark
                    ? "text-yellow-400 hover:bg-title-dark-button-hover"
                    : "text-yellow-500 hover:bg-title-light-button-hover"
                  : isDark
                    ? "hover:bg-title-dark-button-hover text-title-dark-text-secondary hover:text-title-dark-text"
                    : "hover:bg-title-light-button-hover text-title-light-text-secondary hover:text-title-light-text"
              }`}
            >
              <Star
                size={18}
                strokeWidth={1.5}
                fill={isFavorite ? "currentColor" : "none"}
              />
            </motion.button>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
}
