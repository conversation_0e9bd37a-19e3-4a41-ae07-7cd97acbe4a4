import { motion } from "framer-motion";
import { useThemeStore } from "../../../lib/theme/store";

interface WelcomeProps {
  username: string;
}

export function Welcome({ username }: WelcomeProps) {
  const isDark = useThemeStore((s) => s.isDark);
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="h-full flex flex-col items-center justify-center px-4"
    >
      <motion.h1
        className={`text-3xl md:text-4xl font-display font-semibold mb-2 md:mb-3 ${
          isDark ? "text-chat-dark-text" : "text-chat-light-text"
        }`}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        Hello {username}
      </motion.h1>
      <motion.p
        className={`text-lg md:text-xl font-display ${
          isDark ? "text-chat-dark-text-secondary" : "text-chat-light-text-secondary"
        }`}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
      >
        What can the swarm do for you?
      </motion.p>
      <motion.div
        className="max-w-md w-full text-center mt-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
      >
        <p
          className={`text-xs md:text-sm ${
            isDark ? "text-chat-dark-text-secondary" : "text-chat-light-text-secondary"
          }`}
        >
          This closed architectural beta currently has only a small suite of
          agents. Your feedback is greatly appreciated—researchers, additional
          document outputs, code interpreters, MCP features and third-party
          integrations will arrive after stability improvements.
        </p>
      </motion.div>
    </motion.div>
  );
}
