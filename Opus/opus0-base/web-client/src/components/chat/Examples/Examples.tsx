import { motion } from "framer-motion";
import { <PERSON>, <PERSON>, Code, LineChart } from "lucide-react";
import { ExampleCard } from "./ExampleCard";
import type { LucideIcon } from "lucide-react";

interface Example {
  icon: LucideIcon;
  title: string;
  description: string;
  prompt: string;
}

interface ExamplesProps {
  onExampleSelect: (prompt: string) => void;
}

export function Examples({ onExampleSelect }: ExamplesProps) {
  const examples: Example[] = [
    {
      icon: Rocket,
      title: "Trip to Japan",
      description:
        "Opus0 integrates comprehensive travel information to create personalized itineraries",
      prompt:
        "I'm planning a one-month trip to Japan from London this September.\n\nI want to avoid the usual tourist spots and instead explore hidden gems, rural life, and Japan's tech scene — beyond just the cultural highlights.\nCan you create a detailed itinerary that fits this vibe and season?\nPlease include a full daily budget with cost estimates based on where I'll be going.\nAt the end, add some helpful local tips and useful Japanese words or phrases.\nI'd love the final result as a well-designed, fun-looking PDF.",
    },
    {
      icon: Brain,
      title: "AI Professors Outreach",
      description:
        "Identify top AI professors at Imperial College London, analyze their work, and draft personalized outreach emails.",
      prompt:
        "Find the top AI professors at Imperial College London. Research their key publications, online presence, and contact information. Then, draft individualized emails to each professor expressing what you appreciate about their work, mentioning you are a computer science student, and requesting a 5-minute call to discuss AI and your project. Provide a polished PDF containing:\n\n1. A ranked list of the professors with their main research areas.\n2. Summaries of their most influential papers or projects.\n3. Contact details and relevant online profiles.\n4. Personalized email drafts for each professor.",
    },
    {
      icon: Code,
      title: "Project Workflow Flowchart",
      description:
        "Design a complex SaaS onboarding flowchart, and generate HTML/Tailwind code to render it.",
      prompt:
        "Plan out a user signup and onboarding workflow for a SaaS product with multiple decision points (e.g., email verification, plan selection, two-factor authentication, onboarding tutorial). First, describe each step and branch in the workflow, identify dependencies, and explain any edge cases. Then, create a detailed flowchart specification outlining nodes (boxes), arrows (connections), and decision criteria.\n\nFinally, generate the HTML structure and Tailwind CSS needed to render that flowchart visually (including boxes, arrows, labels, and responsive layout). Ensure the code is self-contained so that when included in a simple HTML page with Tailwind imported, it displays the full flowchart.",
    },
    {
      icon: LineChart,
      title: "AI Chatbot Competitive Analysis",
      description:
        "Research top AI chatbots, compare features/pricing, and produce an HTML/Tailwind comparison page.",
      prompt:
        "Identify the top five AI chatbots available today (e.g., ChatGPT, Claude, Gemini, Bard, LLaMA-based services). For each chatbot, use the web scraper to gather information on features, pricing tiers, underlying model, pros, and cons. Then, compare and rank them based on performance, cost-effectiveness, and unique capabilities.\n\nFinally, generate a complete PDF that presents:\n\n1. A comparison table with columns: Chatbot Name, Model, Key Features, Pricing, Strengths, Weaknesses.\n2. A brief written summary above the table explaining the ranking methodology.\n3. Your thoughts on which one is best for what tasks..",
    },
  ];

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ delay: 0.4 }}
      className="max-w-2xl mx-auto px-4 pb-8"
    >
      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
        {examples.map((ex, idx) => (
          <motion.div
            key={ex.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 * (idx + 1) }}
          >
            <ExampleCard
              icon={ex.icon}
              title={ex.title}
              description={ex.description}
              onClick={() => onExampleSelect(ex.prompt)}
            />
          </motion.div>
        ))}
      </div>
    </motion.div>
  );
}
