import { motion } from "framer-motion";
import { LucideIcon } from "lucide-react";
import { useThemeStore } from "../../../lib/theme/store";

interface ExampleCardProps {
  icon: LucideIcon;
  title: string;
  description: string;
  onClick: () => void;
}

export function ExampleCard({
  icon: Icon,
  title,
  description,
  onClick,
}: ExampleCardProps) {
  const isDark = useThemeStore((s) => s.isDark);
  return (
    <motion.button
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      onClick={onClick}
      className={`cursor-pointer rounded-lg p-3 md:p-4 text-left w-full transition-colors hover:bg-opacity-80 ${
        isDark ? "bg-chat-dark-secondary-bg" : "bg-chat-light-secondary-bg"
      }`}
    >
      <div className="flex items-start gap-3">
        <div
          className={`mt-1 p-1 md:p-2 rounded-md ${
            isDark ? "bg-chat-dark-bg" : "bg-chat-light-bg"
          }`}
        >
          <Icon
            size={20}
            className={`w-4 h-4 md:w-5 md:h-5 ${
              isDark ? "text-chat-dark-text" : "text-chat-light-text"
            }`}
          />
        </div>
        <div>
          <h3
            className={`font-medium mb-1 text-sm md:text-base ${
              isDark ? "text-chat-dark-text" : "text-chat-light-text"
            }`}
          >
            {title}
          </h3>
          <p
            className={`text-xs md:text-sm ${
              isDark
                ? "text-chat-dark-text-secondary"
                : "text-chat-light-text-secondary"
            }`}
          >
            {description}
          </p>
        </div>
      </div>
    </motion.button>
  );
}
