// ChatInput/StyleOptions.tsx

import { useEffect, useRef } from "react";
import { useThemeStore } from "../../../lib/theme/store";
import { motion } from "framer-motion";
import { MessageCircle, BookOpen } from "lucide-react";

interface StyleOptionsProps {
  onClose: () => void;
  onSelect: (style: string) => void;
  selectedStyle: string;
  /* allow the ref to be null before the button mounts */
  buttonRef: React.RefObject<HTMLButtonElement | null>;
}

const responseStyles = [
  { icon: MessageCircle, title: "Chat" },
  { icon: BookOpen, title: "Command" },
];

export function StyleOptions({
  onClose,
  onSelect,
  selectedStyle,
  buttonRef,
}: StyleOptionsProps) {
  const popupRef = useRef<HTMLDivElement>(null);
  const isDark = useThemeStore((s) => s.isDark);

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        popupRef.current &&
        !popupRef.current.contains(event.target as Node) &&
        !buttonRef.current?.contains(event.target as Node)
      ) {
        onClose();
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [buttonRef, onClose]);

  return (
    <motion.div
      ref={popupRef}
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 10 }}
      className={`absolute bottom-full left-0 mb-2 rounded-lg shadow-md border p-2 min-w-[140px] z-10 space-y-1 ${
        isDark
          ? "bg-chat-dark-secondary-bg border-chat-dark-border"
          : "bg-chat-light-secondary-bg border-chat-light-border"
      }`}
    >
      {responseStyles.map(({ icon: Icon, title }) => (
        <button
          key={title}
          onClick={() => onSelect(title)}
          className={`w-full flex items-center gap-2 px-4 py-2 text-left rounded-md transition-colors ${
            selectedStyle === title
              ? isDark
                ? "text-chat-dark-text bg-chat-dark-button-hover"
                : "text-chat-light-text bg-chat-light-button-hover"
              : isDark
                ? "text-chat-dark-text-secondary"
                : "text-chat-light-text-secondary"
          } ${isDark ? "hover:bg-chat-dark-button-hover" : "hover:bg-chat-light-button-hover"}`}
        >
          <Icon className="w-4 h-4" />
          <span className="text-sm">{title}</span>
        </button>
      ))}
    </motion.div>
  );
}
