// ChatInput/ModelOptions.tsx

import { useEffect, useRef } from "react";
import { useThemeStore } from "../../../lib/theme/store";
import { motion } from "framer-motion";

interface ModelOptionsProps {
  onClose: () => void;
  onSelect: (model: string) => void;
  selectedModel: string;
  buttonRef: React.RefObject<HTMLButtonElement | null>;
}

const models = [
  { name: "GPT-4.1", desc: "Flagship GPT model for complex tasks" },
  { name: "o3", desc: "Powerful reasoning model" },
  { name: "o4 Mini", desc: "Faster, more affordable reasoning model" },
  { name: "Gemini 2.5 Flash", desc: "Fast responses for quick chats" },
  { name: "Gemini 2.5 Pro", desc: "Google's flagship model." },
];

export function ModelOptions({
  onClose,
  onSelect,
  selectedModel,
  buttonRef,
}: ModelOptionsProps) {
  const popupRef = useRef<HTMLDivElement>(null);
  const isDark = useThemeStore((s) => s.isDark);

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        popupRef.current &&
        !popupRef.current.contains(event.target as Node) &&
        !buttonRef.current?.contains(event.target as Node)
      ) {
        onClose();
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [buttonRef, onClose]);

  return (
    <motion.div
      ref={popupRef}
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 10 }}
      className={`absolute bottom-full left-0 mb-2 rounded-lg shadow-md border p-3 min-w-[300px] z-10 space-y-2 ${
        isDark
          ? "bg-chat-dark-secondary-bg border-chat-dark-border"
          : "bg-chat-light-secondary-bg border-chat-light-border"
      }`}
    >
      {models.map(({ name, desc }) => (
        <button
          key={name}
          onClick={() => onSelect(name)}
          className={`w-full text-left rounded-md px-4 py-2 transition-colors ${
            selectedModel === name
              ? isDark
                ? "text-chat-dark-text bg-chat-dark-button-hover"
                : "text-chat-light-text bg-chat-light-button-hover"
              : isDark
                ? "text-chat-dark-text-secondary"
                : "text-chat-light-text-secondary"
          } ${isDark ? "hover:bg-chat-dark-button-hover" : "hover:bg-chat-light-button-hover"}`}
        >
          <div className="text-base font-medium">{name}</div>
          <div
            className={`text-xs ${
              isDark ? "text-chat-dark-text-secondary" : "text-chat-light-text-secondary"
            }`}
          >
            {desc}
          </div>
        </button>
      ))}
    </motion.div>
  );
}
