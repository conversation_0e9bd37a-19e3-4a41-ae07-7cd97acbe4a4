// src/components/chat/ChatInput/types.ts
export interface Attachment {
  /** Optional local id used during uploads */
  id?: string;
  /** Upload status when originating from the UI */
  status?: "uploading" | "uploaded" | "error";
  /** Download URL (presigned or relative /docs link) */
  url: string;
  /** R2 object key if available */
  key?: string;
  name: string;
  size: string;
  /** File extension */
  ext?: string;
  /** Mime type */
  mime?: string;
}
