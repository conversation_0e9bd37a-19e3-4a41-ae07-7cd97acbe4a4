// src/components/chat/ChatInput/ChatInput.tsx

import {
  FormEvent,
  KeyboardEvent,
  useState,
  useRef,
  useCallback,
  useEffect, // ← import useEffect
} from "react";
import { ChatTextArea } from "./ChatTextArea";
import { ChatToolbar } from "./ChatToolbar";
import { useChatStore } from "../../../lib/chat/store";
import { uploadFile } from "../../../lib/r2/uploadFile"; // your two-fetch util
import { deleteFile } from "../../../lib/r2/deleteFile";
import { formatBytes } from "./formatBytes";
import { Attachment } from "./types";
import { FileAttachments } from "./FileAttachments";
import { useThemeStore } from "../../../lib/theme/store";

import { v4 as uuidv4 } from "uuid"; // install uuid if not already

const ALLOWED_EXTENSIONS = ["docx", "csv", "pptx", "pdf", "png", "jpg", "jpeg"];

interface ChatInputProps {
  message: string;
  onMessageChange: (value: string) => void;
  // now attachments is an array of {key, name}
  // pass the selected model through to the send handler
  onSend: (message: string, attachments: Attachment[], model: string) => void;
}

export function ChatInput({
  message,
  onMessageChange,
  onSend,
}: ChatInputProps) {
  const isDark = useThemeStore((s) => s.isDark);
  const [isStyleOpen, setIsStyleOpen] = useState(false);
  const [isModelOpen, setIsModelOpen] = useState(false);
  const [selectedModel, setSelectedModel] = useState("Gemini 2.5 Flash");
  const currentMessageMode = useChatStore((s) => s.currentMessageMode);
  const setCurrentMessageMode = useChatStore((s) => s.setCurrentMessageMode);

  const placeholder =
    currentMessageMode === "command"
      ? "Command the hive... "
      : "Chat with the hive... ";

  const isProcessing = useChatStore((s) => {
    const id = s.currentChatId;
    return id ? s.processingMap[id] ?? false : false;
  });

  const textAreaRef = useRef<HTMLTextAreaElement>(null);
  // ⬇️ hold the array of uploaded file URLs
  const [attachments, setAttachments] = useState<Attachment[]>([]);
  // 🔍 Debug: log attachments whenever they change
  useEffect(() => {
    console.log("Attachments updated:", attachments);
  }, [attachments]);

  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFiles = useCallback(
    async (e: React.ChangeEvent<HTMLInputElement>) => {
      const files = e.target.files;
      if (!files?.length) return;

      // filter unsupported files
      const validFiles = Array.from(files).filter((f) => {
        const ext = f.name.split(".").pop()!.toLowerCase();
        return ALLOWED_EXTENSIONS.includes(ext);
      });
      if (validFiles.length !== files.length) {
        alert("Some files were skipped because the format is not supported.");
      }
      if (validFiles.length === 0) {
        e.target.value = "";
        return;
      }

      // 1️⃣ Create placeholders immediately
      const placeholders: Attachment[] = validFiles.map((file) => {
        const ext = file.name.split(".").pop()!.toLowerCase();
        return {
          id: uuidv4(),
          status: "uploading",
          url: "",
          key: "",
          name: file.name,
          size: formatBytes(file.size),
          ext,
          mime: file.type,
        };
      });
      setAttachments((prev) => [...prev, ...placeholders]);

      try {
        // 2️⃣ Upload each and update its placeholder
        await Promise.all(
          placeholders.map(async (ph, idx) => {
            const file = validFiles[idx];
            try {
              const { url, key } = await uploadFile(file);
              setAttachments((prev) =>
                prev.map((a) =>
                  a.id === ph.id ? { ...a, url, key, status: "uploaded" } : a
                )
              );
            } catch {
              setAttachments((prev) =>
                prev.map((a) =>
                  a.id === ph.id ? { ...a, status: "error" } : a
                )
              );
            }
          })
        );
      } catch (err) {
        console.error("Upload failed", err);
      } finally {
        e.target.value = "";
      }
    },
    []
  );

  // ─── auto-resize on every value change ─────────────────────────────
  useEffect(() => {
    const ta = textAreaRef.current;
    if (!ta) return;
    // reset then expand to fit
    ta.style.height = "auto";
    ta.style.height = `${ta.scrollHeight}px`;
  }, [message]);

  // ─── whenever message fills (e.g. example click), focus textarea ────
  useEffect(() => {
    if (message && textAreaRef.current) {
      textAreaRef.current.focus();
    }
  }, [message]);

  const handleSubmit = useCallback(
    (e: FormEvent) => {
      e.preventDefault();
      // don’t allow sending if already processing
      if (isProcessing) return;
      if (!message.trim() && attachments.length === 0) return;
      // send raw URLs along with the selected model
      onSend(message, attachments, selectedModel);
      onMessageChange("");
      setAttachments([]);
    },
    [message, attachments, selectedModel, onSend, onMessageChange, isProcessing]
  );
  const handleKeyDown = useCallback(
    (e: KeyboardEvent) => {
      // Block Enter if sending is in progress
      if (isProcessing) {
        if (e.key === "Enter") e.preventDefault();
        return;
      }
      if (e.key === "Enter" && !e.shiftKey) {
        e.preventDefault();
        handleSubmit(e as unknown as FormEvent);
      }
    },
    [handleSubmit, isProcessing]
  );

  const handleStyleSelect = useCallback(
    (style: string) => {
      if (style === "Chat") {
        setCurrentMessageMode("chat");
      } else {
        setCurrentMessageMode("command");
      }
      setIsStyleOpen(false);
    },
    [setCurrentMessageMode]
  );

  const handleModelSelect = useCallback((model: string) => {
    setSelectedModel(model);
    setIsModelOpen(false);
  }, []);

  return (
    <div className={isDark ? "bg-chat-dark-bg" : "bg-chat-light-bg"}>
      <form onSubmit={handleSubmit} className="max-w-3xl mx-auto px-4 pb-4">
        <div
          className={`relative flex flex-col rounded-2xl ${
            isDark ? "bg-chat-dark-input-bg" : "bg-chat-light-input-bg"
          }`}
        >
          {/* 📎 Attachments preview */}
          {attachments.length > 0 && (
            <FileAttachments
              attachments={attachments}
              onRemove={async (key) => {
                try {
                  // 1️⃣ delete from R2
                  await deleteFile(key);
                } catch (e) {
                  console.error("Delete failed", e);
                  return;
                }
                // 2️⃣ remove from UI state
                setAttachments((prev) => prev.filter((a) => a.key !== key));
              }}
            />
          )}
          <ChatTextArea
            ref={textAreaRef}
            value={message}
            onChange={onMessageChange}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
          />

          <ChatToolbar
            onAttach={() => fileInputRef.current?.click()}
            onStyleToggle={() => setIsStyleOpen((open) => !open)}
            onModelToggle={() => setIsModelOpen((open) => !open)}
            onSend={handleSubmit}
            // Disable when processing, no content, or uploads still in progress
            isDisabled={
              isProcessing ||
              (!message.trim() && attachments.length === 0) ||
              attachments.some((a) => a.status !== "uploaded")
            }
            isProcessing={isProcessing}
            selectedStyle={
              currentMessageMode === "command" ? "Command" : "Chat"
            }
            selectedModel={selectedModel}
            isStyleOpen={isStyleOpen}
            isModelOpen={isModelOpen}
            onStyleSelect={handleStyleSelect}
            onModelSelect={handleModelSelect}
            showModelToggle={currentMessageMode === "chat"}
          />

          {/* hidden file picker */}
          <input
            ref={fileInputRef}
            type="file"
            hidden
            multiple
            accept=".pdf,.docx,.pptx,.csv,.png,.jpg,.jpeg"
            onChange={handleFiles}
          />
        </div>
      </form>
    </div>
  );
}
