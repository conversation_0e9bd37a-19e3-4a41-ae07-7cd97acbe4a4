// src/components/chat/ChatInput/FileAttachments.tsx
import { X } from "lucide-react";
import { Attachment } from "./types";

// ← Import your static SVGs from public/docIcons
import csvIcon from "/docIcons/csv.svg";
import docIcon from "/docIcons/doc.svg";
import pdfIcon from "/docIcons/pdf.svg";
import pptIcon from "/docIcons/ppt.svg";
import txtIcon from "/docIcons/txt.svg";
import xlsIcon from "/docIcons/xls.svg";
import fileIcon from "/docIcons/file.svg";
import { useThemeStore } from "../../../lib/theme/store";

const iconMap: Record<string, string> = {
  csv: csvIcon,
  doc: docIcon,
  pdf: pdfIcon,
  ppt: pptIcon,
  txt: txtIcon,
  xls: xlsIcon,
  file: fileIcon, // fallback
};

export function FileAttachments({
  attachments,
  onRemove,
  readOnly = false,
  reverse = false,
}: {
  attachments: Attachment[];
  onRemove: (key: string) => void;
  readOnly?: boolean;
  reverse?: boolean;
}) {
  const isDark = useThemeStore((s) => s.isDark);
  return (
    <div
      className={`flex gap-3 overflow-x-auto w-11/12 py-1.5 mx-4${
        reverse ? " flex-row-reverse" : ""
      }`}
    >
      {attachments.map(
        ({ id, status, url, name, size, ext, mime, key: objectKey }) => {
          const fileExt = ext || name.split(".").pop()!.toLowerCase();
          const isImage =
            (mime && mime.startsWith("image/")) ||
            ["png", "jpg", "jpeg", "gif", "webp"].includes(fileExt);
          const iconSrc = iconMap[fileExt] || iconMap["file"];

          return (
            <div
              key={id || objectKey || name}
              className={`group relative flex-none w-60 h-16 rounded-lg overflow-hidden flex items-center border ${
                isDark
                  ? "bg-attachment-card-bg-dark border-chat-dark-border"
                  : "bg-attachment-card-bg-light border-chat-light-border"
              }`}
            >
              {/* uploading skeleton vs real */}
              {status === "uploading" ? (
                <>
                  <div
                    className={`flex-none m-2 w-10 h-10 rounded animate-pulse ${
                      isDark ? "bg-gray-700" : "bg-gray-300"
                    }`}
                  />
                  <div className="flex-1 mr-4 space-y-2">
                    <div
                      className={`h-4 rounded w-3/4 animate-pulse ${
                        isDark ? "bg-gray-700" : "bg-gray-300"
                      }`}
                    />
                    <div
                      className={`h-3 rounded w-1/2 animate-pulse ${
                        isDark ? "bg-gray-700" : "bg-gray-300"
                      }`}
                    />
                  </div>
                </>
              ) : (
                <>
                  {/* floating preview or icon */}
                  <div className="flex-none m-2 w-10 h-10 rounded flex items-center justify-center overflow-hidden ">
                    {isImage ? (
                      <img
                        src={url}
                        alt={name}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <img src={iconSrc} alt={fileExt} className="w-10 h-10" />
                    )}
                  </div>

                  {/* file info */}
                  <div className="flex-1 mr-4 flex flex-col justify-center overflow-hidden">
                    <div
                      className={`text-sm font-medium truncate ${
                        isDark ? "text-chat-dark-text" : "text-chat-light-text"
                      }`}
                    >
                      {name}
                    </div>
                    <div
                      className={`text-xs uppercase ${
                        isDark
                          ? "text-chat-dark-text-secondary"
                          : "text-chat-light-text-secondary"
                      }`}
                    >
                      {fileExt.toUpperCase()} · {size}
                    </div>
                  </div>
                </>
              )}

              {/* remove button on hover */}
              {!readOnly && (
                <button
                  type="button"
                  onClick={() => {
                    if (objectKey !== undefined) onRemove(objectKey);
                  }}
                  className={`absolute top-2 right-2 opacity-0 group-hover:opacity-100 rounded-full p-1 transition-opacity cursor-pointer ${
                    isDark
                      ? "bg-chat-dark-bg text-chat-dark-text"
                      : "bg-chat-light-bg text-chat-light-text"
                  }`}
                >
                  <X className="w-4 h-4" strokeWidth={1.5} />
                </button>
              )}
            </div>
          );
        }
      )}
    </div>
  );
}
