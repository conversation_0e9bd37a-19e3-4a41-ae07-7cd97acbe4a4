// src/components/chat/ChatInput/ChatTextArea.tsx

import React, { forwardRef, useCallback } from "react";
import { useThemeStore } from "../../../lib/theme/store";

interface ChatTextAreaProps {
  value: string;
  onChange: (value: string) => void;
  onKeyDown: (e: React.KeyboardEvent) => void;
  placeholder?: string;
}

export const ChatTextArea = forwardRef<HTMLTextAreaElement, ChatTextAreaProps>(
  ({ value, onChange, onKeyDown, placeholder }, ref) => {
    // Auto-resize textarea as content grows
    const isDark = useThemeStore((s) => s.isDark);
    const handleInput = useCallback(
      (e: React.ChangeEvent<HTMLTextAreaElement>) => {
        const textarea = e.target;
        onChange(textarea.value);

        // Reset height to auto to accurately calculate new height
        textarea.style.height = "auto";
        // Set new height based on scrollHeight
        textarea.style.height = `${textarea.scrollHeight}px`;
      },
      [onChange]
    );

    return (
      <textarea
        ref={ref}
        value={value}
        onChange={handleInput}
        onKeyDown={onKeyDown}
        placeholder={placeholder ?? "Command the hive... "}
        rows={1}
        className={`w-full px-4 pt-3 bg-transparent resize-none focus:outline-none min-h-[44px] max-h-[200px] overflow-y-auto ${
          isDark
            ? "text-chat-dark-text placeholder-chat-dark-text-secondary"
            : "text-chat-light-text placeholder-chat-light-text-secondary"
        }`}
      />
    );
  }
);
