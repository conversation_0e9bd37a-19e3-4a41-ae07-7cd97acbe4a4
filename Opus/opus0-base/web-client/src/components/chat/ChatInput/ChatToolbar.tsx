// src/components/chat/ChatInput/ChatToolbar.tsx
import React, { useRef } from "react";
import { useThemeStore } from "../../../lib/theme/store";
import { ArrowU<PERSON>, Paperclip } from "lucide-react";
import { StyleOptions } from "./StyleOptions";
import { ModelOptions } from "./ModelOptions";

interface ChatToolbarProps {
  onAttach: () => void;
  onStyleToggle: () => void;
  onModelToggle: () => void;
  onSend: (e: React.FormEvent) => void;
  isDisabled: boolean;
  isProcessing: boolean;
  selectedStyle: string;
  selectedModel: string;
  isStyleOpen: boolean;
  isModelOpen: boolean;
  onStyleSelect: (style: string) => void;
  onModelSelect: (model: string) => void;
  showModelToggle: boolean;
}

export function ChatToolbar({
  onAttach,
  onStyleToggle,
  onModelToggle,
  onSend,
  isDisabled,
  isProcessing,
  selectedStyle,
  selectedModel,
  isStyleOpen,
  isModelOpen,
  onStyleSelect,
  onModelSelect,
  showModelToggle,
}: ChatToolbarProps) {
  const styleButtonRef = useRef<HTMLButtonElement>(null);
  const modelButtonRef = useRef<HTMLButtonElement>(null);
  const isDark = useThemeStore((s) => s.isDark);

  return (
    <div className="flex items-center justify-between px-4 pb-2 pt-0.5">
      {/* Left group: attach + style toggle + model toggle */}
      <div className="flex items-center gap-3">
        <button
          type="button"
          disabled={isProcessing}
          onClick={onAttach}
          className={`p-2 rounded-md cursor-pointer transition-colors disabled:opacity-50 disabled:cursor-not-allowed ${
            isDark
              ? "hover:bg-chat-dark-button-hover text-chat-dark-text"
              : "hover:bg-chat-light-button-hover text-chat-light-text"
          }`}
        >
          <Paperclip size={20} strokeWidth={2} />
        </button>

        <div className="relative">
          <button
            ref={styleButtonRef}
            type="button"
            onClick={onStyleToggle}
            className={`px-3 py-2 text-base cursor-pointer rounded-md transition-colors ${
              isDark
                ? "hover:bg-chat-dark-button-hover text-chat-dark-text"
                : "hover:bg-chat-light-button-hover text-chat-light-text"
            }`}
          >
            {selectedStyle}
          </button>

          {isStyleOpen && (
            <StyleOptions
              onClose={onStyleToggle}
              onSelect={onStyleSelect}
              selectedStyle={selectedStyle}
              buttonRef={styleButtonRef}
            />
          )}
        </div>

        {showModelToggle && (
          <div className="relative">
            <button
              ref={modelButtonRef}
              type="button"
              onClick={onModelToggle}
              className={`px-3 py-2 text-base cursor-pointer rounded-md transition-colors ${
                isDark
                  ? "hover:bg-chat-dark-button-hover text-chat-dark-text"
                  : "hover:bg-chat-light-button-hover text-chat-light-text"
              }`}
            >
              {selectedModel}
            </button>

            {isModelOpen && (
              <ModelOptions
                onClose={onModelToggle}
                onSelect={onModelSelect}
                selectedModel={selectedModel}
                buttonRef={modelButtonRef}
              />
            )}
          </div>
        )}
      </div>

      {/* Send button */}
      <button
        type="submit"
        disabled={isDisabled}
        onClick={onSend}
        className={`p-2.5 rounded-xl cursor-pointer transition-colors disabled:opacity-50 disabled:cursor-not-allowed ${
          isDisabled
            ? isDark
              ? "bg-chat-dark-button-hover text-chat-dark-text-secondary"
              : "bg-chat-light-button-hover text-chat-light-text-secondary"
            : isDark
              ? "bg-white text-chat-dark-bg"
              : "bg-white text-chat-light-text"
        }`}
      >
        <ArrowUp size={20} strokeWidth={3} />
      </button>
    </div>
  );
}
