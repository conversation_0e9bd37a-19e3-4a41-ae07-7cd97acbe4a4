// src/components/chat/Runtime/RuntimeCard.tsx
import { useState, useMemo, useRef, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  CheckCircleIcon,
  ClockIcon,
  GlobeAltIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  MagnifyingGlassIcon,
} from "@heroicons/react/24/outline";
import type { RuntimeLog } from "../../../lib/chat/types";
import { useThemeStore } from "../../../lib/theme/store";

interface RuntimeCardProps {
  log: RuntimeLog;
}

type LogItem =
  | { type: "search"; query: string }
  | { type: "links"; urls: string[] }
  | { type: "pdf"; url: string }
  | { type: "xlsx"; url: string }
  | { type: "pptx"; url: string }
  | { type: "text"; text: string };

export function RuntimeCard({ log }: RuntimeCardProps) {
  const [isOpen, setIsOpen] = useState(true);
  const isDone = log.status === "done";
  const streamRef = useRef<HTMLPreElement>(null);
  const isDark = useThemeStore((s) => s.isDark);

  useEffect(() => {
    if (streamRef.current) {
      streamRef.current.scrollTop = streamRef.current.scrollHeight;
    }
  }, [log.stream]);

  const { subtaskDesc, agentName, items } = useMemo(() => {
    const allLines = log.description
      .split("\n")
      .map((l) => l.trim())
      .filter(Boolean);

    const [firstLine = log.subtaskId, ...rest] = allLines;

    let detectedAgent = "";
    const pureLines = rest.map((line) => {
      const m = line.match(/^<([^>]+)>:\s*(.*)$/);
      if (m) {
        if (!detectedAgent) detectedAgent = m[1];
        return m[2];
      }
      return line;
    });

    const URL_REGEX = /(https?:\/\/[^"'’]+)/g;

    const items: LogItem[] = pureLines.flatMap((line) => {
      const searchMatch = line.match(
        /^Searching web for\s+["“”]?(.*?)["“”]?$/i
      );
      if (searchMatch) {
        const query = searchMatch[1].trim().replace(/^['“”]+|['“”]+$/g, "");
        return { type: "search", query };
      }

      const pdfMatch = line.match(/^Generated PDF:\s*(\S+)/i);
      if (pdfMatch) {
        return { type: "pdf", url: pdfMatch[1] };
      }

      const xlsxMatch = line.match(/^Generated XLSX:\s*(\S+)/i);
      if (xlsxMatch) {
        return { type: "xlsx", url: xlsxMatch[1] };
      }

      const pptxMatch = line.match(/^Generated PPTX:\s*(\S+)/i);
      if (pptxMatch) {
        return { type: "pptx", url: pptxMatch[1] };
      }

      if (/^Selected links?:/i.test(line)) {
        const after = line.replace(/^Selected links?:/i, "").trim();
        let urls: string[] = [];
        try {
          const parsed = JSON.parse(after.replace(/’/g, "'"));
          if (
            Array.isArray(parsed) &&
            parsed.every((u) => typeof u === "string")
          ) {
            urls = parsed;
          }
        } catch {
          urls = Array.from(after.matchAll(URL_REGEX)).map((m) => m[0]);
        }
        if (urls.length) {
          return { type: "links", urls };
        }
      }

      return { type: "text", text: line };
    });

    // filter out empty-text items (e.g., lines that yielded "" after parsing)
    const filteredItems = items.filter(
      (item) => !(item.type === "text" && item.text.trim() === "")
    );

    return {
      subtaskDesc: firstLine,
      agentName: detectedAgent || "Processing",
      items: filteredItems,
    };
  }, [log.description, log.subtaskId]);

  return (
    <div
      className={`self-start mb-4 w-full max-w-lg rounded-2xl shadow-sm overflow-hidden ${
        isDark ? "bg-runtime-dark-card-bg" : "bg-runtime-light-card-bg"
      }`}
    >
      {/* header */}
      <div className="flex items-center justify-between px-4 py-2">
        <div className="flex items-center space-x-2">
          {isDone ? (
            <CheckCircleIcon className="h-5 w-5 text-green-400 flex-shrink-0" />
          ) : (
            <ClockIcon
              className={`h-5 w-5 flex-shrink-0 ${
                isDark ? "text-chat-dark-text" : "text-chat-light-text"
              }`}
            />
          )}
          <h4
            className={`text-sm font-semibold ${
              isDark ? "text-chat-dark-text" : "text-chat-light-text"
            }`}
          >
            {agentName}
          </h4>
        </div>
        <button
          onClick={() => setIsOpen((o) => !o)}
          aria-label={isOpen ? "Collapse logs" : "Expand logs"}
          className="p-1 cursor-pointer"
        >
          {isOpen ? (
            <ChevronUpIcon
              className={`h-5 w-5 ${
                isDark ? "text-chat-dark-text" : "text-chat-light-text"
              }`}
            />
          ) : (
            <ChevronDownIcon
              className={`h-5 w-5 ${
                isDark ? "text-chat-dark-text" : "text-chat-light-text"
              }`}
            />
          )}
        </button>
      </div>

      {/* subtask description */}
      <div className="px-4">
        <p
          className={`text-sm font-medium mb-2 ${
            isDark ? "text-chat-dark-text" : "text-chat-light-text"
          }`}
        >
          {subtaskDesc}
        </p>
      </div>

      {/* toggled logs */}
      <AnimatePresence initial={false}>
        {isOpen && items.length > 0 && (
          <motion.div
            key="content"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.15 }}
            className="px-4 pb-4"
          >
            <div className="space-y-3">
              {items.map((item, idx) => {
                const delay = idx * 0.05;
                const common = {
                  initial: { opacity: 0, y: 4 },
                  animate: { opacity: 1, y: 0 },
                  transition: { duration: 0.15, delay },
                } as const;

                // ── NEW: “Writing the Content” header ─────────────
                if (
                  item.type === "text" &&
                  item.text === "Writing the Content"
                ) {
                  return (
                    <motion.div
                      key={idx}
                      {...common}
                      className={`text-sm font-semibold ${
                        isDark
                          ? "text-chat-dark-accent"
                          : "text-chat-light-accent"
                      }`}
                    >
                      {item.text}
                    </motion.div>
                  );
                }

                // ── NEW: any HTML: log becomes an indented sub-item ─
                if (item.type === "text" && item.text.startsWith("HTML:")) {
                  const heading = item.text.replace(/^HTML:\s*/, "");
                  return (
                    <motion.div
                      key={idx}
                      {...common}
                      className="flex items-start space-x-2 ml-6"
                    >
                      <span
                        className={`mt-1 text-xs ${
                          isDark
                            ? "text-chat-dark-text"
                            : "text-chat-light-text"
                        }`}
                      >
                        ▪︎
                      </span>
                      <span
                        className={`italic text-sm ${
                          isDark
                            ? "text-chat-dark-text"
                            : "text-chat-light-text"
                        }`}
                      >
                        {heading}
                      </span>
                    </motion.div>
                  );
                }

                switch (item.type) {
                  case "search": {
                    const firstSearchIndex = items.findIndex(
                      (i) => i.type === "search"
                    );
                    if (idx !== firstSearchIndex) {
                      return null;
                    }
                    const allQueries = items
                      .filter((i) => i.type === "search")
                      .map(
                        (i) => (i as { type: "search"; query: string }).query
                      );
                    return (
                      <motion.div key={idx} {...common} className="space-y-1">
                        <div className="flex items-center space-x-2">
                          <MagnifyingGlassIcon
                            className={`h-3 w-3 flex-shrink-0 ${
                              isDark
                                ? "text-chat-dark-accent"
                                : "text-chat-light-accent"
                            }`}
                          />
                          <span
                            className={`text-xs font-medium ${
                              isDark
                                ? "text-chat-dark-text"
                                : "text-chat-light-text"
                            }`}
                          >
                            Search Queries:
                          </span>
                        </div>
                        <div className="ml-6 flex flex-wrap gap-2">
                          {allQueries.map((q, qidx) => (
                            <span
                              key={qidx}
                              className={`inline-flex items-center px-2 py-0.5 rounded-full text-[10px] font-medium transition ${
                                isDark
                                  ? "bg-chat-dark-accent/10 text-chat-dark-text hover:bg-chat-dark-accent/20"
                                  : "bg-chat-light-accent/10 text-chat-light-text hover:bg-chat-light-accent/20"
                              }`}
                            >
                              {q}
                            </span>
                          ))}
                        </div>
                      </motion.div>
                    );
                  }

                  case "links":
                    return (
                      <motion.div key={idx} {...common} className="space-y-1">
                        <div className="flex items-center space-x-2">
                          <GlobeAltIcon
                            className={`h-3 w-3 flex-shrink-0 ${
                              isDark
                                ? "text-chat-dark-accent"
                                : "text-chat-light-accent"
                            }`}
                          />
                          <span
                            className={`text-xs font-medium ${
                              isDark
                                ? "text-chat-dark-text"
                                : "text-chat-light-text"
                            }`}
                          >
                            Searched the web:
                          </span>
                        </div>
                        <div className="ml-6 flex flex-wrap gap-2">
                          {item.urls.map((url, uidx) => {
                            let label: string;
                            try {
                              label = new URL(url).hostname.replace(
                                /^www\./,
                                ""
                              );
                            } catch {
                              label = url;
                            }
                            return (
                              <a
                                key={uidx}
                                href={url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className={`inline-flex items-center px-2 py-0.5 rounded-full text-[10px] font-medium transition ${
                                  isDark
                                    ? "bg-chat-dark-accent/10 text-chat-dark-accent hover:bg-chat-dark-accent/20"
                                    : "bg-chat-light-accent/10 text-chat-light-accent hover:bg-chat-light-accent/20"
                                }`}
                              >
                                {label}
                              </a>
                            );
                          })}
                        </div>
                      </motion.div>
                    );

                  case "pdf":
                    return (
                      <motion.div
                        key={idx}
                        {...common}
                        className="flex items-start space-x-2"
                      >
                        <span
                          className={`mt-1 text-xs ${
                            isDark
                              ? "text-chat-dark-text"
                              : "text-chat-light-text"
                          }`}
                        >
                          •
                        </span>
                        <span
                          className={`text-sm ${
                            isDark
                              ? "text-chat-dark-text"
                              : "text-chat-light-text"
                          }`}
                        >
                          Generated PDF:{" "}
                          <a
                            href={item.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className={`underline hover:text-opacity-80 transition ${
                              isDark
                                ? "text-chat-dark-accent"
                                : "text-chat-light-accent"
                            }`}
                          >
                            link
                          </a>
                        </span>
                      </motion.div>
                    );

                  case "xlsx":
                    return (
                      <motion.div
                        key={idx}
                        {...common}
                        className="flex items-start space-x-2"
                      >
                        <span className="mt-1 text-xs text-chat-light-text dark:text-chat-dark-text">
                          •
                        </span>
                        <span className="text-sm text-chat-light-text dark:text-chat-dark-text">
                          Generated XLSX:{" "}
                          <a
                            href={item.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="underline text-chat-light-accent dark:text-chat-dark-accent hover:text-opacity-80 transition"
                          >
                            link
                          </a>
                        </span>
                      </motion.div>
                    );

                  case "pptx":
                    return (
                      <motion.div
                        key={idx}
                        {...common}
                        className="flex items-start space-x-2"
                      >
                        <span className="mt-1 text-xs text-chat-light-text dark:text-chat-dark-text">
                          •
                        </span>
                        <span className="text-sm text-chat-light-text dark:text-chat-dark-text">
                          Generated PPTX:{" "}
                          <a
                            href={item.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="underline text-chat-light-accent dark:text-chat-dark-accent hover:text-opacity-80 transition"
                          >
                            link
                          </a>
                        </span>
                      </motion.div>
                    );

                  default:
                    return (
                      <motion.div
                        key={idx}
                        {...common}
                        className="flex items-start space-x-2"
                      >
                        {/* bullet */}
                        <span
                          className={`mt-1 text-xs ${
                            isDark
                              ? "text-chat-dark-text"
                              : "text-chat-light-text"
                          }`}
                        >
                          •
                        </span>
                        <span
                          className={`text-sm ${
                            isDark
                              ? "text-chat-dark-text"
                              : "text-chat-light-text"
                          }`}
                        >
                          {item.text}
                        </span>
                      </motion.div>
                    );
                }
              })}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
      <AnimatePresence initial={false}>
        {isOpen && log.stream && (
          <motion.div
            key="stream"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.15 }}
            className="px-4 pb-4"
          >
            <div
              className={`rounded-lg border p-3 space-y-2 ${
                isDark
                  ? "border-chat-dark-border bg-runtime-dark-stream-bg"
                  : "border-chat-light-border bg-runtime-light-stream-bg"
              }`}
            >
              <span
                className={`block text-[10px] font-semibold uppercase tracking-wide ${
                  isDark
                    ? "text-chat-dark-text-secondary"
                    : "text-chat-light-text-secondary"
                }`}
              >
                Runtime Output
              </span>
              <pre
                ref={streamRef}
                className={`font-mono text-xs whitespace-pre-wrap max-h-60 overflow-y-auto ${
                  isDark ? "text-chat-dark-text" : "text-chat-light-text"
                }`}
              >
                {log.stream}
              </pre>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
