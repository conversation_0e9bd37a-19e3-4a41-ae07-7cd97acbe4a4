// src/components/chat/Runtime/RuntimeGroup.tsx
import { useState } from "react";
import { useThemeStore } from "../../../lib/theme/store";
import { motion, AnimatePresence } from "framer-motion";
import { ChevronRightIcon } from "@heroicons/react/24/outline";
import type { RuntimeLog } from "../../../lib/chat/types";
import { RuntimeCard } from "./RuntimeCard";

interface RuntimeGroupProps {
  logs: RuntimeLog[];
}

export function RuntimeGroup({ logs }: RuntimeGroupProps) {
  const [open, setOpen] = useState(true);
  const isDark = useThemeStore((s) => s.isDark);

  if (logs.length === 0) return null;

  return (
    <div className="w-full">
      <motion.button
        onClick={() => setOpen((o) => !o)}
        aria-label={open ? "Collapse runtime logs" : "Expand runtime logs"}
        whileTap={{ scale: 0.97 }}
        className={`flex items-center gap-2 rounded-md text-sm font-medium px-2 py-1 transition-colors cursor-pointer ${
          isDark
            ? "hover:bg-chat-dark-button-hover text-chat-dark-text"
            : "hover:bg-chat-light-button-hover text-chat-light-text"
        }`}
      >
        <motion.span
          animate={{ rotate: open ? 90 : 0 }}
          transition={{ duration: 0.2 }}
          className="flex"
        >
          <ChevronRightIcon className="h-4 w-4" />
        </motion.span>
        <span>{open ? "Hide Runtime Logs" : "Show Runtime Logs"}</span>
      </motion.button>

      <AnimatePresence initial={false}>
        {open && (
          <motion.div
            key="logs"
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: "auto", opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.2, ease: "easeInOut" }}
            className="overflow-hidden mt-2 space-y-2"
          >
            {logs.map((log) => (
              <RuntimeCard key={log.id} log={log} />
            ))}
          </motion.div>
        )}
        {/* {!open && <span className="text-sm">Runtime Logs</span>} */}
      </AnimatePresence>
    </div>
  );
}
