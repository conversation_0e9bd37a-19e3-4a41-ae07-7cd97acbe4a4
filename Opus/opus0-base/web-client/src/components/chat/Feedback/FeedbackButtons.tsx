import { useState } from "react";
import { ThumbsUp, ThumbsDown } from "lucide-react";
import { useSocketChat } from "../../../lib/chat/useSocketChat";
import type { ChatId, MessageId } from "../../../lib/chat/types";

interface FeedbackButtonsProps {
  chatId: ChatId;
  messageId: MessageId;
}

export function FeedbackButtons({ chatId, messageId }: FeedbackButtonsProps) {
  const { sendFeedback } = useSocketChat();
  const [choice, setChoice] = useState<"up" | "down" | null>(null);

  const handle = (val: "up" | "down") => {
    if (choice) return;
    setChoice(val);
    sendFeedback(chatId, messageId, val);
  };

  return (
    <div className="flex gap-2 mt-2">
      <button
        onClick={() => handle("up")}
        disabled={Boolean(choice)}
        aria-label="Thumbs up"
        className={
          "p-1 rounded-md  " +
          (choice === "up" ? "text-green-500 " : "text-white ") +
          (!choice ? "hover:cursor-pointer" : "")
        }
      >
        <ThumbsUp size={18} />
      </button>
      <button
        onClick={() => handle("down")}
        disabled={Boolean(choice)}
        aria-label="Thumbs down"
        className={
          "p-1 rounded-md  " +
          (choice === "down" ? "text-red-500 " : "text-white ") +
          (!choice ? "hover:cursor-pointer" : "")
        }
      >
        <ThumbsDown size={18} />
      </button>
    </div>
  );
}
