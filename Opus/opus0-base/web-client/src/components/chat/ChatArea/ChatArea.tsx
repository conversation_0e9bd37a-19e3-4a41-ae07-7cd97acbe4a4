// src/components/chat/ChatArea/ChatArea.tsx
import { useState, useCallback, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { TitleBar } from "../TitleBar/TitleBar";
import { Welcome } from "../Welcome/Welcome";
import { Examples } from "../../chat/Examples/Examples";
import { ChatInput } from "../../chat/ChatInput/ChatInput";
import { MessageList } from "../Messages/MessageList";
import { getFirstWords } from "../../../lib/chat/utils";
import { useChatStore, useAuthStore } from "../../../lib/chat/store";
import { useThemeStore } from "../../../lib/theme/store";
import { useSocketChat } from "../../../lib/chat/useSocketChat";
import { ChevronDownIcon } from "@heroicons/react/24/outline";
import type { Attachment } from "../ChatInput/types";
import type { ChatId } from "../../../lib/chat/types";

export function ChatArea({
  isMobile = false,
  isMenuOpen = false,
  onMenuToggle,
}: {
  isMobile?: boolean;
  isMenuOpen?: boolean;
  onMenuToggle?: () => void;
}) {
  const {
    chats,
    currentChatId,
    pendingChatId,
    createChat,
    confirmPendingChat,
    updateChat,
    addMessage,
    setPlanningForChat,
  } = useChatStore();
  const { sendMessage } = useSocketChat();
  const userName = useAuthStore((s) => s.userName);
  const inputValue = useChatStore((s) => {
    const id = s.currentChatId;
    return id ? s.inputMap[id] ?? "" : "";
  });
  const setInput = useChatStore((s) => s.setInput);

  // Autofill handler for examples
  const handleExampleSelect = useCallback(
    (prompt: string) => {
      if (currentChatId) setInput(currentChatId, prompt);
    },
    [currentChatId, setInput]
  );

  const currentChat = chats.find((c) => c.id === currentChatId);
  const showWelcome = !currentChat || currentChatId === pendingChatId;

  // scroll ref
  const scrollRef = useRef<HTMLDivElement>(null);

  // track whether user has scrolled away from bottom
  const [showScrollButton, setShowScrollButton] = useState(false);
  const isDark = useThemeStore((s) => s.isDark);

  const handleScroll = useCallback(() => {
    if (!scrollRef.current) return;
    const { scrollTop, scrollHeight, clientHeight } = scrollRef.current;
    const atBottom = scrollTop + clientHeight >= scrollHeight - 20;
    setShowScrollButton(!atBottom);
  }, []);

  const scrollToBottom = useCallback(() => {
    if (!scrollRef.current) return;
    scrollRef.current.scrollTo({
      top: scrollRef.current.scrollHeight,
      behavior: "smooth",
    });
  }, []);

  const handleSendMessage = useCallback(
    (msg: string, attachments: Attachment[] = [], model: string) => {
      let chatId = currentChatId;
      if (!chatId) {
        chatId = createChat();
      }

      const isNewChat = pendingChatId === chatId;
      if (isNewChat) {
        confirmPendingChat(chatId);
      }

      const uploaded = attachments.filter(
        (a): a is Attachment & { key: string } =>
          a.status === "uploaded" && Boolean(a.key)
      );

      const messageId = addMessage(chatId, {
        type: "user",
        content: msg,
        timestamp: new Date(),
        chatId,
        attachments: uploaded,
      });
      if (isNewChat) {
        const autoTitle = getFirstWords(msg, 10);
        updateChat(chatId, { title: autoTitle });
      }
      setPlanningForChat(chatId, messageId);

      // send one event, with only successfully uploaded files
      sendMessage(
        chatId,
        msg,
        uploaded.map(({ key, name, size }) => ({ key, name, size })),
        model
      );
      setInput(chatId, ""); // clear input
    },
    [
      currentChatId,
      pendingChatId,
      createChat,
      confirmPendingChat,
      addMessage,
      updateChat,
      sendMessage,
      setPlanningForChat,
      setInput,
    ]
  );

  const handleTitleChange = useCallback(
    (newTitle: string) => {
      if (currentChatId) updateChat(currentChatId, { title: newTitle });
    },
    [currentChatId, updateChat]
  );

  return (
    <motion.div
      layout
      className={`relative flex flex-col h-full z-0 ${
        isDark ? "bg-chat-dark-bg" : "bg-chat-light-bg"
      }`}
      style={{ height: "100dvh" }}
    >
      <TitleBar
        isMobile={isMobile}
        isMenuOpen={isMenuOpen}
        onMenuToggle={onMenuToggle}
        showTitle={!showWelcome}
        title={currentChat?.title}
        onTitleChange={handleTitleChange}
      />

      <div className="flex-1 overflow-hidden">
        <AnimatePresence mode="wait">
          {showWelcome ? (
            <motion.div
              key="welcome"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="h-full flex flex-col"
            >
              <Welcome username={userName || "Guest"} />
              <div className="mt-auto">
                <Examples onExampleSelect={handleExampleSelect} />
              </div>
            </motion.div>
          ) : (
            <motion.div
              key="chat"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="h-full overflow-y-auto pb-4"
              ref={scrollRef}
              onScroll={handleScroll}
            >
              <MessageList
                messages={currentChat?.messages || []}
                runtime={currentChat?.runtime || []}
                chatId={currentChat?.id as ChatId}
              />
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      <div className="mt-auto">
        <ChatInput
          message={inputValue}
          onMessageChange={(v) => {
            if (currentChatId) setInput(currentChatId, v);
          }}
          // now handleSendMessage takes (msg, attachments, model)
          onSend={handleSendMessage}
        />
      </div>

      {/* Floating Scroll-to-Bottom Button */}
      {!showWelcome && showScrollButton && (
        <button
          onClick={scrollToBottom}
          className={`absolute cursor-pointer bottom-26 right-6 flex items-center justify-center p-1.5 border-2 rounded-full shadow-md hover:opacity-90 transition-opacity ${
            isDark
              ? "border-chat-dark-border bg-chat-dark-accent text-chat-dark-text"
              : "border-white bg-chat-light-accent text-white"
          }`}
          aria-label="Scroll to bottom"
        >
          <ChevronDownIcon className="h-5 w-5" />
        </button>
      )}
    </motion.div>
  );
}
