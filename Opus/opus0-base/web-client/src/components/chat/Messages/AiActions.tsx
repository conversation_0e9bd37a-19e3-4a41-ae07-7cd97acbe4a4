// src/components/chat/Messages/AiActions.tsx
import { <PERSON><PERSON>, ThumbsUp, ThumbsDown, Check } from "lucide-react";
import { useState } from "react";
import { motion } from "framer-motion";
import { useSocketChat } from "../../../lib/chat/useSocketChat";
import { useChatStore } from "../../../lib/chat/store";
import { useThemeStore } from "../../../lib/theme/store";
import type { ChatId, MessageId } from "../../../lib/chat/types";

/** Delay before resetting the copied state (ms) */
const COPY_RESET_DELAY = 1500;

interface AiActionsProps {
  chatId: ChatId;
  messageId: MessageId;
  content: string;
  feedback: "up" | "down" | null | undefined;
  show: boolean;
}
export function AiActions({ chatId, messageId, content, feedback, show }: AiActionsProps) {
  const [copied, setCopied] = useState(false);
  const { sendFeedback } = useSocketChat();
  const setFeedback = useChatStore((s) => s.setFeedback);
  const isDark = useThemeStore((s) => s.isDark);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(content);
      setCopied(true);
      setTimeout(() => setCopied(false), COPY_RESET_DELAY);
    } catch (err) {
      console.error("Failed to copy", err);
    }
  };

  const handleFeedback = (val: "up" | "down") => {
    if (feedback) return;
    setFeedback(chatId, messageId, val);
    sendFeedback(chatId, messageId, val);
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: show ? 1 : 0 }}
      transition={{ duration: 0.15 }}
      style={{ visibility: show ? "visible" : "hidden" }}
      className={`flex justify-start gap-2 mt-1 ${
        isDark ? "text-chat-dark-text" : "text-chat-light-text"
      }`}
    >
      <button
        onClick={() => handleFeedback("up")}
        disabled={Boolean(feedback)}
        aria-label="Like response"
        className={`p-1 rounded-md ${
          feedback === "up"
            ? "text-green-500"
            : isDark
            ? "hover:bg-chat-dark-button-hover"
            : "hover:bg-chat-light-button-hover"
        }`}
      >
        <ThumbsUp size={16} />
      </button>
      <button
        onClick={() => handleFeedback("down")}
        disabled={Boolean(feedback)}
        aria-label="Dislike response"
        className={`p-1 rounded-md ${
          feedback === "down"
            ? "text-red-500"
            : isDark
            ? "hover:bg-chat-dark-button-hover"
            : "hover:bg-chat-light-button-hover"
        }`}
      >
        <ThumbsDown size={16} />
      </button>
      <button
        onClick={handleCopy}
        aria-label="Copy response"
        className={`p-1 rounded-md ${
          isDark ? "hover:bg-chat-dark-button-hover" : "hover:bg-chat-light-button-hover"
        }`}
      >
        {copied ? <Check size={16} /> : <Copy size={16} />}
      </button>
    </motion.div>
  );
}
