// src/components/chat/Messages/AiMessage.tsx
import ReactMarkdown from "react-markdown";
import { AiActions } from "./AiActions";
import { useThemeStore } from "../../../lib/theme/store";
import type { ChatId, MessageId } from "../../../lib/chat/types";

interface AiMessageProps {
  chatId: ChatId;
  id: MessageId;
  content: string;
  feedback: "up" | "down" | null | undefined;
  showActions: boolean;
}

export function AiMessage({ chatId, id, content, feedback, showActions }: AiMessageProps) {
  const isDark = useThemeStore((s) => s.isDark);
  return (
    <div
      className={`break-words max-w-none mt-4 mb-7 ${
        isDark ? "prose prose-invert" : "prose"
      }`}
    >
      <ReactMarkdown
        components={{
          // Remove 'node' since we don't use it
          a: ({ href, children, ...props }) => (
            <a href={href} {...props} target="_blank" rel="noopener noreferrer">
              {children}
            </a>
          ),
        }}
      >
        {content}
      </ReactMarkdown>
      <AiActions
        chatId={chatId}
        messageId={id}
        content={content}
        feedback={feedback}
        show={showActions}
      />
    </div>
  );
}
