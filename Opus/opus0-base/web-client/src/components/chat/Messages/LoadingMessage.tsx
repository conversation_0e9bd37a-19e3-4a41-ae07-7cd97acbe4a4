// src/components/chat/Messages/LoadingMessage.tsx

import { motion } from "framer-motion";
import { useChatStore } from "../../../lib/chat/store";
import { useThemeStore } from "../../../lib/theme/store";

export function LoadingMessage() {
  const mode = useChatStore((s) => s.currentMessageMode);
  const text = mode === "chat" ? "Thinking..." : "Planning...";
  const isDark = useThemeStore((s) => s.isDark);

  return (
    <motion.div
      initial={{ opacity: 0.3 }}
      animate={{ opacity: [0.3, 1, 0.3] }}
      transition={{ repeat: Infinity, duration: 2 }} // slower pulse
      className="flex justify-start mb-6"
    >
      <p
        className={`text-[15px] leading-relaxed ${
          isDark ? "text-chat-dark-text" : "text-chat-light-text"
        }`}
      >
        {text}
      </p>
    </motion.div>
  );
}
