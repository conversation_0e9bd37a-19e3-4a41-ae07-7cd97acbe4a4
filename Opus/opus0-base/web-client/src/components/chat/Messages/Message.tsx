// src/components/chat/Messages/Message.tsx

import { motion } from "framer-motion";
import { useState } from "react";
import { UserActions } from "./UserActions";
import { useThemeStore } from "../../../lib/theme/store";

interface MessageProps {
  id: string;
  content: string;
  isUser: boolean;
}

export function Message({ id, content, isUser }: MessageProps) {
  const [hovered, setHovered] = useState(false);
  const isDark = useThemeStore((s) => s.isDark);
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      onMouseEnter={() => setHovered(true)}
      onMouseLeave={() => setHovered(false)}
      className={`flex flex-col ${isUser ? "items-end" : "items-start"} mb-6`}
    >
      <div
        className={`flex ${
          isUser ? "flex-row-reverse" : "flex-row"
        } items-start gap-3 ${isUser ? "max-w-[65%]" : "max-w-[85%] pl-6"}`}
      >
        <div
          className={
            isUser
              ? `rounded-2xl px-4 py-2.5 ${
                  isDark
                    ? "bg-chat-dark-input-bg text-chat-dark-text"
                    : "bg-chat-light-input-bg text-chat-light-text"
                }`
              : isDark
              ? "text-chat-dark-text"
              : "text-chat-light-text"
          }
        >
          <p className="text-[15px] leading-relaxed whitespace-pre-wrap">
            {content}
          </p>
        </div>
      </div>
      {isUser && (
        <UserActions messageId={id} content={content} show={hovered} />
      )}
    </motion.div>
  );
}
