// src/components/chat/Messages/MessageList.tsx

import { AnimatePresence } from "framer-motion";
import { Message } from "./Message";
import { AiMessage } from "./AiMessage";
// import { RuntimeCard } from "../Runtime/RuntimeCard";
import { RuntimeGroup } from "../Runtime/RuntimeGroup";
import { LoadingMessage } from "./LoadingMessage";
import { useChatStore } from "../../../lib/chat/store";
import type { RuntimeLog, ChatId, MessageId } from "../../../lib/chat/types";
import type { Attachment } from "../ChatInput/types";
import { FileAttachments } from "../ChatInput/FileAttachments";
// import { JSX } from "react";

/** Local chat message type **/
export interface ChatMessage {
  id: string;
  content: string;
  attachments?: Attachment[];
  isUser: boolean;
  feedback?: "up" | "down" | null;
  timestamp: Date;
  runtimeLogs?: RuntimeLog[];
}

interface MessageListProps {
  messages: ChatMessage[];
  runtime: RuntimeLog[];
  chatId: ChatId;
}

export function MessageList({ messages, runtime, chatId }: MessageListProps) {
  // 1) sort logs by start time
  const sortedLogs = [...runtime].sort(
    (a, b) => a.tsStart.getTime() - b.tsStart.getTime()
  );

  const planningMsgId = useChatStore((s) => s.planningMap[chatId]);
  const isProcessing = useChatStore((s) => s.processingMap[chatId] ?? false);

  let logPointer = 0;

  // 2) flush only logs for this particular user turn
  function flushLogsForTurn(userTs: Date, nextUserTs?: Date) {
    const logs: RuntimeLog[] = [];
    while (logPointer < sortedLogs.length) {
      const log = sortedLogs[logPointer];
      const t = log.tsStart.getTime();
      if (t >= userTs.getTime() && (!nextUserTs || t < nextUserTs.getTime())) {
        logs.push(log);
        logPointer++;
      } else {
        break;
      }
    }
    return logs;
  }

  return (
    <div className="flex-1 overflow-y-auto px-4 py-6">
      <div className="max-w-3xl mx-auto space-y-8">
        <AnimatePresence>
          {messages.map((m, i) => {
            // find when the next user message happens (to bound our logs)
            const nextUser = messages.slice(i + 1).find((x) => x.isUser);
            const nextUserTs = nextUser?.timestamp;

            // flush logs for this turn
            const logsForTurn =
              m.runtimeLogs && m.runtimeLogs.length > 0
                ? m.runtimeLogs
                : m.isUser
                ? flushLogsForTurn(m.timestamp, nextUserTs)
                : [];

            return (
              <div key={m.id}>
                {m.isUser && m.attachments && m.attachments.length > 0 && (
                  <div className="flex mb-1 justify-end">
                    <FileAttachments
                      attachments={m.attachments}
                      onRemove={() => {}}
                      readOnly
                      reverse
                    />
                  </div>
                )}
                {m.isUser ? (
                  <Message id={m.id} content={m.content} isUser />
                ) : (
                  <AiMessage
                    chatId={chatId}
                    id={m.id as MessageId}
                    content={m.content}
                    feedback={m.feedback}
                    showActions={!(i === messages.length - 1 && isProcessing)}
                  />
                )}

                {m.isUser && (
                  <>
                    {/* while no logs yet, show pulsing “Planning...” */}
                    {logsForTurn.length === 0 && planningMsgId === m.id && (
                      <LoadingMessage />
                    )}
                    {/* then render any runtime-cards */}
                    {logsForTurn.length > 0 && (
                      <RuntimeGroup logs={logsForTurn} />
                    )}
                  </>
                )}
              </div>
            );
          })}

          {/* flush any leftover logs */}
          {logPointer < sortedLogs.length && (
            <RuntimeGroup logs={sortedLogs.slice(logPointer)} />
          )}
        </AnimatePresence>
      </div>
    </div>
  );
}
