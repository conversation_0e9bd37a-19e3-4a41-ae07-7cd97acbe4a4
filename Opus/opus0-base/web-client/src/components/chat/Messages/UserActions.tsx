// src/components/chat/Messages/UserActions.tsx
import { Edit, Copy, Check } from "lucide-react";
import { useState } from "react";
import { motion } from "framer-motion";
import { useThemeStore } from "../../../lib/theme/store";

/** Reset copied state after this many ms */
const COPY_RESET_DELAY = 1500;

interface UserActionsProps {
  messageId: string;
  content: string;
  onEdit?: (id: string) => void;
  show?: boolean;
}

export function UserActions({
  messageId,
  content,
  onEdit,
  show = true,
}: UserActionsProps) {
  const [copied, setCopied] = useState(false);
  const isDark = useThemeStore((s) => s.isDark);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(content);
      setCopied(true);
      setTimeout(() => setCopied(false), COPY_RESET_DELAY);
    } catch (err) {
      console.error("Failed to copy", err);
    }
  };
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: show ? 1 : 0 }}
      transition={{ duration: 0.15 }}
      style={{ visibility: show ? "visible" : "hidden" }}
      className={`flex justify-end gap-2 mt-1 ${
        isDark ? "text-chat-dark-text" : "text-chat-light-text"
      }`}
    >
      <button
        onClick={() => onEdit?.(messageId)}
        aria-label="Edit message"
        className={`p-1 rounded-md ${
          isDark ? "hover:bg-chat-dark-button-hover" : "hover:bg-chat-light-button-hover"
        }`}
      >
        <Edit size={16} />
      </button>
      <button
        onClick={handleCopy}
        aria-label="Copy message"
        className={`p-1 rounded-md ${
          isDark ? "hover:bg-chat-dark-button-hover" : "hover:bg-chat-light-button-hover"
        }`}
      >
        {copied ? <Check size={16} /> : <Copy size={16} />}
      </button>
    </motion.div>
  );
}
