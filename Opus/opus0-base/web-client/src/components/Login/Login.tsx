// src/components/Login/Login.tsx
import { useEffect } from "react";
import { useAuthStore } from "../../lib/chat/store";
import { getGoogleOAuthUrl } from "../../lib/auth";

export function Login() {
  const login = useAuthStore((s) => s.login);
  const isLoggedIn = useAuthStore((s) => s.isLoggedIn);

  useEffect(() => {
    if (isLoggedIn) {
      window.location.replace("/");
    }
  }, [isLoggedIn]);

  useEffect(() => {
    const hash = window.location.hash.slice(1);
    const params = new URLSearchParams(hash);
    const token = params.get("access_token");
    if (token) {
      void login(token);
      return;
    }
    window.location.replace(getGoogleOAuthUrl());
  }, [login]);

  return null;
}
