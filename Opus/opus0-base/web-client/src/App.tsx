import { useState, useEffect, useCallback } from "react";
import { Sidebar } from "./components/layout/Sidebar/Sidebar";
import { ChatArea } from "./components/chat/ChatArea/ChatArea";
import { Login } from "./components/Login/Login";
import Home from "./components/HomePage/Home";
import Contact from "./components/HomePage/Contact/Contact";
import PlansAndPricing from "./components/HomePage/PlansAndPricing/PlansAndPricing";
import { useAuthStore, useChatStore } from "./lib/chat/store";
import { fetchChat, fetchSummaries } from "./lib/chat/api";
import {
  Routes,
  Route,
  Navigate,
  useParams,
  useNavigate,
} from "react-router-dom";
import { isValidChatId } from "./lib/chat/utils";
import type { ChatId } from "./lib/chat/types";

function ChatLayout() {
  const [isDocked, setIsDocked] = useState(true);
  const [isMobile, setIsMobile] = useState(window.innerWidth < 1024);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [lastDockedState, setLastDockedState] = useState(true);

  useEffect(() => {
    const handleResize = () => {
      const newIsMobile = window.innerWidth < 1024;
      setIsMobile(newIsMobile);

      if (newIsMobile) {
        setLastDockedState(isDocked);
        setIsDocked(false);
      } else {
        setIsDocked(lastDockedState);
      }
    };

    window.addEventListener("resize", handleResize);
    handleResize();
    return () => window.removeEventListener("resize", handleResize);
  }, [isDocked, lastDockedState]);

  const handleDockChange = useCallback((newIsDocked: boolean) => {
    setIsDocked(newIsDocked);
    setLastDockedState(newIsDocked);
  }, []);

  const handleMenuToggle = useCallback(() => {
    setIsMenuOpen((prev) => !prev);
  }, []);

  return (
    <div className="flex h-screen bg-gray-100">
      <Sidebar
        isDocked={isDocked}
        onDockChange={handleDockChange}
        isOpen={isMenuOpen}
        onOpenChange={setIsMenuOpen}
      />
      <main className="flex-1 min-w-0">
        <ChatArea
          isMobile={isMobile}
          isMenuOpen={isMenuOpen}
          onMenuToggle={handleMenuToggle}
        />
      </main>
    </div>
  );
}

function ChatRouteWrapper() {
  const { chatId } = useParams();
  const navigate = useNavigate();
  const createChat = useChatStore((s) => s.createChat);
  const setCurrentChat = useChatStore((s) => s.setCurrentChat);
  const pendingChatId = useChatStore((s) => s.pendingChatId);
  const hydrateChat = useChatStore((s) => s.hydrateChat);
  const setSummaries = useChatStore((s) => s.setSummaries);

  useEffect(() => {
    fetchSummaries()
      .then(setSummaries)
      .catch((err) => console.error(err));
  }, [setSummaries]);

  useEffect(() => {
    if (!chatId) return;
    if (chatId === "new") {
      const id = createChat();
      setCurrentChat(id);
      navigate(`/chat/${id}`, { replace: true });
      return;
    }
    if (!isValidChatId(chatId)) {
      navigate("/chat/new", { replace: true });
      return;
    }
    setCurrentChat(chatId as unknown as ChatId);
    // Skip fetching if this is a pending chat that hasn't been saved yet
    if (pendingChatId === chatId) return;
    if (!useChatStore.getState().chats.some((c) => c.id === chatId)) {
      fetchChat(chatId)
        .then(hydrateChat)
        .catch(() => navigate("/chat/new", { replace: true }));
    }
  }, [
    chatId,
    pendingChatId,
    createChat,
    setCurrentChat,
    hydrateChat,
    navigate,
  ]);

  return <ChatLayout />;
}

export default function App() {
  const isLoggedIn = useAuthStore((s) => s.isLoggedIn);

  return (
    <Routes>
      <Route
        path="/auth"
        element={isLoggedIn ? <Navigate to="/" replace /> : <Login />}
      />
      <Route
        path="/chat/:chatId"
        element={isLoggedIn ? <ChatRouteWrapper /> : <Navigate to="/auth" />}
      />
      <Route path="/home" element={<Home />} />
      <Route path="/contact" element={<Contact />} />
      <Route path="/plans-pricing" element={<PlansAndPricing />} />
      <Route
        path="/"
        element={
          isLoggedIn ? (
            <Navigate to="/chat/new" replace />
          ) : (
            <Navigate to="/home" replace />
          )
        }
      />
      <Route path="*" element={<Navigate to="/" replace />} />
    </Routes>
  );
}
