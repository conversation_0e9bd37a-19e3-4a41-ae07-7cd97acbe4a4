# web-client/Dockerfile
#
# Build the React frontend and serve it via Nginx.

# ---- build stage ----
FROM node:20-alpine AS build
WORKDIR /app
COPY package.json package-lock.json ./
RUN npm ci
COPY . .
RUN npm run build

# ---- production stage ----
FROM nginx:alpine
# copy custom nginx configuration
COPY nginx.conf /etc/nginx/conf.d/default.conf
# static site
COPY --from=build /app/dist /usr/share/nginx/html
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]