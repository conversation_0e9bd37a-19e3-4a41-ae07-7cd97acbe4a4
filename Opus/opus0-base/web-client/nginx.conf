server {
    listen 80;
    server_name _;
    root /usr/share/nginx/html;
    index index.html;

    # allow larger uploads
    client_max_body_size 100m;

    # websocket endpoint
    location /socket.io/ {
        proxy_pass http://backend:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
    }

    # API routes proxied to backend
    location /uploads {
        # handle CORS preflight requests directly
        if ($request_method = OPTIONS) {
            add_header 'Access-Control-Allow-Origin' '$http_origin' always;
            add_header 'Access-Control-Allow-Methods' 'GET,POST,OPTIONS,DELETE';
            add_header 'Access-Control-Allow-Headers' 'Content-Type,Authorization';
            return 204;
        }

        proxy_pass http://backend:8000;
        proxy_set_header Host $host;
    }

    location /auth/ {
        proxy_pass http://backend:8000;
        proxy_set_header Host $host;
    }

    location /chats/ {
        proxy_pass http://backend:8000;
        proxy_set_header Host $host;
    }

    location /files/ {
        proxy_pass http://backend:8000;
        proxy_set_header Host $host;
    }

    location /sessions/ {
        proxy_pass http://backend:8000;
        proxy_set_header Host $host;
    }

    # SPA fallback
    location / {
        try_files $uri /index.html;
    }
}