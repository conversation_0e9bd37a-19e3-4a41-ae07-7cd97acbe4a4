{"name": "web-client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@heroicons/react": "^2.2.0", "@react-oauth/google": "^0.12.2", "@tailwindcss/vite": "^4.1.4", "framer-motion": "^12.8.2", "lucide-react": "^0.503.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-markdown": "^10.1.0", "react-router-dom": "^6.23.0", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "socket.io-client": "^4.8.1", "tailwindcss": "^4.1.4", "uuid": "^11.1.0", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.22.0", "@tailwindcss/typography": "^0.5.16", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.7.2", "typescript-eslint": "^8.26.1", "vite": "^6.3.1"}}