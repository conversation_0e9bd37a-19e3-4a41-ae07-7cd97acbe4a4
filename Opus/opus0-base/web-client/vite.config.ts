import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import tailwindcss from "@tailwindcss/vite";

export default defineConfig({
  plugins: [react(), tailwindcss()],
  server: {
    historyApiFallback: true,
    proxy: {
      // any call to /uploads → forward to your backend
      "/uploads": {
        target: "http://localhost:8000",
        changeOrigin: true,
      },
      "/auth/login": {
        target: "http://localhost:8000",
        changeOrigin: true,
      },
      "/auth/google": {
        target: "http://localhost:8000",
        changeOrigin: true,
      },
      "/chats": {
        target: "http://localhost:8000",
        changeOrigin: true,
      },
    },
  },
  preview: {
    historyApiFallback: true,
  },
});
