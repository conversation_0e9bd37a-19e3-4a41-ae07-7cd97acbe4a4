import typography from "@tailwindcss/typography";

/** @type {import('tailwindcss').Config} */
export default {
  content: ["./index.html", "./src/**/*.{js,ts,jsx,tsx}"],
  darkMode: "class",
  theme: {
    extend: {
      fontFamily: {
        // Primary font for UI elements
        sans: [
          "SF Pro Display",
          "system-ui",
          "-apple-system",
          "BlinkMacSystemFont",
          "Segoe UI",
          "Roboto",
          "Arial",
          "sans-serif",
        ],
        // Secondary font for headings and display text
        display: [
          "Plus Jakarta Sans",
          "SF Pro Display",
          "system-ui",
          "sans-serif",
        ],
        // Monospace font for code blocks
        mono: [
          "SF Mono",
          "Monaco",
          "Consolas",
          "Liberation Mono",
          "Courier New",
          "monospace",
        ],
      },
      colors: {
        sidebar: {
          light: {
            bg: "#F2F2F2",
            hover: "#E8E8E8",
            active: "#E2E2E2",
            text: "#1A1A1A",
            "text-secondary": "#6B7280",
            border: "#E5E7EB",
            "button-bg": "#FFFFFF",
            "button-hover": "#F3F4F6",
            scrollbar: "#E5E7EB",
            "scrollbar-hover": "#D1D5DB",
          },
          dark: {
            bg: "#1E1F20",
            hover: "#2C2C2C",
            active: "#2C2C2C",
            text: "#E6E6E6",
            "text-secondary": "#8E8E8E",
            border: "#2C2C2C",
            "button-bg": "#2C2C2C",
            "button-hover": "#363636",
            scrollbar: "#2C2C2C",
            "scrollbar-hover": "#404040",
          },
        },
        chat: {
          light: {
            bg: "#FFFFFF",
            "secondary-bg": "#F9FAFB",
            "input-bg": "#F3F4F6",
            "input-border": "#E5E7EB",
            "input-focus": "#3B82F6",
            "button-hover": "#E5E7EB",
            text: "#111827",
            "text-secondary": "#6B7280",
            border: "#E5E7EB",
            accent: "#3B82F6",
          },
          dark: {
            bg: "#1A1A1A",
            "secondary-bg": "#2D2D2D",
            "input-bg": "#2B2B2B",
            "input-border": "#404040",
            "input-focus": "#60A5FA",
            "button-hover": "#404040",
            text: "#F3F4F6",
            "text-secondary": "#9CA3AF",
            border: "#404040",
            accent: "#60A5FA",
          },
        },
        runtime: {
          light: {
            "card-bg": "#F9FAFB",
            "stream-bg": "#F3F4F6",
          },
          dark: {
            "card-bg": "#2E2E2E",
            "stream-bg": "#272727",
          },
        },
      },
      fontSize: {
        sm: "0.8125rem", // 13px
        base: "0.875rem", // 14px
        lg: "0.9375rem", // 15px
      },
      spacing: {
        4.5: "1.125rem",
        5.5: "1.375rem",
      },
      lineHeight: {
        snug: "1.375",
        relaxed: "1.625",
      },
    },
  },
  plugins: [typography],
};
