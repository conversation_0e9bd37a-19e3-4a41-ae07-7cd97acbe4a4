Simple Crawling
This guide covers the basics of web crawling with Crawl4AI. You'll learn how to set up a crawler, make your first request, and understand the response.

Basic Usage
Set up a simple crawl using BrowserConfig and CrawlerRunConfig:

import asyncio
from crawl4ai import AsyncWebCrawler
from crawl4ai.async_configs import BrowserConfig, CrawlerRunConfig

async def main():
browser_config = BrowserConfig() # Default browser configuration
run_config = CrawlerRunConfig() # Default crawl run configuration

    async with AsyncWebCrawler(config=browser_config) as crawler:
        result = await crawler.arun(
            url="https://example.com",
            config=run_config
        )
        print(result.markdown)  # Print clean markdown content

if **name** == "**main**":
asyncio.run(main())
Copy
Understanding the Response
The arun() method returns a CrawlResult object with several useful properties. Here's a quick overview (see CrawlResult for complete details):

result = await crawler.arun(
url="https://example.com",
config=CrawlerRunConfig(fit_markdown=True)
)

# Different content formats

print(result.html) # Raw HTML
print(result.cleaned_html) # Cleaned HTML
print(result.markdown.raw_markdown) # Raw markdown from cleaned html
print(result.markdown.fit_markdown) # Most relevant content in markdown

# Check success status

print(result.success) # True if crawl succeeded
print(result.status_code) # HTTP status code (e.g., 200, 404)

# Access extracted media and links

print(result.media) # Dictionary of found media (images, videos, audio)
print(result.links) # Dictionary of internal and external links
Copy
Adding Basic Options
Customize your crawl using CrawlerRunConfig:

run_config = CrawlerRunConfig(
word_count_threshold=10, # Minimum words per content block
exclude_external_links=True, # Remove external links
remove_overlay_elements=True, # Remove popups/modals
process_iframes=True # Process iframe content
)

result = await crawler.arun(
url="https://example.com",
config=run_config
)
Copy
Handling Errors
Always check if the crawl was successful:

run_config = CrawlerRunConfig()
result = await crawler.arun(url="https://example.com", config=run_config)

if not result.success:
print(f"Crawl failed: {result.error_message}")
print(f"Status code: {result.status_code}")
Copy
Logging and Debugging
Enable verbose logging in BrowserConfig:

browser_config = BrowserConfig(verbose=True)

async with AsyncWebCrawler(config=browser_config) as crawler:
run_config = CrawlerRunConfig()
result = await crawler.arun(url="https://example.com", config=run_config)
Copy
Complete Example
Here's a more comprehensive example demonstrating common usage patterns:

import asyncio
from crawl4ai import AsyncWebCrawler
from crawl4ai.async_configs import BrowserConfig, CrawlerRunConfig, CacheMode

async def main():
browser_config = BrowserConfig(verbose=True)
run_config = CrawlerRunConfig( # Content filtering
word_count_threshold=10,
excluded_tags=['form', 'header'],
exclude_external_links=True,

        # Content processing
        process_iframes=True,
        remove_overlay_elements=True,

        # Cache control
        cache_mode=CacheMode.ENABLED  # Use cache if available
    )

    async with AsyncWebCrawler(config=browser_config) as crawler:
        result = await crawler.arun(
            url="https://example.com",
            config=run_config
        )

        if result.success:
            # Print clean content
            print("Content:", result.markdown[:500])  # First 500 chars

            # Process images
            for image in result.media["images"]:
                print(f"Found image: {image['src']}")

            # Process links
            for link in result.links["internal"]:
                print(f"Internal link: {link['href']}")

        else:
            print(f"Crawl failed: {result.error_message}")

if **name** == "**main**":
asyncio.run(main())

---

Deep Crawling
One of Crawl4AI's most powerful features is its ability to perform configurable deep crawling that can explore websites beyond a single page. With fine-tuned control over crawl depth, domain boundaries, and content filtering, Crawl4AI gives you the tools to extract precisely the content you need.

In this tutorial, you'll learn:

How to set up a Basic Deep Crawler with BFS strategy
Understanding the difference between streamed and non-streamed output
Implementing filters and scorers to target specific content
Creating advanced filtering chains for sophisticated crawls
Using BestFirstCrawling for intelligent exploration prioritization
Prerequisites

- You’ve completed or read AsyncWebCrawler Basics to understand how to run a simple crawl.
- You know how to configure CrawlerRunConfig.

1. Quick Example
   Here's a minimal code snippet that implements a basic deep crawl using the BFSDeepCrawlStrategy:

import asyncio
from crawl4ai import AsyncWebCrawler, CrawlerRunConfig
from crawl4ai.deep_crawling import BFSDeepCrawlStrategy
from crawl4ai.content_scraping_strategy import LXMLWebScrapingStrategy

async def main(): # Configure a 2-level deep crawl
config = CrawlerRunConfig(
deep_crawl_strategy=BFSDeepCrawlStrategy(
max_depth=2,
include_external=False
),
scraping_strategy=LXMLWebScrapingStrategy(),
verbose=True
)

    async with AsyncWebCrawler() as crawler:
        results = await crawler.arun("https://example.com", config=config)

        print(f"Crawled {len(results)} pages in total")

        # Access individual results
        for result in results[:3]:  # Show first 3 results
            print(f"URL: {result.url}")
            print(f"Depth: {result.metadata.get('depth', 0)}")

if **name** == "**main**":
asyncio.run(main())
Copy
What's happening?

- BFSDeepCrawlStrategy(max_depth=2, include_external=False) instructs Crawl4AI to: - Crawl the starting page (depth 0) plus 2 more levels - Stay within the same domain (don't follow external links) - Each result contains metadata like the crawl depth - Results are returned as a list after all crawling is complete

2. Understanding Deep Crawling Strategy Options
   2.1 BFSDeepCrawlStrategy (Breadth-First Search)
   The BFSDeepCrawlStrategy uses a breadth-first approach, exploring all links at one depth before moving deeper:

from crawl4ai.deep_crawling import BFSDeepCrawlStrategy

# Basic configuration

strategy = BFSDeepCrawlStrategy(
max_depth=2, # Crawl initial page + 2 levels deep
include_external=False, # Stay within the same domain
max_pages=50, # Maximum number of pages to crawl (optional)
score_threshold=0.3, # Minimum score for URLs to be crawled (optional)
)
Copy
Key parameters: - max_depth: Number of levels to crawl beyond the starting page - include_external: Whether to follow links to other domains - max_pages: Maximum number of pages to crawl (default: infinite) - score_threshold: Minimum score for URLs to be crawled (default: -inf) - filter_chain: FilterChain instance for URL filtering - url_scorer: Scorer instance for evaluating URLs

2.2 DFSDeepCrawlStrategy (Depth-First Search)
The DFSDeepCrawlStrategy uses a depth-first approach, explores as far down a branch as possible before backtracking.

from crawl4ai.deep_crawling import DFSDeepCrawlStrategy

# Basic configuration

strategy = DFSDeepCrawlStrategy(
max_depth=2, # Crawl initial page + 2 levels deep
include_external=False, # Stay within the same domain
max_pages=30, # Maximum number of pages to crawl (optional)
score_threshold=0.5, # Minimum score for URLs to be crawled (optional)
)
Copy
Key parameters: - max_depth: Number of levels to crawl beyond the starting page - include_external: Whether to follow links to other domains - max_pages: Maximum number of pages to crawl (default: infinite) - score_threshold: Minimum score for URLs to be crawled (default: -inf) - filter_chain: FilterChain instance for URL filtering - url_scorer: Scorer instance for evaluating URLs

2.3 BestFirstCrawlingStrategy (⭐️ - Recommended Deep crawl strategy)
For more intelligent crawling, use BestFirstCrawlingStrategy with scorers to prioritize the most relevant pages:

from crawl4ai.deep_crawling import BestFirstCrawlingStrategy
from crawl4ai.deep_crawling.scorers import KeywordRelevanceScorer

# Create a scorer

scorer = KeywordRelevanceScorer(
keywords=["crawl", "example", "async", "configuration"],
weight=0.7
)

# Configure the strategy

strategy = BestFirstCrawlingStrategy(
max_depth=2,
include_external=False,
url_scorer=scorer,
max_pages=25, # Maximum number of pages to crawl (optional)
)
Copy
This crawling approach: - Evaluates each discovered URL based on scorer criteria - Visits higher-scoring pages first - Helps focus crawl resources on the most relevant content - Can limit total pages crawled with max_pages - Does not need score_threshold as it naturally prioritizes by score

3. Streaming vs. Non-Streaming Results
   Crawl4AI can return results in two modes:

3.1 Non-Streaming Mode (Default)
config = CrawlerRunConfig(
deep_crawl_strategy=BFSDeepCrawlStrategy(max_depth=1),
stream=False # Default behavior
)

async with AsyncWebCrawler() as crawler: # Wait for ALL results to be collected before returning
results = await crawler.arun("https://example.com", config=config)

    for result in results:
        process_result(result)

Copy
When to use non-streaming mode: - You need the complete dataset before processing - You're performing batch operations on all results together - Crawl time isn't a critical factor

3.2 Streaming Mode
config = CrawlerRunConfig(
deep_crawl_strategy=BFSDeepCrawlStrategy(max_depth=1),
stream=True # Enable streaming
)

async with AsyncWebCrawler() as crawler: # Returns an async iterator
async for result in await crawler.arun("https://example.com", config=config): # Process each result as it becomes available
process_result(result)
Copy
Benefits of streaming mode: - Process results immediately as they're discovered - Start working with early results while crawling continues - Better for real-time applications or progressive display - Reduces memory pressure when handling many pages

4. Filtering Content with Filter Chains
   Filters help you narrow down which pages to crawl. Combine multiple filters using FilterChain for powerful targeting.

4.1 Basic URL Pattern Filter
from crawl4ai.deep_crawling.filters import FilterChain, URLPatternFilter

# Only follow URLs containing "blog" or "docs"

url_filter = URLPatternFilter(patterns=["*blog*", "*docs*"])

config = CrawlerRunConfig(
deep_crawl_strategy=BFSDeepCrawlStrategy(
max_depth=1,
filter_chain=FilterChain([url_filter])
)
)
Copy
4.2 Combining Multiple Filters
from crawl4ai.deep_crawling.filters import (
FilterChain,
URLPatternFilter,
DomainFilter,
ContentTypeFilter
)

# Create a chain of filters

filter_chain = FilterChain([ # Only follow URLs with specific patterns
URLPatternFilter(patterns=["*guide*", "*tutorial*"]),

    # Only crawl specific domains
    DomainFilter(
        allowed_domains=["docs.example.com"],
        blocked_domains=["old.docs.example.com"]
    ),

    # Only include specific content types
    ContentTypeFilter(allowed_types=["text/html"])

])

config = CrawlerRunConfig(
deep_crawl_strategy=BFSDeepCrawlStrategy(
max_depth=2,
filter_chain=filter_chain
)
)
Copy
4.3 Available Filter Types
Crawl4AI includes several specialized filters:

URLPatternFilter: Matches URL patterns using wildcard syntax
DomainFilter: Controls which domains to include or exclude
ContentTypeFilter: Filters based on HTTP Content-Type
ContentRelevanceFilter: Uses similarity to a text query
SEOFilter: Evaluates SEO elements (meta tags, headers, etc.) 5. Using Scorers for Prioritized Crawling
Scorers assign priority values to discovered URLs, helping the crawler focus on the most relevant content first.

5.1 KeywordRelevanceScorer
from crawl4ai.deep_crawling.scorers import KeywordRelevanceScorer
from crawl4ai.deep_crawling import BestFirstCrawlingStrategy

# Create a keyword relevance scorer

keyword_scorer = KeywordRelevanceScorer(
keywords=["crawl", "example", "async", "configuration"],
weight=0.7 # Importance of this scorer (0.0 to 1.0)
)

config = CrawlerRunConfig(
deep_crawl_strategy=BestFirstCrawlingStrategy(
max_depth=2,
url_scorer=keyword_scorer
),
stream=True # Recommended with BestFirstCrawling
)

# Results will come in order of relevance score

async with AsyncWebCrawler() as crawler:
async for result in await crawler.arun("https://example.com", config=config):
score = result.metadata.get("score", 0)
print(f"Score: {score:.2f} | {result.url}")
Copy
How scorers work: - Evaluate each discovered URL before crawling - Calculate relevance based on various signals - Help the crawler make intelligent choices about traversal order

6. Advanced Filtering Techniques
   6.1 SEO Filter for Quality Assessment
   The SEOFilter helps you identify pages with strong SEO characteristics:

from crawl4ai.deep_crawling.filters import FilterChain, SEOFilter

# Create an SEO filter that looks for specific keywords in page metadata

seo_filter = SEOFilter(
threshold=0.5, # Minimum score (0.0 to 1.0)
keywords=["tutorial", "guide", "documentation"]
)

config = CrawlerRunConfig(
deep_crawl_strategy=BFSDeepCrawlStrategy(
max_depth=1,
filter_chain=FilterChain([seo_filter])
)
)
Copy
6.2 Content Relevance Filter
The ContentRelevanceFilter analyzes the actual content of pages:

from crawl4ai.deep_crawling.filters import FilterChain, ContentRelevanceFilter

# Create a content relevance filter

relevance_filter = ContentRelevanceFilter(
query="Web crawling and data extraction with Python",
threshold=0.7 # Minimum similarity score (0.0 to 1.0)
)

config = CrawlerRunConfig(
deep_crawl_strategy=BFSDeepCrawlStrategy(
max_depth=1,
filter_chain=FilterChain([relevance_filter])
)
)
Copy
This filter: - Measures semantic similarity between query and page content - It's a BM25-based relevance filter using head section content

7. Building a Complete Advanced Crawler
   This example combines multiple techniques for a sophisticated crawl:

import asyncio
from crawl4ai import AsyncWebCrawler, CrawlerRunConfig
from crawl4ai.content_scraping_strategy import LXMLWebScrapingStrategy
from crawl4ai.deep_crawling import BestFirstCrawlingStrategy
from crawl4ai.deep_crawling.filters import (
FilterChain,
DomainFilter,
URLPatternFilter,
ContentTypeFilter
)
from crawl4ai.deep_crawling.scorers import KeywordRelevanceScorer

async def run_advanced_crawler(): # Create a sophisticated filter chain
filter_chain = FilterChain([ # Domain boundaries
DomainFilter(
allowed_domains=["docs.example.com"],
blocked_domains=["old.docs.example.com"]
),

        # URL patterns to include
        URLPatternFilter(patterns=["*guide*", "*tutorial*", "*blog*"]),

        # Content type filtering
        ContentTypeFilter(allowed_types=["text/html"])
    ])

    # Create a relevance scorer
    keyword_scorer = KeywordRelevanceScorer(
        keywords=["crawl", "example", "async", "configuration"],
        weight=0.7
    )

    # Set up the configuration
    config = CrawlerRunConfig(
        deep_crawl_strategy=BestFirstCrawlingStrategy(
            max_depth=2,
            include_external=False,
            filter_chain=filter_chain,
            url_scorer=keyword_scorer
        ),
        scraping_strategy=LXMLWebScrapingStrategy(),
        stream=True,
        verbose=True
    )

    # Execute the crawl
    results = []
    async with AsyncWebCrawler() as crawler:
        async for result in await crawler.arun("https://docs.example.com", config=config):
            results.append(result)
            score = result.metadata.get("score", 0)
            depth = result.metadata.get("depth", 0)
            print(f"Depth: {depth} | Score: {score:.2f} | {result.url}")

    # Analyze the results
    print(f"Crawled {len(results)} high-value pages")
    print(f"Average score: {sum(r.metadata.get('score', 0) for r in results) / len(results):.2f}")

    # Group by depth
    depth_counts = {}
    for result in results:
        depth = result.metadata.get("depth", 0)
        depth_counts[depth] = depth_counts.get(depth, 0) + 1

    print("Pages crawled by depth:")
    for depth, count in sorted(depth_counts.items()):
        print(f"  Depth {depth}: {count} pages")

if **name** == "**main**":
asyncio.run(run_advanced_crawler())
Copy 8. Limiting and Controlling Crawl Size
8.1 Using max_pages
You can limit the total number of pages crawled with the max_pages parameter:

# Limit to exactly 20 pages regardless of depth

strategy = BFSDeepCrawlStrategy(
max_depth=3,
max_pages=20
)
Copy
This feature is useful for: - Controlling API costs - Setting predictable execution times - Focusing on the most important content - Testing crawl configurations before full execution

8.2 Using score_threshold
For BFS and DFS strategies, you can set a minimum score threshold to only crawl high-quality pages:

# Only follow links with scores above 0.4

strategy = DFSDeepCrawlStrategy(
max_depth=2,
url_scorer=KeywordRelevanceScorer(keywords=["api", "guide", "reference"]),
score_threshold=0.4 # Skip URLs with scores below this value
)
Copy
Note that for BestFirstCrawlingStrategy, score_threshold is not needed since pages are already processed in order of highest score first.

9. Common Pitfalls & Tips
   1.Set realistic limits. Be cautious with max_depth values > 3, which can exponentially increase crawl size. Use max_pages to set hard limits.

2.Don't neglect the scoring component. BestFirstCrawling works best with well-tuned scorers. Experiment with keyword weights for optimal prioritization.

3.Be a good web citizen. Respect robots.txt. (disabled by default)

4.Handle page errors gracefully. Not all pages will be accessible. Check result.status when processing results.

5.Balance breadth vs. depth. Choose your strategy wisely - BFS for comprehensive coverage, DFS for deep exploration, BestFirst for focused relevance-based crawling.

10. Summary & Next Steps
    In this Deep Crawling with Crawl4AI tutorial, you learned to:

Configure BFSDeepCrawlStrategy, DFSDeepCrawlStrategy, and BestFirstCrawlingStrategy
Process results in streaming or non-streaming mode
Apply filters to target specific content
Use scorers to prioritize the most relevant pages
Limit crawls with max_pages and score_threshold parameters
Build a complete advanced crawler with combined techniques
With these tools, you can efficiently extract structured data from websites at scale, focusing precisely on the content you need for your specific use case.

---

Crawl Result and Output
When you call arun() on a page, Crawl4AI returns a CrawlResult object containing everything you might need—raw HTML, a cleaned version, optional screenshots or PDFs, structured extraction results, and more. This document explains those fields and how they map to different output types.

1. The CrawlResult Model
   Below is the core schema. Each field captures a different aspect of the crawl’s result:

class MarkdownGenerationResult(BaseModel):
raw_markdown: str
markdown_with_citations: str
references_markdown: str
fit_markdown: Optional[str] = None
fit_html: Optional[str] = None

class CrawlResult(BaseModel):
url: str
html: str
success: bool
cleaned_html: Optional[str] = None
media: Dict[str, List[Dict]] = {}
links: Dict[str, List[Dict]] = {}
downloaded_files: Optional[List[str]] = None
screenshot: Optional[str] = None
pdf : Optional[bytes] = None
mhtml: Optional[str] = None
markdown: Optional[Union[str, MarkdownGenerationResult]] = None
extracted_content: Optional[str] = None
metadata: Optional[dict] = None
error_message: Optional[str] = None
session_id: Optional[str] = None
response_headers: Optional[dict] = None
status_code: Optional[int] = None
ssl_certificate: Optional[SSLCertificate] = None
class Config:
arbitrary_types_allowed = True
Copy
Table: Key Fields in CrawlResult
Field (Name & Type) Description
url (str) The final or actual URL crawled (in case of redirects).
html (str) Original, unmodified page HTML. Good for debugging or custom processing.
success (bool) True if the crawl completed without major errors, else False.
cleaned_html (Optional[str]) Sanitized HTML with scripts/styles removed; can exclude tags if configured via excluded_tags etc.
media (Dict[str, List[Dict]]) Extracted media info (images, audio, etc.), each with attributes like src, alt, score, etc.
links (Dict[str, List[Dict]]) Extracted link data, split by internal and external. Each link usually has href, text, etc.
downloaded_files (Optional[List[str]]) If accept_downloads=True in BrowserConfig, this lists the filepaths of saved downloads.
screenshot (Optional[str]) Screenshot of the page (base64-encoded) if screenshot=True.
pdf (Optional[bytes]) PDF of the page if pdf=True.
mhtml (Optional[str]) MHTML snapshot of the page if capture_mhtml=True. Contains the full page with all resources.
markdown (Optional[str or MarkdownGenerationResult]) It holds a MarkdownGenerationResult. Over time, this will be consolidated into markdown. The generator can provide raw markdown, citations, references, and optionally fit_markdown.
extracted_content (Optional[str]) The output of a structured extraction (CSS/LLM-based) stored as JSON string or other text.
metadata (Optional[dict]) Additional info about the crawl or extracted data.
error_message (Optional[str]) If success=False, contains a short description of what went wrong.
session_id (Optional[str]) The ID of the session used for multi-page or persistent crawling.
response_headers (Optional[dict]) HTTP response headers, if captured.
status_code (Optional[int]) HTTP status code (e.g., 200 for OK).
ssl_certificate (Optional[SSLCertificate]) SSL certificate info if fetch_ssl_certificate=True. 2. HTML Variants
html: Raw HTML
Crawl4AI preserves the exact HTML as result.html. Useful for:

Debugging page issues or checking the original content.
Performing your own specialized parse if needed.
cleaned_html: Sanitized
If you specify any cleanup or exclusion parameters in CrawlerRunConfig (like excluded_tags, remove_forms, etc.), you’ll see the result here:

config = CrawlerRunConfig(
excluded_tags=["form", "header", "footer"],
keep_data_attributes=False
)
result = await crawler.arun("https://example.com", config=config)
print(result.cleaned_html) # Freed of forms, header, footer, data-\* attributes
Copy 3. Markdown Generation
3.1 markdown
markdown: The current location for detailed markdown output, returning a MarkdownGenerationResult object.
markdown_v2: Deprecated since v0.5.
MarkdownGenerationResult Fields:

Field Description
raw_markdown The basic HTML→Markdown conversion.
markdown_with_citations Markdown including inline citations that reference links at the end.
references_markdown The references/citations themselves (if citations=True).
fit_markdown The filtered/“fit” markdown if a content filter was used.
fit_html The filtered HTML that generated fit_markdown.
3.2 Basic Example with a Markdown Generator
from crawl4ai import AsyncWebCrawler, CrawlerRunConfig
from crawl4ai.markdown_generation_strategy import DefaultMarkdownGenerator

config = CrawlerRunConfig(
markdown_generator=DefaultMarkdownGenerator(
options={"citations": True, "body_width": 80} # e.g. pass html2text style options
)
)
result = await crawler.arun(url="https://example.com", config=config)

md_res = result.markdown # or eventually 'result.markdown'
print(md_res.raw_markdown[:500])
print(md_res.markdown_with_citations)
print(md_res.references_markdown)
Copy
Note: If you use a filter like PruningContentFilter, you’ll get fit_markdown and fit_html as well.

4. Structured Extraction: extracted_content
   If you run a JSON-based extraction strategy (CSS, XPath, LLM, etc.), the structured data is not stored in markdown—it’s placed in result.extracted_content as a JSON string (or sometimes plain text).

Example: CSS Extraction with raw:// HTML
import asyncio
import json
from crawl4ai import AsyncWebCrawler, CrawlerRunConfig, CacheMode
from crawl4ai.extraction_strategy import JsonCssExtractionStrategy

async def main():
schema = {
"name": "Example Items",
"baseSelector": "div.item",
"fields": [
{"name": "title", "selector": "h2", "type": "text"},
{"name": "link", "selector": "a", "type": "attribute", "attribute": "href"}
]
}
raw_html = "<div class='item'><h2>Item 1</h2><a href='https://example.com/item1'>Link 1</a></div>"

    async with AsyncWebCrawler() as crawler:
        result = await crawler.arun(
            url="raw://" + raw_html,
            config=CrawlerRunConfig(
                cache_mode=CacheMode.BYPASS,
                extraction_strategy=JsonCssExtractionStrategy(schema)
            )
        )
        data = json.loads(result.extracted_content)
        print(data)

if **name** == "**main**":
asyncio.run(main())
Copy
Here: - url="raw://..." passes the HTML content directly, no network requests.

- The CSS extraction strategy populates result.extracted_content with the JSON array [{"title": "...", "link": "..."}].

5. More Fields: Links, Media, and More
   5.1 links
   A dictionary, typically with "internal" and "external" lists. Each entry might have href, text, title, etc. This is automatically captured if you haven’t disabled link extraction.

print(result.links["internal"][:3]) # Show first 3 internal links
Copy
5.2 media
Similarly, a dictionary with "images", "audio", "video", etc. Each item could include src, alt, score, and more, if your crawler is set to gather them.

images = result.media.get("images", [])
for img in images:
print("Image URL:", img["src"], "Alt:", img.get("alt"))
Copy
5.3 screenshot, pdf, and mhtml
If you set screenshot=True, pdf=True, or capture_mhtml=True in CrawlerRunConfig, then:

result.screenshot contains a base64-encoded PNG string.
result.pdf contains raw PDF bytes (you can write them to a file).
result.mhtml contains the MHTML snapshot of the page as a string (you can write it to a .mhtml file).

# Save the PDF

with open("page.pdf", "wb") as f:
f.write(result.pdf)

# Save the MHTML

if result.mhtml:
with open("page.mhtml", "w", encoding="utf-8") as f:
f.write(result.mhtml)
Copy
The MHTML (MIME HTML) format is particularly useful as it captures the entire web page including all of its resources (CSS, images, scripts, etc.) in a single file, making it perfect for archiving or offline viewing.

5.4 ssl_certificate
If fetch_ssl_certificate=True, result.ssl_certificate holds details about the site’s SSL cert, such as issuer, validity dates, etc.

6. Accessing These Fields
   After you run:

result = await crawler.arun(url="https://example.com", config=some_config)
Copy
Check any field:

if result.success:
print(result.status_code, result.response_headers)
print("Links found:", len(result.links.get("internal", [])))
if result.markdown:
print("Markdown snippet:", result.markdown.raw_markdown[:200])
if result.extracted_content:
print("Structured JSON:", result.extracted_content)
else:
print("Error:", result.error_message)
Copy
Deprecation: Since v0.5 result.markdown_v2, result.fit_html,result.fit_markdown are deprecated. Use result.markdown instead! It holds MarkdownGenerationResult, which includes fit_html and fit_markdown as it's properties.

7. Next Steps
   Markdown Generation: Dive deeper into how to configure DefaultMarkdownGenerator and various filters.
   Content Filtering: Learn how to use BM25ContentFilter and PruningContentFilter.
   Session & Hooks: If you want to manipulate the page or preserve state across multiple arun() calls, see the hooking or session docs.
   LLM Extraction: For complex or unstructured content requiring AI-driven parsing, check the LLM-based strategies doc.
   Enjoy exploring all that CrawlResult offers—whether you need raw HTML, sanitized output, markdown, or fully structured data, Crawl4AI has you covered!

---

Browser, Crawler & LLM Configuration (Quick Overview)
Crawl4AI's flexibility stems from two key classes:

BrowserConfig – Dictates how the browser is launched and behaves (e.g., headless or visible, proxy, user agent).
CrawlerRunConfig – Dictates how each crawl operates (e.g., caching, extraction, timeouts, JavaScript code to run, etc.).
LLMConfig - Dictates how LLM providers are configured. (model, api token, base url, temperature etc.)
In most examples, you create one BrowserConfig for the entire crawler session, then pass a fresh or re-used CrawlerRunConfig whenever you call arun(). This tutorial shows the most commonly used parameters. If you need advanced or rarely used fields, see the Configuration Parameters.

1. BrowserConfig Essentials
   class BrowserConfig:
   def **init**(
   browser_type="chromium",
   headless=True,
   proxy_config=None,
   viewport_width=1080,
   viewport_height=600,
   verbose=True,
   use_persistent_context=False,
   user_data_dir=None,
   cookies=None,
   headers=None,
   user_agent=None,
   text_mode=False,
   light_mode=False,
   extra_args=None, # ... other advanced parameters omitted here
   ):
   ...
   Copy
   Key Fields to Note
   browser_type
   Options: "chromium", "firefox", or "webkit".
   Defaults to "chromium".
   If you need a different engine, specify it here.

headless

True: Runs the browser in headless mode (invisible browser).
False: Runs the browser in visible mode, which helps with debugging.

proxy_config

A dictionary with fields like:
{
"server": "http://proxy.example.com:8080",
"username": "...",
"password": "..."
}
Copy
Leave as None if a proxy is not required.

viewport_width & viewport_height:

The initial window size.
Some sites behave differently with smaller or bigger viewports.

verbose:

If True, prints extra logs.
Handy for debugging.

use_persistent_context:

If True, uses a persistent browser profile, storing cookies/local storage across runs.
Typically also set user_data_dir to point to a folder.

cookies & headers:

If you want to start with specific cookies or add universal HTTP headers, set them here.
E.g. cookies=[{"name": "session", "value": "abc123", "domain": "example.com"}].

user_agent:

Custom User-Agent string. If None, a default is used.
You can also set user_agent_mode="random" for randomization (if you want to fight bot detection).

text_mode & light_mode:

text_mode=True disables images, possibly speeding up text-only crawls.
light_mode=True turns off certain background features for performance.

extra_args:

Additional flags for the underlying browser.
E.g. ["--disable-extensions"].
Helper Methods
Both configuration classes provide a clone() method to create modified copies:

# Create a base browser config

base_browser = BrowserConfig(
browser_type="chromium",
headless=True,
text_mode=True
)

# Create a visible browser config for debugging

debug_browser = base_browser.clone(
headless=False,
verbose=True
)
Copy
Minimal Example:

from crawl4ai import AsyncWebCrawler, BrowserConfig

browser_conf = BrowserConfig(
browser_type="firefox",
headless=False,
text_mode=True
)

async with AsyncWebCrawler(config=browser_conf) as crawler:
result = await crawler.arun("https://example.com")
print(result.markdown[:300])
Copy 2. CrawlerRunConfig Essentials
class CrawlerRunConfig:
def **init**(
word_count_threshold=200,
extraction_strategy=None,
markdown_generator=None,
cache_mode=None,
js_code=None,
wait_for=None,
screenshot=False,
pdf=False,
capture_mhtml=False, # Location and Identity Parameters
locale=None, # e.g. "en-US", "fr-FR"
timezone_id=None, # e.g. "America/New_York"
geolocation=None, # GeolocationConfig object # Resource Management
enable_rate_limiting=False,
rate_limit_config=None,
memory_threshold_percent=70.0,
check_interval=1.0,
max_session_permit=20,
display_mode=None,
verbose=True,
stream=False, # Enable streaming for arun_many() # ... other advanced parameters omitted
):
...
Copy
Key Fields to Note
word_count_threshold:
The minimum word count before a block is considered.
If your site has lots of short paragraphs or items, you can lower it.

extraction_strategy:

Where you plug in JSON-based extraction (CSS, LLM, etc.).
If None, no structured extraction is done (only raw/cleaned HTML + markdown).

markdown_generator:

E.g., DefaultMarkdownGenerator(...), controlling how HTML→Markdown conversion is done.
If None, a default approach is used.

cache_mode:

Controls caching behavior (ENABLED, BYPASS, DISABLED, etc.).
If None, defaults to some level of caching or you can specify CacheMode.ENABLED.

js_code:

A string or list of JS strings to execute.
Great for "Load More" buttons or user interactions.

wait_for:

A CSS or JS expression to wait for before extracting content.
Common usage: wait_for="css:.main-loaded" or wait_for="js:() => window.loaded === true".

screenshot, pdf, & capture_mhtml:

If True, captures a screenshot, PDF, or MHTML snapshot after the page is fully loaded.
The results go to result.screenshot (base64), result.pdf (bytes), or result.mhtml (string).

Location Parameters:

locale: Browser's locale (e.g., "en-US", "fr-FR") for language preferences
timezone_id: Browser's timezone (e.g., "America/New_York", "Europe/Paris")
geolocation: GPS coordinates via GeolocationConfig(latitude=48.8566, longitude=2.3522)
See Identity Based Crawling

verbose:

Logs additional runtime details.
Overlaps with the browser's verbosity if also set to True in BrowserConfig.

enable_rate_limiting:

If True, enables rate limiting for batch processing.
Requires rate_limit_config to be set.

memory_threshold_percent:

The memory threshold (as a percentage) to monitor.
If exceeded, the crawler will pause or slow down.
check_interval:

The interval (in seconds) to check system resources.
Affects how often memory and CPU usage are monitored.
max_session_permit:

The maximum number of concurrent crawl sessions.
Helps prevent overwhelming the system.
display_mode:

The display mode for progress information (DETAILED, BRIEF, etc.).
Affects how much information is printed during the crawl.
Helper Methods
The clone() method is particularly useful for creating variations of your crawler configuration:

# Create a base configuration

base_config = CrawlerRunConfig(
cache_mode=CacheMode.ENABLED,
word_count_threshold=200,
wait_until="networkidle"
)

# Create variations for different use cases

stream_config = base_config.clone(
stream=True, # Enable streaming mode
cache_mode=CacheMode.BYPASS
)

debug_config = base_config.clone(
page_timeout=120000, # Longer timeout for debugging
verbose=True
)
Copy
The clone() method: - Creates a new instance with all the same settings - Updates only the specified parameters - Leaves the original configuration unchanged - Perfect for creating variations without repeating all parameters

3. LLMConfig Essentials
   Key fields to note
   provider:
   Which LLM provoder to use.
   Possible values are "ollama/llama3","groq/llama3-70b-8192","groq/llama3-8b-8192", "openai/gpt-4o-mini" ,"openai/gpt-4o","openai/o1-mini","openai/o1-preview","openai/o3-mini","openai/o3-mini-high","anthropic/claude-3-haiku-20240307","anthropic/claude-3-opus-20240229","anthropic/claude-3-sonnet-20240229","anthropic/claude-3-5-sonnet-20240620","gemini/gemini-pro","gemini/gemini-1.5-pro","gemini/gemini-2.0-flash","gemini/gemini-2.0-flash-exp","gemini/gemini-2.0-flash-lite-preview-02-05","deepseek/deepseek-chat"
   (default: "openai/gpt-4o-mini")

api_token:

Optional. When not provided explicitly, api_token will be read from environment variables based on provider. For example: If a gemini model is passed as provider then,"GEMINI_API_KEY" will be read from environment variables
API token of LLM provider
eg: api_token = "gsk_1ClHGGJ7Lpn4WGybR7vNWGdyb3FY7zXEw3SCiy0BAVM9lL8CQv"
Environment variable - use with prefix "env:"
eg:api_token = "env: GROQ_API_KEY"
base_url:

If your provider has a custom endpoint
llm_config = LLMConfig(provider="openai/gpt-4o-mini", api_token=os.getenv("OPENAI_API_KEY"))
Copy 4. Putting It All Together
In a typical scenario, you define one BrowserConfig for your crawler session, then create one or more CrawlerRunConfig & LLMConfig depending on each call's needs:

import asyncio
from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig, CacheMode, LLMConfig
from crawl4ai.extraction_strategy import JsonCssExtractionStrategy

async def main(): # 1) Browser config: headless, bigger viewport, no proxy
browser_conf = BrowserConfig(
headless=True,
viewport_width=1280,
viewport_height=720
)

    # 2) Example extraction strategy
    schema = {
        "name": "Articles",
        "baseSelector": "div.article",
        "fields": [
            {"name": "title", "selector": "h2", "type": "text"},
            {"name": "link", "selector": "a", "type": "attribute", "attribute": "href"}
        ]
    }
    extraction = JsonCssExtractionStrategy(schema)

    # 3) Example LLM content filtering

    gemini_config = LLMConfig(
        provider="gemini/gemini-1.5-pro"
        api_token = "env:GEMINI_API_TOKEN"
    )

    # Initialize LLM filter with specific instruction
    filter = LLMContentFilter(
        llm_config=gemini_config,  # or your preferred provider
        instruction="""
        Focus on extracting the core educational content.
        Include:
        - Key concepts and explanations
        - Important code examples
        - Essential technical details
        Exclude:
        - Navigation elements
        - Sidebars
        - Footer content
        Format the output as clean markdown with proper code blocks and headers.
        """,
        chunk_token_threshold=500,  # Adjust based on your needs
        verbose=True
    )

    md_generator = DefaultMarkdownGenerator(
    content_filter=filter,
    options={"ignore_links": True}

    # 4) Crawler run config: skip cache, use extraction
    run_conf = CrawlerRunConfig(
        markdown_generator=md_generator,
        extraction_strategy=extraction,
        cache_mode=CacheMode.BYPASS,
    )

    async with AsyncWebCrawler(config=browser_conf) as crawler:
        # 4) Execute the crawl
        result = await crawler.arun(url="https://example.com/news", config=run_conf)

        if result.success:
            print("Extracted content:", result.extracted_content)
        else:
            print("Error:", result.error_message)

if **name** == "**main**":
asyncio.run(main())
Copy 5. Next Steps
For a detailed list of available parameters (including advanced ones), see:

BrowserConfig, CrawlerRunConfig & LLMConfig Reference
You can explore topics like:

Custom Hooks & Auth (Inject JavaScript or handle login forms).
Session Management (Re-use pages, preserve state across multiple calls).
Magic Mode or Identity-based Crawling (Fight bot detection by simulating user behavior).
Advanced Caching (Fine-tune read/write cache modes). 6. Conclusion
BrowserConfig, CrawlerRunConfig and LLMConfig give you straightforward ways to define:

Which browser to launch, how it should run, and any proxy or user agent needs.
How each crawl should behave—caching, timeouts, JavaScript code, extraction strategies, etc.
Which LLM provider to use, api token, temperature and base url for custom endpoints
Use them together for clear, maintainable code, and when you need more specialized behavior, check out the advanced parameters in the reference docs. Happy crawling!

Markdown Generation Basics
One of Crawl4AI’s core features is generating clean, structured markdown from web pages. Originally built to solve the problem of extracting only the “actual” content and discarding boilerplate or noise, Crawl4AI’s markdown system remains one of its biggest draws for AI workflows.

In this tutorial, you’ll learn:

How to configure the Default Markdown Generator
How content filters (BM25 or Pruning) help you refine markdown and discard junk
The difference between raw markdown (result.markdown) and filtered markdown (fit_markdown)
Prerequisites

- You’ve completed or read AsyncWebCrawler Basics to understand how to run a simple crawl.
- You know how to configure CrawlerRunConfig.

1. Quick Example
   Here’s a minimal code snippet that uses the DefaultMarkdownGenerator with no additional filtering:

import asyncio
from crawl4ai import AsyncWebCrawler, CrawlerRunConfig
from crawl4ai.markdown_generation_strategy import DefaultMarkdownGenerator

async def main():
config = CrawlerRunConfig(
markdown_generator=DefaultMarkdownGenerator()
)
async with AsyncWebCrawler() as crawler:
result = await crawler.arun("https://example.com", config=config)

        if result.success:
            print("Raw Markdown Output:\n")
            print(result.markdown)  # The unfiltered markdown from the page
        else:
            print("Crawl failed:", result.error_message)

if **name** == "**main**":
asyncio.run(main())
Copy
What’s happening?

- CrawlerRunConfig( markdown_generator = DefaultMarkdownGenerator() ) instructs Crawl4AI to convert the final HTML into markdown at the end of each crawl.
- The resulting markdown is accessible via result.markdown.

2. How Markdown Generation Works
   2.1 HTML-to-Text Conversion (Forked & Modified)
   Under the hood, DefaultMarkdownGenerator uses a specialized HTML-to-text approach that:

Preserves headings, code blocks, bullet points, etc.
Removes extraneous tags (scripts, styles) that don’t add meaningful content.
Can optionally generate references for links or skip them altogether.
A set of options (passed as a dict) allows you to customize precisely how HTML converts to markdown. These map to standard html2text-like configuration plus your own enhancements (e.g., ignoring internal links, preserving certain tags verbatim, or adjusting line widths).

2.2 Link Citations & References
By default, the generator can convert <a href="..."> elements into [text][1] citations, then place the actual links at the bottom of the document. This is handy for research workflows that demand references in a structured manner.

2.3 Optional Content Filters
Before or after the HTML-to-Markdown step, you can apply a content filter (like BM25 or Pruning) to reduce noise and produce a “fit_markdown”—a heavily pruned version focusing on the page’s main text. We’ll cover these filters shortly.

3. Configuring the Default Markdown Generator
   You can tweak the output by passing an options dict to DefaultMarkdownGenerator. For example:

from crawl4ai.markdown_generation_strategy import DefaultMarkdownGenerator
from crawl4ai import AsyncWebCrawler, CrawlerRunConfig

async def main(): # Example: ignore all links, don't escape HTML, and wrap text at 80 characters
md_generator = DefaultMarkdownGenerator(
options={
"ignore_links": True,
"escape_html": False,
"body_width": 80
}
)

    config = CrawlerRunConfig(
        markdown_generator=md_generator
    )

    async with AsyncWebCrawler() as crawler:
        result = await crawler.arun("https://example.com/docs", config=config)
        if result.success:
            print("Markdown:\n", result.markdown[:500])  # Just a snippet
        else:
            print("Crawl failed:", result.error_message)

if **name** == "**main**":
import asyncio
asyncio.run(main())
Copy
Some commonly used options:

ignore_links (bool): Whether to remove all hyperlinks in the final markdown.
ignore_images (bool): Remove all ![image]() references.
escape_html (bool): Turn HTML entities into text (default is often True).
body_width (int): Wrap text at N characters. 0 or None means no wrapping.
skip_internal_links (bool): If True, omit #localAnchors or internal links referencing the same page.
include_sup_sub (bool): Attempt to handle <sup> / <sub> in a more readable way. 4. Selecting the HTML Source for Markdown Generation
The content_source parameter allows you to control which HTML content is used as input for markdown generation. This gives you flexibility in how the HTML is processed before conversion to markdown.

from crawl4ai.markdown_generation_strategy import DefaultMarkdownGenerator
from crawl4ai import AsyncWebCrawler, CrawlerRunConfig

async def main(): # Option 1: Use the raw HTML directly from the webpage (before any processing)
raw_md_generator = DefaultMarkdownGenerator(
content_source="raw_html",
options={"ignore_links": True}
)

    # Option 2: Use the cleaned HTML (after scraping strategy processing - default)
    cleaned_md_generator = DefaultMarkdownGenerator(
        content_source="cleaned_html",  # This is the default
        options={"ignore_links": True}
    )

    # Option 3: Use preprocessed HTML optimized for schema extraction
    fit_md_generator = DefaultMarkdownGenerator(
        content_source="fit_html",
        options={"ignore_links": True}
    )

    # Use one of the generators in your crawler config
    config = CrawlerRunConfig(
        markdown_generator=raw_md_generator  # Try each of the generators
    )

    async with AsyncWebCrawler() as crawler:
        result = await crawler.arun("https://example.com", config=config)
        if result.success:
            print("Markdown:\n", result.markdown.raw_markdown[:500])
        else:
            print("Crawl failed:", result.error_message)

if **name** == "**main**":
import asyncio
asyncio.run(main())
Copy
HTML Source Options
"cleaned_html" (default): Uses the HTML after it has been processed by the scraping strategy. This HTML is typically cleaner and more focused on content, with some boilerplate removed.

"raw_html": Uses the original HTML directly from the webpage, before any cleaning or processing. This preserves more of the original content, but may include navigation bars, ads, footers, and other elements that might not be relevant to the main content.

"fit_html": Uses HTML preprocessed for schema extraction. This HTML is optimized for structured data extraction and may have certain elements simplified or removed.

When to Use Each Option
Use "cleaned_html" (default) for most cases where you want a balance of content preservation and noise removal.
Use "raw_html" when you need to preserve all original content, or when the cleaning process is removing content you actually want to keep.
Use "fit_html" when working with structured data or when you need HTML that's optimized for schema extraction. 5. Content Filters
Content filters selectively remove or rank sections of text before turning them into Markdown. This is especially helpful if your page has ads, nav bars, or other clutter you don’t want.

5.1 BM25ContentFilter
If you have a search query, BM25 is a good choice:

from crawl4ai.markdown_generation_strategy import DefaultMarkdownGenerator
from crawl4ai.content_filter_strategy import BM25ContentFilter
from crawl4ai import CrawlerRunConfig

bm25_filter = BM25ContentFilter(
user_query="machine learning",
bm25_threshold=1.2,
use_stemming=True
)

md_generator = DefaultMarkdownGenerator(
content_filter=bm25_filter,
options={"ignore_links": True}
)

config = CrawlerRunConfig(markdown_generator=md_generator)
Copy
user_query: The term you want to focus on. BM25 tries to keep only content blocks relevant to that query.
bm25_threshold: Raise it to keep fewer blocks; lower it to keep more.
use_stemming: If True, variations of words match (e.g., “learn,” “learning,” “learnt”).
No query provided? BM25 tries to glean a context from page metadata, or you can simply treat it as a scorched-earth approach that discards text with low generic score. Realistically, you want to supply a query for best results.

5.2 PruningContentFilter
If you don’t have a specific query, or if you just want a robust “junk remover,” use PruningContentFilter. It analyzes text density, link density, HTML structure, and known patterns (like “nav,” “footer”) to systematically prune extraneous or repetitive sections.

from crawl4ai.content_filter_strategy import PruningContentFilter

prune_filter = PruningContentFilter(
threshold=0.5,
threshold_type="fixed", # or "dynamic"
min_word_threshold=50
)
Copy
threshold: Score boundary. Blocks below this score get removed.
threshold_type:
"fixed": Straight comparison (score >= threshold keeps the block).
"dynamic": The filter adjusts threshold in a data-driven manner.
min_word_threshold: Discard blocks under N words as likely too short or unhelpful.
When to Use PruningContentFilter

- You want a broad cleanup without a user query.
- The page has lots of repeated sidebars, footers, or disclaimers that hamper text extraction.

  5.3 LLMContentFilter
  For intelligent content filtering and high-quality markdown generation, you can use the LLMContentFilter. This filter leverages LLMs to generate relevant markdown while preserving the original content's meaning and structure:

from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig, LLMConfig
from crawl4ai.content_filter_strategy import LLMContentFilter

async def main(): # Initialize LLM filter with specific instruction
filter = LLMContentFilter(
llm_config = LLMConfig(provider="openai/gpt-4o",api_token="your-api-token"), #or use environment variable
instruction="""
Focus on extracting the core educational content.
Include: - Key concepts and explanations - Important code examples - Essential technical details
Exclude: - Navigation elements - Sidebars - Footer content
Format the output as clean markdown with proper code blocks and headers.
""",
chunk_token_threshold=4096, # Adjust based on your needs
verbose=True
)

    config = CrawlerRunConfig(
        content_filter=filter
    )

    async with AsyncWebCrawler() as crawler:
        result = await crawler.arun("https://example.com", config=config)
        print(result.markdown.fit_markdown)  # Filtered markdown content

Copy
Key Features: - Intelligent Filtering: Uses LLMs to understand and extract relevant content while maintaining context - Customizable Instructions: Tailor the filtering process with specific instructions - Chunk Processing: Handles large documents by processing them in chunks (controlled by chunk_token_threshold) - Parallel Processing: For better performance, use smaller chunk_token_threshold (e.g., 2048 or 4096) to enable parallel processing of content chunks

Two Common Use Cases:

Exact Content Preservation:

filter = LLMContentFilter(
instruction="""
Extract the main educational content while preserving its original wording and substance completely. 1. Maintain the exact language and terminology 2. Keep all technical explanations and examples intact 3. Preserve the original flow and structure 4. Remove only clearly irrelevant elements like navigation menus and ads
""",
chunk_token_threshold=4096
)
Copy
Focused Content Extraction:

filter = LLMContentFilter(
instruction="""
Focus on extracting specific types of content: - Technical documentation - Code examples - API references
Reformat the content into clear, well-structured markdown
""",
chunk_token_threshold=4096
)
Copy
Performance Tip: Set a smaller chunk_token_threshold (e.g., 2048 or 4096) to enable parallel processing of content chunks. The default value is infinity, which processes the entire content as a single chunk.

6. Using Fit Markdown
   When a content filter is active, the library produces two forms of markdown inside result.markdown:

1. raw_markdown: The full unfiltered markdown.
2. fit_markdown: A “fit” version where the filter has removed or trimmed noisy segments.

import asyncio
from crawl4ai import AsyncWebCrawler, CrawlerRunConfig
from crawl4ai.markdown_generation_strategy import DefaultMarkdownGenerator
from crawl4ai.content_filter_strategy import PruningContentFilter

async def main():
config = CrawlerRunConfig(
markdown_generator=DefaultMarkdownGenerator(
content_filter=PruningContentFilter(threshold=0.6),
options={"ignore_links": True}
)
)
async with AsyncWebCrawler() as crawler:
result = await crawler.arun("https://news.example.com/tech", config=config)
if result.success:
print("Raw markdown:\n", result.markdown)

            # If a filter is used, we also have .fit_markdown:
            md_object = result.markdown  # or your equivalent
            print("Filtered markdown:\n", md_object.fit_markdown)
        else:
            print("Crawl failed:", result.error_message)

if **name** == "**main**":
asyncio.run(main())
Copy 7. The MarkdownGenerationResult Object
If your library stores detailed markdown output in an object like MarkdownGenerationResult, you’ll see fields such as:

raw_markdown: The direct HTML-to-markdown transformation (no filtering).
markdown_with_citations: A version that moves links to reference-style footnotes.
references_markdown: A separate string or section containing the gathered references.
fit_markdown: The filtered markdown if you used a content filter.
fit_html: The corresponding HTML snippet used to generate fit_markdown (helpful for debugging or advanced usage).
Example:

md_obj = result.markdown # your library’s naming may vary
print("RAW:\n", md_obj.raw_markdown)
print("CITED:\n", md_obj.markdown_with_citations)
print("REFERENCES:\n", md_obj.references_markdown)
print("FIT:\n", md_obj.fit_markdown)
Copy
Why Does This Matter?

- You can supply raw_markdown to an LLM if you want the entire text.
- Or feed fit_markdown into a vector database to reduce token usage.
- references_markdown can help you keep track of link provenance.

Below is a revised section under “Combining Filters (BM25 + Pruning)” that demonstrates how you can run two passes of content filtering without re-crawling, by taking the HTML (or text) from a first pass and feeding it into the second filter. It uses real code patterns from the snippet you provided for BM25ContentFilter, which directly accepts HTML strings (and can also handle plain text with minimal adaptation).

8. Combining Filters (BM25 + Pruning) in Two Passes
   You might want to prune out noisy boilerplate first (with PruningContentFilter), and then rank what’s left against a user query (with BM25ContentFilter). You don’t have to crawl the page twice. Instead:

1. First pass: Apply PruningContentFilter directly to the raw HTML from result.html (the crawler’s downloaded HTML).
2. Second pass: Take the pruned HTML (or text) from step 1, and feed it into BM25ContentFilter, focusing on a user query.

Two-Pass Example
import asyncio
from crawl4ai import AsyncWebCrawler, CrawlerRunConfig
from crawl4ai.content_filter_strategy import PruningContentFilter, BM25ContentFilter
from bs4 import BeautifulSoup

async def main(): # 1. Crawl with minimal or no markdown generator, just get raw HTML
config = CrawlerRunConfig( # If you only want raw HTML, you can skip passing a markdown_generator # or provide one but focus on .html in this example
)

    async with AsyncWebCrawler() as crawler:
        result = await crawler.arun("https://example.com/tech-article", config=config)

        if not result.success or not result.html:
            print("Crawl failed or no HTML content.")
            return

        raw_html = result.html

        # 2. First pass: PruningContentFilter on raw HTML
        pruning_filter = PruningContentFilter(threshold=0.5, min_word_threshold=50)

        # filter_content returns a list of "text chunks" or cleaned HTML sections
        pruned_chunks = pruning_filter.filter_content(raw_html)
        # This list is basically pruned content blocks, presumably in HTML or text form

        # For demonstration, let's combine these chunks back into a single HTML-like string
        # or you could do further processing. It's up to your pipeline design.
        pruned_html = "\n".join(pruned_chunks)

        # 3. Second pass: BM25ContentFilter with a user query
        bm25_filter = BM25ContentFilter(
            user_query="machine learning",
            bm25_threshold=1.2,
            language="english"
        )

        # returns a list of text chunks
        bm25_chunks = bm25_filter.filter_content(pruned_html)

        if not bm25_chunks:
            print("Nothing matched the BM25 query after pruning.")
            return

        # 4. Combine or display final results
        final_text = "\n---\n".join(bm25_chunks)

        print("==== PRUNED OUTPUT (first pass) ====")
        print(pruned_html[:500], "... (truncated)")  # preview

        print("\n==== BM25 OUTPUT (second pass) ====")
        print(final_text[:500], "... (truncated)")

if **name** == "**main**":
asyncio.run(main())
Copy
What’s Happening?
1. Raw HTML: We crawl once and store the raw HTML in result.html.
2. PruningContentFilter: Takes HTML + optional parameters. It extracts blocks of text or partial HTML, removing headings/sections deemed “noise.” It returns a list of text chunks.
3. Combine or Transform: We join these pruned chunks back into a single HTML-like string. (Alternatively, you could store them in a list for further logic—whatever suits your pipeline.)
4. BM25ContentFilter: We feed the pruned string into BM25ContentFilter with a user query. This second pass further narrows the content to chunks relevant to “machine learning.”

No Re-Crawling: We used raw_html from the first pass, so there’s no need to run arun() again—no second network request.

Tips & Variations
Plain Text vs. HTML: If your pruned output is mostly text, BM25 can still handle it; just keep in mind it expects a valid string input. If you supply partial HTML (like "<p>some text</p>"), it will parse it as HTML.
Chaining in a Single Pipeline: If your code supports it, you can chain multiple filters automatically. Otherwise, manual two-pass filtering (as shown) is straightforward.
Adjust Thresholds: If you see too much or too little text in step one, tweak threshold=0.5 or min_word_threshold=50. Similarly, bm25_threshold=1.2 can be raised/lowered for more or fewer chunks in step two.
One-Pass Combination?
If your codebase or pipeline design allows applying multiple filters in one pass, you could do so. But often it’s simpler—and more transparent—to run them sequentially, analyzing each step’s result.

Bottom Line: By manually chaining your filtering logic in two passes, you get powerful incremental control over the final content. First, remove “global” clutter with Pruning, then refine further with BM25-based query relevance—without incurring a second network crawl.

9. Common Pitfalls & Tips
   1. No Markdown Output?

- Make sure the crawler actually retrieved HTML. If the site is heavily JS-based, you may need to enable dynamic rendering or wait for elements.
- Check if your content filter is too aggressive. Lower thresholds or disable the filter to see if content reappears.

  2. Performance Considerations

- Very large pages with multiple filters can be slower. Consider cache_mode to avoid re-downloading.
- If your final use case is LLM ingestion, consider summarizing further or chunking big texts.

  3. Take Advantage of fit_markdown

- Great for RAG pipelines, semantic search, or any scenario where extraneous boilerplate is unwanted.
- Still verify the textual quality—some sites have crucial data in footers or sidebars.

  4. Adjusting html2text Options

- If you see lots of raw HTML slipping into the text, turn on escape_html.
- If code blocks look messy, experiment with mark_code or handle_code_in_pre.

10. Summary & Next Steps
    In this Markdown Generation Basics tutorial, you learned to:

Configure the DefaultMarkdownGenerator with HTML-to-text options.
Select different HTML sources using the content_source parameter.
Use BM25ContentFilter for query-specific extraction or PruningContentFilter for general noise removal.
Distinguish between raw and filtered markdown (fit_markdown).
Leverage the MarkdownGenerationResult object to handle different forms of output (citations, references, etc.).
Now you can produce high-quality Markdown from any website, focusing on exactly the content you need—an essential step for powering AI models, summarization pipelines, or knowledge-base queries.

Fit Markdown with Pruning & BM25
Fit Markdown is a specialized filtered version of your page’s markdown, focusing on the most relevant content. By default, Crawl4AI converts the entire HTML into a broad raw_markdown. With fit markdown, we apply a content filter algorithm (e.g., Pruning or BM25) to remove or rank low-value sections—such as repetitive sidebars, shallow text blocks, or irrelevancies—leaving a concise textual “core.”

1. How “Fit Markdown” Works
   1.1 The content_filter
   In CrawlerRunConfig, you can specify a content_filter to shape how content is pruned or ranked before final markdown generation. A filter’s logic is applied before or during the HTML→Markdown process, producing:

result.markdown.raw_markdown (unfiltered)
result.markdown.fit_markdown (filtered or “fit” version)
result.markdown.fit_html (the corresponding HTML snippet that produced fit_markdown)
1.2 Common Filters
1. PruningContentFilter – Scores each node by text density, link density, and tag importance, discarding those below a threshold.
2. BM25ContentFilter – Focuses on textual relevance using BM25 ranking, especially useful if you have a specific user query (e.g., “machine learning” or “food nutrition”).

2. PruningContentFilter
   Pruning discards less relevant nodes based on text density, link density, and tag importance. It’s a heuristic-based approach—if certain sections appear too “thin” or too “spammy,” they’re pruned.

2.1 Usage Example
import asyncio
from crawl4ai import AsyncWebCrawler, CrawlerRunConfig
from crawl4ai.content_filter_strategy import PruningContentFilter
from crawl4ai.markdown_generation_strategy import DefaultMarkdownGenerator

async def main(): # Step 1: Create a pruning filter
prune_filter = PruningContentFilter( # Lower → more content retained, higher → more content pruned
threshold=0.45,  
 # "fixed" or "dynamic"
threshold_type="dynamic",  
 # Ignore nodes with <5 words
min_word_threshold=5  
 )

    # Step 2: Insert it into a Markdown Generator
    md_generator = DefaultMarkdownGenerator(content_filter=prune_filter)

    # Step 3: Pass it to CrawlerRunConfig
    config = CrawlerRunConfig(
        markdown_generator=md_generator
    )

    async with AsyncWebCrawler() as crawler:
        result = await crawler.arun(
            url="https://news.ycombinator.com",
            config=config
        )

        if result.success:
            # 'fit_markdown' is your pruned content, focusing on "denser" text
            print("Raw Markdown length:", len(result.markdown.raw_markdown))
            print("Fit Markdown length:", len(result.markdown.fit_markdown))
        else:
            print("Error:", result.error_message)

if **name** == "**main**":
asyncio.run(main())
Copy
2.2 Key Parameters
min_word_threshold (int): If a block has fewer words than this, it’s pruned.
threshold_type (str):
"fixed" → each node must exceed threshold (0–1).
"dynamic" → node scoring adjusts according to tag type, text/link density, etc.
threshold (float, default ~0.48): The base or “anchor” cutoff.
Algorithmic Factors:

Text density – Encourages blocks that have a higher ratio of text to overall content.
Link density – Penalizes sections that are mostly links.
Tag importance – e.g., an <article> or <p> might be more important than a <div>.
Structural context – If a node is deeply nested or in a suspected sidebar, it might be deprioritized. 3. BM25ContentFilter
BM25 is a classical text ranking algorithm often used in search engines. If you have a user query or rely on page metadata to derive a query, BM25 can identify which text chunks best match that query.

3.1 Usage Example
import asyncio
from crawl4ai import AsyncWebCrawler, CrawlerRunConfig
from crawl4ai.content_filter_strategy import BM25ContentFilter
from crawl4ai.markdown_generation_strategy import DefaultMarkdownGenerator

async def main(): # 1) A BM25 filter with a user query
bm25_filter = BM25ContentFilter(
user_query="startup fundraising tips", # Adjust for stricter or looser results
bm25_threshold=1.2  
 )

    # 2) Insert into a Markdown Generator
    md_generator = DefaultMarkdownGenerator(content_filter=bm25_filter)

    # 3) Pass to crawler config
    config = CrawlerRunConfig(
        markdown_generator=md_generator
    )

    async with AsyncWebCrawler() as crawler:
        result = await crawler.arun(
            url="https://news.ycombinator.com",
            config=config
        )
        if result.success:
            print("Fit Markdown (BM25 query-based):")
            print(result.markdown.fit_markdown)
        else:
            print("Error:", result.error_message)

if **name** == "**main**":
asyncio.run(main())
Copy
3.2 Parameters
user_query (str, optional): E.g. "machine learning". If blank, the filter tries to glean a query from page metadata.
bm25_threshold (float, default 1.0):
Higher → fewer chunks but more relevant.
Lower → more inclusive.
In more advanced scenarios, you might see parameters like use_stemming, case_sensitive, or priority_tags to refine how text is tokenized or weighted.

4. Accessing the “Fit” Output
   After the crawl, your “fit” content is found in result.markdown.fit_markdown.

fit_md = result.markdown.fit_markdown
fit_html = result.markdown.fit_html
Copy
If the content filter is BM25, you might see additional logic or references in fit_markdown that highlight relevant segments. If it’s Pruning, the text is typically well-cleaned but not necessarily matched to a query.

5. Code Patterns Recap
   5.1 Pruning
   prune_filter = PruningContentFilter(
   threshold=0.5,
   threshold_type="fixed",
   min_word_threshold=10
   )
   md_generator = DefaultMarkdownGenerator(content_filter=prune_filter)
   config = CrawlerRunConfig(markdown_generator=md_generator)
   Copy
   5.2 BM25
   bm25_filter = BM25ContentFilter(
   user_query="health benefits fruit",
   bm25_threshold=1.2
   )
   md_generator = DefaultMarkdownGenerator(content_filter=bm25_filter)
   config = CrawlerRunConfig(markdown_generator=md_generator)
   Copy
6. Combining with “word_count_threshold” & Exclusions
   Remember you can also specify:

config = CrawlerRunConfig(
word_count_threshold=10,
excluded_tags=["nav", "footer", "header"],
exclude_external_links=True,
markdown_generator=DefaultMarkdownGenerator(
content_filter=PruningContentFilter(threshold=0.5)
)
)
Copy
Thus, multi-level filtering occurs:

The crawler’s excluded_tags are removed from the HTML first.
The content filter (Pruning, BM25, or custom) prunes or ranks the remaining text blocks.
The final “fit” content is generated in result.markdown.fit_markdown. 7. Custom Filters
If you need a different approach (like a specialized ML model or site-specific heuristics), you can create a new class inheriting from RelevantContentFilter and implement filter_content(html). Then inject it into your markdown generator:

from crawl4ai.content_filter_strategy import RelevantContentFilter

class MyCustomFilter(RelevantContentFilter):
def filter_content(self, html, min_word_threshold=None): # parse HTML, implement custom logic
return [block for block in ... if ... some condition...]
Copy
Steps:

Subclass RelevantContentFilter.
Implement filter_content(...).
Use it in your DefaultMarkdownGenerator(content_filter=MyCustomFilter(...)). 8. Final Thoughts
Fit Markdown is a crucial feature for:

Summaries: Quickly get the important text from a cluttered page.
Search: Combine with BM25 to produce content relevant to a query.
AI Pipelines: Filter out boilerplate so LLM-based extraction or summarization runs on denser text.
Key Points: - PruningContentFilter: Great if you just want the “meatiest” text without a user query.

- BM25ContentFilter: Perfect for query-based extraction or searching.
- Combine with excluded_tags, exclude_external_links, word_count_threshold to refine your final “fit” text.
- Fit markdown ends up in result.markdown.fit_markdown; eventually result.markdown.fit_markdown in future versions.

With these tools, you can zero in on the text that truly matters, ignoring spammy or boilerplate content, and produce a concise, relevant “fit markdown” for your AI or data pipelines. Happy pruning and searching!

Content Selection
Crawl4AI provides multiple ways to select, filter, and refine the content from your crawls. Whether you need to target a specific CSS region, exclude entire tags, filter out external links, or remove certain domains and images, CrawlerRunConfig offers a wide range of parameters.

Below, we show how to configure these parameters and combine them for precise control.

1. CSS-Based Selection
   There are two ways to select content from a page: using css_selector or the more flexible target_elements.

1.1 Using css_selector
A straightforward way to limit your crawl results to a certain region of the page is css_selector in CrawlerRunConfig:

import asyncio
from crawl4ai import AsyncWebCrawler, CrawlerRunConfig

async def main():
config = CrawlerRunConfig( # e.g., first 30 items from Hacker News
css_selector=".athing:nth-child(-n+30)"  
 )
async with AsyncWebCrawler() as crawler:
result = await crawler.arun(
url="https://news.ycombinator.com/newest",
config=config
)
print("Partial HTML length:", len(result.cleaned_html))

if **name** == "**main**":
asyncio.run(main())
Copy
Result: Only elements matching that selector remain in result.cleaned_html.

1.2 Using target_elements
The target_elements parameter provides more flexibility by allowing you to target multiple elements for content extraction while preserving the entire page context for other features:

import asyncio
from crawl4ai import AsyncWebCrawler, CrawlerRunConfig

async def main():
config = CrawlerRunConfig( # Target article body and sidebar, but not other content
target_elements=["article.main-content", "aside.sidebar"]
)
async with AsyncWebCrawler() as crawler:
result = await crawler.arun(
url="https://example.com/blog-post",
config=config
)
print("Markdown focused on target elements")
print("Links from entire page still available:", len(result.links.get("internal", [])))

if **name** == "**main**":
asyncio.run(main())
Copy
Key difference: With target_elements, the markdown generation and structural data extraction focus on those elements, but other page elements (like links, images, and tables) are still extracted from the entire page. This gives you fine-grained control over what appears in your markdown content while preserving full page context for link analysis and media collection.

2.  Content Filtering & Exclusions
    2.1 Basic Overview
    config = CrawlerRunConfig( # Content thresholds
    word_count_threshold=10, # Minimum words per block

        # Tag exclusions
        excluded_tags=['form', 'header', 'footer', 'nav'],

        # Link filtering
        exclude_external_links=True,
        exclude_social_media_links=True,
        # Block entire domains
        exclude_domains=["adtrackers.com", "spammynews.org"],
        exclude_social_media_domains=["facebook.com", "twitter.com"],

        # Media filtering
        exclude_external_images=True

    )
    Copy
    Explanation:

word_count_threshold: Ignores text blocks under X words. Helps skip trivial blocks like short nav or disclaimers.
excluded_tags: Removes entire tags (<form>, <header>, <footer>, etc.).
Link Filtering:
exclude_external_links: Strips out external links and may remove them from result.links.
exclude_social_media_links: Removes links pointing to known social media domains.
exclude_domains: A custom list of domains to block if discovered in links.
exclude_social_media_domains: A curated list (override or add to it) for social media sites.
Media Filtering:
exclude_external_images: Discards images not hosted on the same domain as the main page (or its subdomains).
By default in case you set exclude_social_media_links=True, the following social media domains are excluded:

[
'facebook.com',
'twitter.com',
'x.com',
'linkedin.com',
'instagram.com',
'pinterest.com',
'tiktok.com',
'snapchat.com',
'reddit.com',
]
Copy
2.2 Example Usage
import asyncio
from crawl4ai import AsyncWebCrawler, CrawlerRunConfig, CacheMode

async def main():
config = CrawlerRunConfig(
css_selector="main.content",
word_count_threshold=10,
excluded_tags=["nav", "footer"],
exclude_external_links=True,
exclude_social_media_links=True,
exclude_domains=["ads.com", "spammytrackers.net"],
exclude_external_images=True,
cache_mode=CacheMode.BYPASS
)

    async with AsyncWebCrawler() as crawler:
        result = await crawler.arun(url="https://news.ycombinator.com", config=config)
        print("Cleaned HTML length:", len(result.cleaned_html))

if **name** == "**main**":
asyncio.run(main())
Copy
Note: If these parameters remove too much, reduce or disable them accordingly.

3. Handling Iframes
   Some sites embed content in <iframe> tags. If you want that inline:

config = CrawlerRunConfig( # Merge iframe content into the final output
process_iframes=True,  
 remove_overlay_elements=True
)
Copy
Usage:

import asyncio
from crawl4ai import AsyncWebCrawler, CrawlerRunConfig

async def main():
config = CrawlerRunConfig(
process_iframes=True,
remove_overlay_elements=True
)
async with AsyncWebCrawler() as crawler:
result = await crawler.arun(
url="https://example.org/iframe-demo",
config=config
)
print("Iframe-merged length:", len(result.cleaned_html))

if **name** == "**main**":
asyncio.run(main())
Copy 4. Structured Extraction Examples
You can combine content selection with a more advanced extraction strategy. For instance, a CSS-based or LLM-based extraction strategy can run on the filtered HTML.

4.1 Pattern-Based with JsonCssExtractionStrategy
import asyncio
import json
from crawl4ai import AsyncWebCrawler, CrawlerRunConfig, CacheMode
from crawl4ai.extraction_strategy import JsonCssExtractionStrategy

async def main(): # Minimal schema for repeated items
schema = {
"name": "News Items",
"baseSelector": "tr.athing",
"fields": [
{"name": "title", "selector": "span.titleline a", "type": "text"},
{
"name": "link",
"selector": "span.titleline a",
"type": "attribute",
"attribute": "href"
}
]
}

    config = CrawlerRunConfig(
        # Content filtering
        excluded_tags=["form", "header"],
        exclude_domains=["adsite.com"],

        # CSS selection or entire page
        css_selector="table.itemlist",

        # No caching for demonstration
        cache_mode=CacheMode.BYPASS,

        # Extraction strategy
        extraction_strategy=JsonCssExtractionStrategy(schema)
    )

    async with AsyncWebCrawler() as crawler:
        result = await crawler.arun(
            url="https://news.ycombinator.com/newest",
            config=config
        )
        data = json.loads(result.extracted_content)
        print("Sample extracted item:", data[:1])  # Show first item

if **name** == "**main**":
asyncio.run(main())
Copy
4.2 LLM-Based Extraction
import asyncio
import json
from pydantic import BaseModel, Field
from crawl4ai import AsyncWebCrawler, CrawlerRunConfig, LLMConfig
from crawl4ai.extraction_strategy import LLMExtractionStrategy

class ArticleData(BaseModel):
headline: str
summary: str

async def main():
llm_strategy = LLMExtractionStrategy(
llm_config = LLMConfig(provider="openai/gpt-4",api_token="sk-YOUR_API_KEY")
schema=ArticleData.schema(),
extraction_type="schema",
instruction="Extract 'headline' and a short 'summary' from the content."
)

    config = CrawlerRunConfig(
        exclude_external_links=True,
        word_count_threshold=20,
        extraction_strategy=llm_strategy
    )

    async with AsyncWebCrawler() as crawler:
        result = await crawler.arun(url="https://news.ycombinator.com", config=config)
        article = json.loads(result.extracted_content)
        print(article)

if **name** == "**main**":
asyncio.run(main())
Copy
Here, the crawler:

Filters out external links (exclude_external_links=True).
Ignores very short text blocks (word_count_threshold=20).
Passes the final HTML to your LLM strategy for an AI-driven parse. 5. Comprehensive Example
Below is a short function that unifies CSS selection, exclusion logic, and a pattern-based extraction, demonstrating how you can fine-tune your final data:

import asyncio
import json
from crawl4ai import AsyncWebCrawler, CrawlerRunConfig, CacheMode
from crawl4ai.extraction_strategy import JsonCssExtractionStrategy

async def extract_main_articles(url: str):
schema = {
"name": "ArticleBlock",
"baseSelector": "div.article-block",
"fields": [
{"name": "headline", "selector": "h2", "type": "text"},
{"name": "summary", "selector": ".summary", "type": "text"},
{
"name": "metadata",
"type": "nested",
"fields": [
{"name": "author", "selector": ".author", "type": "text"},
{"name": "date", "selector": ".date", "type": "text"}
]
}
]
}

    config = CrawlerRunConfig(
        # Keep only #main-content
        css_selector="#main-content",

        # Filtering
        word_count_threshold=10,
        excluded_tags=["nav", "footer"],
        exclude_external_links=True,
        exclude_domains=["somebadsite.com"],
        exclude_external_images=True,

        # Extraction
        extraction_strategy=JsonCssExtractionStrategy(schema),

        cache_mode=CacheMode.BYPASS
    )

    async with AsyncWebCrawler() as crawler:
        result = await crawler.arun(url=url, config=config)
        if not result.success:
            print(f"Error: {result.error_message}")
            return None
        return json.loads(result.extracted_content)

async def main():
articles = await extract_main_articles("https://news.ycombinator.com/newest")
if articles:
print("Extracted Articles:", articles[:2]) # Show first 2

if **name** == "**main**":
asyncio.run(main())
Copy
Why This Works: - CSS scoping with #main-content.

- Multiple exclude\_ parameters to remove domains, external images, etc.
- A JsonCssExtractionStrategy to parse repeated article blocks.

6. Scraping Modes
   Crawl4AI provides two different scraping strategies for HTML content processing: WebScrapingStrategy (BeautifulSoup-based, default) and LXMLWebScrapingStrategy (LXML-based). The LXML strategy offers significantly better performance, especially for large HTML documents.

from crawl4ai import AsyncWebCrawler, CrawlerRunConfig, LXMLWebScrapingStrategy

async def main():
config = CrawlerRunConfig(
scraping_strategy=LXMLWebScrapingStrategy() # Faster alternative to default BeautifulSoup
)
async with AsyncWebCrawler() as crawler:
result = await crawler.arun(
url="https://example.com",
config=config
)
Copy
You can also create your own custom scraping strategy by inheriting from ContentScrapingStrategy. The strategy must return a ScrapingResult object with the following structure:

from crawl4ai import ContentScrapingStrategy, ScrapingResult, MediaItem, Media, Link, Links

class CustomScrapingStrategy(ContentScrapingStrategy):
def scrap(self, url: str, html: str, \*\*kwargs) -> ScrapingResult: # Implement your custom scraping logic here
return ScrapingResult(
cleaned_html="<html>...</html>", # Cleaned HTML content
success=True, # Whether scraping was successful
media=Media(
images=[ # List of images found
MediaItem(
src="https://example.com/image.jpg",
alt="Image description",
desc="Surrounding text",
score=1,
type="image",
group_id=1,
format="jpg",
width=800
)
],
videos=[], # List of videos (same structure as images)
audios=[] # List of audio files (same structure as images)
),
links=Links(
internal=[ # List of internal links
Link(
href="https://example.com/page",
text="Link text",
title="Link title",
base_domain="example.com"
)
],
external=[] # List of external links (same structure)
),
metadata={ # Additional metadata
"title": "Page Title",
"description": "Page description"
}
)

    async def ascrap(self, url: str, html: str, **kwargs) -> ScrapingResult:
        # For simple cases, you can use the sync version
        return await asyncio.to_thread(self.scrap, url, html, **kwargs)

Copy
Performance Considerations
The LXML strategy can be up to 10-20x faster than BeautifulSoup strategy, particularly when processing large HTML documents. However, please note:

LXML strategy is currently experimental
In some edge cases, the parsing results might differ slightly from BeautifulSoup
If you encounter any inconsistencies between LXML and BeautifulSoup results, please raise an issue with a reproducible example
Choose LXML strategy when: - Processing large HTML documents (recommended for >100KB) - Performance is critical - Working with well-formed HTML

Stick to BeautifulSoup strategy (default) when: - Maximum compatibility is needed - Working with malformed HTML - Exact parsing behavior is critical

7. Combining CSS Selection Methods
   You can combine css_selector and target_elements in powerful ways to achieve fine-grained control over your output:

import asyncio
from crawl4ai import AsyncWebCrawler, CrawlerRunConfig, CacheMode

async def main(): # Target specific content but preserve page context
config = CrawlerRunConfig( # Focus markdown on main content and sidebar
target_elements=["#main-content", ".sidebar"],

        # Global filters applied to entire page
        excluded_tags=["nav", "footer", "header"],
        exclude_external_links=True,

        # Use basic content thresholds
        word_count_threshold=15,

        cache_mode=CacheMode.BYPASS
    )

    async with AsyncWebCrawler() as crawler:
        result = await crawler.arun(
            url="https://example.com/article",
            config=config
        )

        print(f"Content focuses on specific elements, but all links still analyzed")
        print(f"Internal links: {len(result.links.get('internal', []))}")
        print(f"External links: {len(result.links.get('external', []))}")

if **name** == "**main**":
asyncio.run(main())
Copy
This approach gives you the best of both worlds: - Markdown generation and content extraction focus on the elements you care about - Links, images and other page data still give you the full context of the page - Content filtering still applies globally

8. Conclusion
   By mixing target_elements or css_selector scoping, content filtering parameters, and advanced extraction strategies, you can precisely choose which data to keep. Key parameters in CrawlerRunConfig for content selection include:

target_elements – Array of CSS selectors to focus markdown generation and data extraction, while preserving full page context for links and media.
css_selector – Basic scoping to an element or region for all extraction processes.
word_count_threshold – Skip short blocks.
excluded_tags – Remove entire HTML tags.
exclude_external_links, exclude_social_media_links, exclude_domains – Filter out unwanted links or domains.
exclude_external_images – Remove images from external sources.
process_iframes – Merge iframe content if needed.
Combine these with structured extraction (CSS, LLM-based, or others) to build powerful crawls that yield exactly the content you want, from raw or cleaned HTML up to sophisticated JSON structures. For more detail, see Configuration Reference. Enjoy curating your data to the max!

Download Handling in Crawl4AI
This guide explains how to use Crawl4AI to handle file downloads during crawling. You'll learn how to trigger downloads, specify download locations, and access downloaded files.

Enabling Downloads
To enable downloads, set the accept_downloads parameter in the BrowserConfig object and pass it to the crawler.

from crawl4ai.async_configs import BrowserConfig, AsyncWebCrawler

async def main():
config = BrowserConfig(accept_downloads=True) # Enable downloads globally
async with AsyncWebCrawler(config=config) as crawler: # ... your crawling logic ...

asyncio.run(main())
Copy
Specifying Download Location
Specify the download directory using the downloads_path attribute in the BrowserConfig object. If not provided, Crawl4AI defaults to creating a "downloads" directory inside the .crawl4ai folder in your home directory.

from crawl4ai.async_configs import BrowserConfig
import os

downloads_path = os.path.join(os.getcwd(), "my_downloads") # Custom download path
os.makedirs(downloads_path, exist_ok=True)

config = BrowserConfig(accept_downloads=True, downloads_path=downloads_path)

async def main():
async with AsyncWebCrawler(config=config) as crawler:
result = await crawler.arun(url="https://example.com") # ...
Copy
Triggering Downloads
Downloads are typically triggered by user interactions on a web page, such as clicking a download button. Use js_code in CrawlerRunConfig to simulate these actions and wait_for to allow sufficient time for downloads to start.

from crawl4ai.async_configs import CrawlerRunConfig

config = CrawlerRunConfig(
js_code="""
const downloadLink = document.querySelector('a[href$=".exe"]');
if (downloadLink) {
downloadLink.click();
}
""",
wait_for=5 # Wait 5 seconds for the download to start
)

result = await crawler.arun(url="https://www.python.org/downloads/", config=config)
Copy
Accessing Downloaded Files
The downloaded_files attribute of the CrawlResult object contains paths to downloaded files.

if result.downloaded_files:
print("Downloaded files:")
for file_path in result.downloaded_files:
print(f"- {file_path}")
file_size = os.path.getsize(file_path)
print(f"- File size: {file_size} bytes")
else:
print("No files downloaded.")
Copy
Example: Downloading Multiple Files
from crawl4ai.async_configs import BrowserConfig, CrawlerRunConfig
import os
from pathlib import Path

async def download_multiple_files(url: str, download_path: str):
config = BrowserConfig(accept_downloads=True, downloads_path=download_path)
async with AsyncWebCrawler(config=config) as crawler:
run_config = CrawlerRunConfig(
js_code="""
const downloadLinks = document.querySelectorAll('a[download]');
for (const link of downloadLinks) {
link.click();
// Delay between clicks
await new Promise(r => setTimeout(r, 2000));  
 }
""",
wait_for=10 # Wait for all downloads to start
)
result = await crawler.arun(url=url, config=run_config)

        if result.downloaded_files:
            print("Downloaded files:")
            for file in result.downloaded_files:
                print(f"- {file}")
        else:
            print("No files downloaded.")

# Usage

download_path = os.path.join(Path.home(), ".crawl4ai", "downloads")
os.makedirs(download_path, exist_ok=True)

asyncio.run(download_multiple_files("https://www.python.org/downloads/windows/", download_path))
Copy
Important Considerations
Browser Context: Downloads are managed within the browser context. Ensure js_code correctly targets the download triggers on the webpage.
Timing: Use wait_for in CrawlerRunConfig to manage download timing.
Error Handling: Handle errors to manage failed downloads or incorrect paths gracefully.
Security: Scan downloaded files for potential security threats before use.
This revised guide ensures consistency with the Crawl4AI codebase by using BrowserConfig and CrawlerRunConfig for all download-related configurations. Let me know if further adjustments are needed!

Handling Lazy-Loaded Images
Many websites now load images lazily as you scroll. If you need to ensure they appear in your final crawl (and in result.media), consider:

1. wait_for_images=True – Wait for images to fully load.
2. scan_full_page – Force the crawler to scroll the entire page, triggering lazy loads.
3. scroll_delay – Add small delays between scroll steps.

Note: If the site requires multiple “Load More” triggers or complex interactions, see the Page Interaction docs.

Example: Ensuring Lazy Images Appear
import asyncio
from crawl4ai import AsyncWebCrawler, CrawlerRunConfig, BrowserConfig
from crawl4ai.async_configs import CacheMode

async def main():
config = CrawlerRunConfig( # Force the crawler to wait until images are fully loaded
wait_for_images=True,

        # Option 1: If you want to automatically scroll the page to load images
        scan_full_page=True,  # Tells the crawler to try scrolling the entire page
        scroll_delay=0.5,     # Delay (seconds) between scroll steps

        # Option 2: If the site uses a 'Load More' or JS triggers for images,
        # you can also specify js_code or wait_for logic here.

        cache_mode=CacheMode.BYPASS,
        verbose=True
    )

    async with AsyncWebCrawler(config=BrowserConfig(headless=True)) as crawler:
        result = await crawler.arun("https://www.example.com/gallery", config=config)

        if result.success:
            images = result.media.get("images", [])
            print("Images found:", len(images))
            for i, img in enumerate(images[:5]):
                print(f"[Image {i}] URL: {img['src']}, Score: {img.get('score','N/A')}")
        else:
            print("Error:", result.error_message)

if **name** == "**main**":
asyncio.run(main())
Copy
Explanation:

wait_for_images=True
The crawler tries to ensure images have finished loading before finalizing the HTML.
scan_full_page=True
Tells the crawler to attempt scrolling from top to bottom. Each scroll step helps trigger lazy loading.
scroll_delay=0.5
Pause half a second between each scroll step. Helps the site load images before continuing.
When to Use:

Lazy-Loading: If images appear only when the user scrolls into view, scan_full_page + scroll_delay helps the crawler see them.
Heavier Pages: If a page is extremely long, be mindful that scanning the entire page can be slow. Adjust scroll_delay or the max scroll steps as needed.
Combining with Other Link & Media Filters
You can still combine lazy-load logic with the usual exclude_external_images, exclude_domains, or link filtration:

config = CrawlerRunConfig(
wait_for_images=True,
scan_full_page=True,
scroll_delay=0.5,

    # Filter out external images if you only want local ones
    exclude_external_images=True,

    # Exclude certain domains for links
    exclude_domains=["spammycdn.com"],

)
Copy
This approach ensures you see all images from the main domain while ignoring external ones, and the crawler physically scrolls the entire page so that lazy-loading triggers.

Tips & Troubleshooting
1. Long Pages

- Setting scan_full_page=True on extremely long or infinite-scroll pages can be resource-intensive.
- Consider using hooks or specialized logic to load specific sections or “Load More” triggers repeatedly.

  2. Mixed Image Behavior

- Some sites load images in batches as you scroll. If you’re missing images, increase your scroll_delay or call multiple partial scrolls in a loop with JS code or hooks.

  3. Combining with Dynamic Wait

- If the site has a placeholder that only changes to a real image after a certain event, you might do wait_for="css:img.loaded" or a custom JS wait_for.

  4. Caching

- If cache_mode is enabled, repeated crawls might skip some network fetches. If you suspect caching is missing new images, set cache_mode=CacheMode.BYPASS for fresh fetches.

With lazy-loading support, wait_for_images, and scan_full_page settings, you can capture the entire gallery or feed of images you expect—even if the site only loads them as the user scrolls. Combine these with the standard media filtering and domain exclusion for a complete link & media handling strategy.

Advanced Multi-URL Crawling with Dispatchers
Heads Up: Crawl4AI supports advanced dispatchers for parallel or throttled crawling, providing dynamic rate limiting and memory usage checks. The built-in arun_many() function uses these dispatchers to handle concurrency efficiently.

1. Introduction
   When crawling many URLs:

Basic: Use arun() in a loop (simple but less efficient)
Better: Use arun_many(), which efficiently handles multiple URLs with proper concurrency control
Best: Customize dispatcher behavior for your specific needs (memory management, rate limits, etc.)
Why Dispatchers?

Adaptive: Memory-based dispatchers can pause or slow down based on system resources
Rate-limiting: Built-in rate limiting with exponential backoff for 429/503 responses
Real-time Monitoring: Live dashboard of ongoing tasks, memory usage, and performance
Flexibility: Choose between memory-adaptive or semaphore-based concurrency 2. Core Components
2.1 Rate Limiter
class RateLimiter:
def **init**( # Random delay range between requests
base_delay: Tuple[float, float] = (1.0, 3.0),

        # Maximum backoff delay
        max_delay: float = 60.0,

        # Retries before giving up
        max_retries: int = 3,

        # Status codes triggering backoff
        rate_limit_codes: List[int] = [429, 503]
    )

Copy
Here’s the revised and simplified explanation of the RateLimiter, focusing on constructor parameters and adhering to your markdown style and mkDocs guidelines.

RateLimiter Constructor Parameters
The RateLimiter is a utility that helps manage the pace of requests to avoid overloading servers or getting blocked due to rate limits. It operates internally to delay requests and handle retries but can be configured using its constructor parameters.

Parameters of the RateLimiter constructor:

1. base_delay (Tuple[float, float], default: (1.0, 3.0))
  The range for a random delay (in seconds) between consecutive requests to the same domain.

A random delay is chosen between base_delay[0] and base_delay[1] for each request.
This prevents sending requests at a predictable frequency, reducing the chances of triggering rate limits.
Example:
If base_delay = (2.0, 5.0), delays could be randomly chosen as 2.3s, 4.1s, etc.

2. max_delay (float, default: 60.0)
  The maximum allowable delay when rate-limiting errors occur.

When servers return rate-limit responses (e.g., 429 or 503), the delay increases exponentially with jitter.
The max_delay ensures the delay doesn’t grow unreasonably high, capping it at this value.
Example:
For a max_delay = 30.0, even if backoff calculations suggest a delay of 45s, it will cap at 30s.

3. max_retries (int, default: 3)
  The maximum number of retries for a request if rate-limiting errors occur.

After encountering a rate-limit response, the RateLimiter retries the request up to this number of times.
If all retries fail, the request is marked as failed, and the process continues.
Example:
If max_retries = 3, the system retries a failed request three times before giving up.

4. rate_limit_codes (List[int], default: [429, 503])
  A list of HTTP status codes that trigger the rate-limiting logic.

These status codes indicate the server is overwhelmed or actively limiting requests.
You can customize this list to include other codes based on specific server behavior.
Example:
If rate_limit_codes = [429, 503, 504], the crawler will back off on these three error codes.

How to Use the RateLimiter:

Here’s an example of initializing and using a RateLimiter in your project:

from crawl4ai import RateLimiter

# Create a RateLimiter with custom settings

rate_limiter = RateLimiter(
base_delay=(2.0, 4.0), # Random delay between 2-4 seconds
max_delay=30.0, # Cap delay at 30 seconds
max_retries=5, # Retry up to 5 times on rate-limiting errors
rate_limit_codes=[429, 503] # Handle these HTTP status codes
)

# RateLimiter will handle delays and retries internally

# No additional setup is required for its operation

Copy
The RateLimiter integrates seamlessly with dispatchers like MemoryAdaptiveDispatcher and SemaphoreDispatcher, ensuring requests are paced correctly without user intervention. Its internal mechanisms manage delays and retries to avoid overwhelming servers while maximizing efficiency.

2.2 Crawler Monitor
The CrawlerMonitor provides real-time visibility into crawling operations:

from crawl4ai import CrawlerMonitor, DisplayMode
monitor = CrawlerMonitor( # Maximum rows in live display
max_visible_rows=15,

    # DETAILED or AGGREGATED view
    display_mode=DisplayMode.DETAILED

)
Copy
Display Modes:

DETAILED: Shows individual task status, memory usage, and timing
AGGREGATED: Displays summary statistics and overall progress 3. Available Dispatchers
3.1 MemoryAdaptiveDispatcher (Default)
Automatically manages concurrency based on system memory usage:

from crawl4ai.async_dispatcher import MemoryAdaptiveDispatcher

dispatcher = MemoryAdaptiveDispatcher(
memory_threshold_percent=90.0, # Pause if memory exceeds this
check_interval=1.0, # How often to check memory
max_session_permit=10, # Maximum concurrent tasks
rate_limiter=RateLimiter( # Optional rate limiting
base_delay=(1.0, 2.0),
max_delay=30.0,
max_retries=2
),
monitor=CrawlerMonitor( # Optional monitoring
max_visible_rows=15,
display_mode=DisplayMode.DETAILED
)
)
Copy
Constructor Parameters:

1. memory_threshold_percent (float, default: 90.0)
  Specifies the memory usage threshold (as a percentage). If system memory usage exceeds this value, the dispatcher pauses crawling to prevent system overload.

2. check_interval (float, default: 1.0)
  The interval (in seconds) at which the dispatcher checks system memory usage.

3. max_session_permit (int, default: 10)
  The maximum number of concurrent crawling tasks allowed. This ensures resource limits are respected while maintaining concurrency.

4. memory_wait_timeout (float, default: 300.0)
  Optional timeout (in seconds). If memory usage exceeds memory_threshold_percent for longer than this duration, a MemoryError is raised.

5. rate_limiter (RateLimiter, default: None)
  Optional rate-limiting logic to avoid server-side blocking (e.g., for handling 429 or 503 errors). See RateLimiter for details.

6. monitor (CrawlerMonitor, default: None)
  Optional monitoring for real-time task tracking and performance insights. See CrawlerMonitor for details.

3.2 SemaphoreDispatcher
Provides simple concurrency control with a fixed limit:

from crawl4ai.async_dispatcher import SemaphoreDispatcher

dispatcher = SemaphoreDispatcher(
max_session_permit=20, # Maximum concurrent tasks
rate_limiter=RateLimiter( # Optional rate limiting
base_delay=(0.5, 1.0),
max_delay=10.0
),
monitor=CrawlerMonitor( # Optional monitoring
max_visible_rows=15,
display_mode=DisplayMode.DETAILED
)
)
Copy
Constructor Parameters:

1. max_session_permit (int, default: 20)
  The maximum number of concurrent crawling tasks allowed, irrespective of semaphore slots.

2. rate_limiter (RateLimiter, default: None)
  Optional rate-limiting logic to avoid overwhelming servers. See RateLimiter for details.

3. monitor (CrawlerMonitor, default: None)
  Optional monitoring for tracking task progress and resource usage. See CrawlerMonitor for details.

4.  Usage Examples
    4.1 Batch Processing (Default)
    async def crawl_batch():
    browser_config = BrowserConfig(headless=True, verbose=False)
    run_config = CrawlerRunConfig(
    cache_mode=CacheMode.BYPASS,
    stream=False # Default: get all results at once
    )

        dispatcher = MemoryAdaptiveDispatcher(
            memory_threshold_percent=70.0,
            check_interval=1.0,
            max_session_permit=10,
            monitor=CrawlerMonitor(
                display_mode=DisplayMode.DETAILED
            )
        )

        async with AsyncWebCrawler(config=browser_config) as crawler:
            # Get all results at once
            results = await crawler.arun_many(
                urls=urls,
                config=run_config,
                dispatcher=dispatcher
            )

            # Process all results after completion
            for result in results:
                if result.success:
                    await process_result(result)
                else:
                    print(f"Failed to crawl {result.url}: {result.error_message}")

    Copy
    Review:

- Purpose: Executes a batch crawl with all URLs processed together after crawling is complete.
- Dispatcher: Uses MemoryAdaptiveDispatcher to manage concurrency and system memory.
- Stream: Disabled (stream=False), so all results are collected at once for post-processing.
- Best Use Case: When you need to analyze results in bulk rather than individually during the crawl.

  4.2 Streaming Mode
  async def crawl_streaming():
  browser_config = BrowserConfig(headless=True, verbose=False)
  run_config = CrawlerRunConfig(
  cache_mode=CacheMode.BYPASS,
  stream=True # Enable streaming mode
  )

      dispatcher = MemoryAdaptiveDispatcher(
          memory_threshold_percent=70.0,
          check_interval=1.0,
          max_session_permit=10,
          monitor=CrawlerMonitor(
              display_mode=DisplayMode.DETAILED
          )
      )

      async with AsyncWebCrawler(config=browser_config) as crawler:
          # Process results as they become available
          async for result in await crawler.arun_many(
              urls=urls,
              config=run_config,
              dispatcher=dispatcher
          ):
              if result.success:
                  # Process each result immediately
                  await process_result(result)
              else:
                  print(f"Failed to crawl {result.url}: {result.error_message}")

  Copy
  Review:

- Purpose: Enables streaming to process results as soon as they’re available.
- Dispatcher: Uses MemoryAdaptiveDispatcher for concurrency and memory management.
- Stream: Enabled (stream=True), allowing real-time processing during crawling.
- Best Use Case: When you need to act on results immediately, such as for real-time analytics or progressive data storage.

  4.3 Semaphore-based Crawling
  async def crawl_with_semaphore(urls):
  browser_config = BrowserConfig(headless=True, verbose=False)
  run_config = CrawlerRunConfig(cache_mode=CacheMode.BYPASS)

      dispatcher = SemaphoreDispatcher(
          semaphore_count=5,
          rate_limiter=RateLimiter(
              base_delay=(0.5, 1.0),
              max_delay=10.0
          ),
          monitor=CrawlerMonitor(
              max_visible_rows=15,
              display_mode=DisplayMode.DETAILED
          )
      )

      async with AsyncWebCrawler(config=browser_config) as crawler:
          results = await crawler.arun_many(
              urls,
              config=run_config,
              dispatcher=dispatcher
          )
          return results

  Copy
  Review:

- Purpose: Uses SemaphoreDispatcher to limit concurrency with a fixed number of slots.
- Dispatcher: Configured with a semaphore to control parallel crawling tasks.
- Rate Limiter: Prevents servers from being overwhelmed by pacing requests.
- Best Use Case: When you want precise control over the number of concurrent requests, independent of system memory.

  4.4 Robots.txt Consideration
  import asyncio
  from crawl4ai import AsyncWebCrawler, CrawlerRunConfig, CacheMode

async def main():
urls = [
"https://example1.com",
"https://example2.com",
"https://example3.com"
]

    config = CrawlerRunConfig(
        cache_mode=CacheMode.ENABLED,
        check_robots_txt=True,  # Will respect robots.txt for each URL
        semaphore_count=3      # Max concurrent requests
    )

    async with AsyncWebCrawler() as crawler:
        async for result in crawler.arun_many(urls, config=config):
            if result.success:
                print(f"Successfully crawled {result.url}")
            elif result.status_code == 403 and "robots.txt" in result.error_message:
                print(f"Skipped {result.url} - blocked by robots.txt")
            else:
                print(f"Failed to crawl {result.url}: {result.error_message}")

if **name** == "**main**":
asyncio.run(main())
Copy
Review:

- Purpose: Ensures compliance with robots.txt rules for ethical and legal web crawling.
- Configuration: Set check_robots_txt=True to validate each URL against robots.txt before crawling.
- Dispatcher: Handles requests with concurrency limits (semaphore_count=3).
- Best Use Case: When crawling websites that strictly enforce robots.txt policies or for responsible crawling practices.

5. Dispatch Results
   Each crawl result includes dispatch information:

@dataclass
class DispatchResult:
task_id: str
memory_usage: float
peak_memory: float
start_time: datetime
end_time: datetime
error_message: str = ""
Copy
Access via result.dispatch_result:

for result in results:
if result.success:
dr = result.dispatch_result
print(f"URL: {result.url}")
print(f"Memory: {dr.memory_usage:.1f}MB")
print(f"Duration: {dr.end_time - dr.start_time}")
Copy 6. Summary
1. Two Dispatcher Types:

MemoryAdaptiveDispatcher (default): Dynamic concurrency based on memory
SemaphoreDispatcher: Fixed concurrency limit
2. Optional Components:

RateLimiter: Smart request pacing and backoff
CrawlerMonitor: Real-time progress visualization
3. Key Benefits:

Automatic memory management
Built-in rate limiting
Live progress monitoring
Flexible concurrency control
Choose the dispatcher that best fits your needs:

MemoryAdaptiveDispatcher: For large crawls or limited resources
SemaphoreDispatcher: For simple, fixed-concurrency scenarios

Extracting JSON (No LLM)
One of Crawl4AI's most powerful features is extracting structured JSON from websites without relying on large language models. Crawl4AI offers several strategies for LLM-free extraction:

Schema-based extraction with CSS or XPath selectors via JsonCssExtractionStrategy and JsonXPathExtractionStrategy
Regular expression extraction with RegexExtractionStrategy for fast pattern matching
These approaches let you extract data instantly—even from complex or nested HTML structures—without the cost, latency, or environmental impact of an LLM.

Why avoid LLM for basic extractions?

Faster & Cheaper: No API calls or GPU overhead.
Lower Carbon Footprint: LLM inference can be energy-intensive. Pattern-based extraction is practically carbon-free.
Precise & Repeatable: CSS/XPath selectors and regex patterns do exactly what you specify. LLM outputs can vary or hallucinate.
Scales Readily: For thousands of pages, pattern-based extraction runs quickly and in parallel.
Below, we'll explore how to craft these schemas and use them with JsonCssExtractionStrategy (or JsonXPathExtractionStrategy if you prefer XPath). We'll also highlight advanced features like nested fields and base element attributes.

1. Intro to Schema-Based Extraction
   A schema defines:

A base selector that identifies each "container" element on the page (e.g., a product row, a blog post card).
Fields describing which CSS/XPath selectors to use for each piece of data you want to capture (text, attribute, HTML block, etc.).
Nested or list types for repeated or hierarchical structures.
For example, if you have a list of products, each one might have a name, price, reviews, and "related products." This approach is faster and more reliable than an LLM for consistent, structured pages.

2. Simple Example: Crypto Prices
   Let's begin with a simple schema-based extraction using the JsonCssExtractionStrategy. Below is a snippet that extracts cryptocurrency prices from a site (similar to the legacy Coinbase example). Notice we don't call any LLM:

import json
import asyncio
from crawl4ai import AsyncWebCrawler, CrawlerRunConfig, CacheMode
from crawl4ai.extraction_strategy import JsonCssExtractionStrategy

async def extract_crypto_prices(): # 1. Define a simple extraction schema
schema = {
"name": "Crypto Prices",
"baseSelector": "div.crypto-row", # Repeated elements
"fields": [
{
"name": "coin_name",
"selector": "h2.coin-name",
"type": "text"
},
{
"name": "price",
"selector": "span.coin-price",
"type": "text"
}
]
}

    # 2. Create the extraction strategy
    extraction_strategy = JsonCssExtractionStrategy(schema, verbose=True)

    # 3. Set up your crawler config (if needed)
    config = CrawlerRunConfig(
        # e.g., pass js_code or wait_for if the page is dynamic
        # wait_for="css:.crypto-row:nth-child(20)"
        cache_mode = CacheMode.BYPASS,
        extraction_strategy=extraction_strategy,
    )

    async with AsyncWebCrawler(verbose=True) as crawler:
        # 4. Run the crawl and extraction
        result = await crawler.arun(
            url="https://example.com/crypto-prices",

            config=config
        )

        if not result.success:
            print("Crawl failed:", result.error_message)
            return

        # 5. Parse the extracted JSON
        data = json.loads(result.extracted_content)
        print(f"Extracted {len(data)} coin entries")
        print(json.dumps(data[0], indent=2) if data else "No data found")

asyncio.run(extract_crypto_prices())
Copy
Highlights:

baseSelector: Tells us where each "item" (crypto row) is.
fields: Two fields (coin_name, price) using simple CSS selectors.
Each field defines a type (e.g., text, attribute, html, regex, etc.).
No LLM is needed, and the performance is near-instant for hundreds or thousands of items.

XPath Example with raw:// HTML
Below is a short example demonstrating XPath extraction plus the raw:// scheme. We'll pass a dummy HTML directly (no network request) and define the extraction strategy in CrawlerRunConfig.

import json
import asyncio
from crawl4ai import AsyncWebCrawler, CrawlerRunConfig
from crawl4ai.extraction_strategy import JsonXPathExtractionStrategy

async def extract_crypto_prices_xpath(): # 1. Minimal dummy HTML with some repeating rows
dummy_html = """
<html>
<body>
<div class='crypto-row'>
<h2 class='coin-name'>Bitcoin</h2>
<span class='coin-price'>$28,000</span>
</div>
<div class='crypto-row'>
<h2 class='coin-name'>Ethereum</h2>
<span class='coin-price'>$1,800</span>
</div>
</body>
</html>
"""

    # 2. Define the JSON schema (XPath version)
    schema = {
        "name": "Crypto Prices via XPath",
        "baseSelector": "//div[@class='crypto-row']",
        "fields": [
            {
                "name": "coin_name",
                "selector": ".//h2[@class='coin-name']",
                "type": "text"
            },
            {
                "name": "price",
                "selector": ".//span[@class='coin-price']",
                "type": "text"
            }
        ]
    }

    # 3. Place the strategy in the CrawlerRunConfig
    config = CrawlerRunConfig(
        extraction_strategy=JsonXPathExtractionStrategy(schema, verbose=True)
    )

    # 4. Use raw:// scheme to pass dummy_html directly
    raw_url = f"raw://{dummy_html}"

    async with AsyncWebCrawler(verbose=True) as crawler:
        result = await crawler.arun(
            url=raw_url,
            config=config
        )

        if not result.success:
            print("Crawl failed:", result.error_message)
            return

        data = json.loads(result.extracted_content)
        print(f"Extracted {len(data)} coin rows")
        if data:
            print("First item:", data[0])

asyncio.run(extract_crypto_prices_xpath())
Copy
Key Points:

JsonXPathExtractionStrategy is used instead of JsonCssExtractionStrategy.
baseSelector and each field's "selector" use XPath instead of CSS.
raw:// lets us pass dummy_html with no real network request—handy for local testing.
Everything (including the extraction strategy) is in CrawlerRunConfig.
That's how you keep the config self-contained, illustrate XPath usage, and demonstrate the raw scheme for direct HTML input—all while avoiding the old approach of passing extraction_strategy directly to arun().

3. Advanced Schema & Nested Structures
   Real sites often have nested or repeated data—like categories containing products, which themselves have a list of reviews or features. For that, we can define nested or list (and even nested_list) fields.

Sample E-Commerce HTML
We have a sample e-commerce HTML file on GitHub (example):

https://gist.githubusercontent.com/githubusercontent/2d7b8ba3cd8ab6cf3c8da771ddb36878/raw/1ae2f90c6861ce7dd84cc50d3df9920dee5e1fd2/sample_ecommerce.html
Copy
This snippet includes categories, products, features, reviews, and related items. Let's see how to define a schema that fully captures that structure without LLM.
schema = {
"name": "E-commerce Product Catalog",
"baseSelector": "div.category", # (1) We can define optional baseFields if we want to extract attributes # from the category container
"baseFields": [
{"name": "data_cat_id", "type": "attribute", "attribute": "data-cat-id"},
],
"fields": [
{
"name": "category_name",
"selector": "h2.category-name",
"type": "text"
},
{
"name": "products",
"selector": "div.product",
"type": "nested_list", # repeated sub-objects
"fields": [
{
"name": "name",
"selector": "h3.product-name",
"type": "text"
},
{
"name": "price",
"selector": "p.product-price",
"type": "text"
},
{
"name": "details",
"selector": "div.product-details",
"type": "nested", # single sub-object
"fields": [
{
"name": "brand",
"selector": "span.brand",
"type": "text"
},
{
"name": "model",
"selector": "span.model",
"type": "text"
}
]
},
{
"name": "features",
"selector": "ul.product-features li",
"type": "list",
"fields": [
{"name": "feature", "type": "text"}
]
},
{
"name": "reviews",
"selector": "div.review",
"type": "nested_list",
"fields": [
{
"name": "reviewer",
"selector": "span.reviewer",
"type": "text"
},
{
"name": "rating",
"selector": "span.rating",
"type": "text"
},
{
"name": "comment",
"selector": "p.review-text",
"type": "text"
}
]
},
{
"name": "related_products",
"selector": "ul.related-products li",
"type": "list",
"fields": [
{
"name": "name",
"selector": "span.related-name",
"type": "text"
},
{
"name": "price",
"selector": "span.related-price",
"type": "text"
}
]
}
]
}
]
}
Copy
Key Takeaways:

Nested vs. List:
type: "nested" means a single sub-object (like details).
type: "list" means multiple items that are simple dictionaries or single text fields.
type: "nested_list" means repeated complex objects (like products or reviews).
Base Fields: We can extract attributes from the container element via "baseFields". For instance, "data_cat_id" might be data-cat-id="elect123".
Transforms: We can also define a transform if we want to lower/upper case, strip whitespace, or even run a custom function.
Running the Extraction
import json
import asyncio
from crawl4ai import AsyncWebCrawler, CrawlerRunConfig
from crawl4ai.extraction_strategy import JsonCssExtractionStrategy

ecommerce_schema = { # ... the advanced schema from above ...
}

async def extract_ecommerce_data():
strategy = JsonCssExtractionStrategy(ecommerce_schema, verbose=True)

    config = CrawlerRunConfig()

    async with AsyncWebCrawler(verbose=True) as crawler:
        result = await crawler.arun(
            url="https://gist.githubusercontent.com/githubusercontent/2d7b8ba3cd8ab6cf3c8da771ddb36878/raw/1ae2f90c6861ce7dd84cc50d3df9920dee5e1fd2/sample_ecommerce.html",
            extraction_strategy=strategy,
            config=config
        )

        if not result.success:
            print("Crawl failed:", result.error_message)
            return

        # Parse the JSON output
        data = json.loads(result.extracted_content)
        print(json.dumps(data, indent=2) if data else "No data found.")

asyncio.run(extract_ecommerce_data())
Copy
If all goes well, you get a structured JSON array with each "category," containing an array of products. Each product includes details, features, reviews, etc. All of that without an LLM.

4. RegexExtractionStrategy - Fast Pattern-Based Extraction
   Crawl4AI now offers a powerful new zero-LLM extraction strategy: RegexExtractionStrategy. This strategy provides lightning-fast extraction of common data types like emails, phone numbers, URLs, dates, and more using pre-compiled regular expressions.

Key Features
Zero LLM Dependency: Extracts data without any AI model calls
Blazing Fast: Uses pre-compiled regex patterns for maximum performance
Built-in Patterns: Includes ready-to-use patterns for common data types
Custom Patterns: Add your own regex patterns for domain-specific extraction
LLM-Assisted Pattern Generation: Optionally use an LLM once to generate optimized patterns, then reuse them without further LLM calls
Simple Example: Extracting Common Entities
The easiest way to start is by using the built-in pattern catalog:

import json
import asyncio
from crawl4ai import (
AsyncWebCrawler,
CrawlerRunConfig,
RegexExtractionStrategy
)

async def extract_with_regex(): # Create a strategy using built-in patterns for URLs and currencies
strategy = RegexExtractionStrategy(
pattern = RegexExtractionStrategy.Url | RegexExtractionStrategy.Currency
)

    config = CrawlerRunConfig(extraction_strategy=strategy)

    async with AsyncWebCrawler() as crawler:
        result = await crawler.arun(
            url="https://example.com",
            config=config
        )

        if result.success:
            data = json.loads(result.extracted_content)
            for item in data[:5]:  # Show first 5 matches
                print(f"{item['label']}: {item['value']}")
            print(f"Total matches: {len(data)}")

asyncio.run(extract_with_regex())
Copy
Available Built-in Patterns
RegexExtractionStrategy provides these common patterns as IntFlag attributes for easy combining:

# Use individual patterns

strategy = RegexExtractionStrategy(pattern=RegexExtractionStrategy.Email)

# Combine multiple patterns

strategy = RegexExtractionStrategy(
pattern = (
RegexExtractionStrategy.Email |
RegexExtractionStrategy.PhoneUS |
RegexExtractionStrategy.Url
)
)

# Use all available patterns

strategy = RegexExtractionStrategy(pattern=RegexExtractionStrategy.All)
Copy
Available patterns include: - Email - Email addresses - PhoneIntl - International phone numbers - PhoneUS - US-format phone numbers - Url - HTTP/HTTPS URLs - IPv4 - IPv4 addresses - IPv6 - IPv6 addresses - Uuid - UUIDs - Currency - Currency values (USD, EUR, etc.) - Percentage - Percentage values - Number - Numeric values - DateIso - ISO format dates - DateUS - US format dates - Time24h - 24-hour format times - PostalUS - US postal codes - PostalUK - UK postal codes - HexColor - HTML hex color codes - TwitterHandle - Twitter handles - Hashtag - Hashtags - MacAddr - MAC addresses - Iban - International bank account numbers - CreditCard - Credit card numbers

Custom Pattern Example
For more targeted extraction, you can provide custom patterns:

import json
import asyncio
from crawl4ai import (
AsyncWebCrawler,
CrawlerRunConfig,
RegexExtractionStrategy
)

async def extract_prices(): # Define a custom pattern for US Dollar prices
price_pattern = {"usd_price": r"\$\s?\d{1,3}(?:,\d{3})\*(?:\.\d{2})?"}

    # Create strategy with custom pattern
    strategy = RegexExtractionStrategy(custom=price_pattern)
    config = CrawlerRunConfig(extraction_strategy=strategy)

    async with AsyncWebCrawler() as crawler:
        result = await crawler.arun(
            url="https://www.example.com/products",
            config=config
        )

        if result.success:
            data = json.loads(result.extracted_content)
            for item in data:
                print(f"Found price: {item['value']}")

asyncio.run(extract_prices())
Copy
LLM-Assisted Pattern Generation
For complex or site-specific patterns, you can use an LLM once to generate an optimized pattern, then save and reuse it without further LLM calls:

import json
import asyncio
from pathlib import Path
from crawl4ai import (
AsyncWebCrawler,
CrawlerRunConfig,
RegexExtractionStrategy,
LLMConfig
)

async def extract_with_generated_pattern():
cache_dir = Path("./pattern_cache")
cache_dir.mkdir(exist_ok=True)
pattern_file = cache_dir / "price_pattern.json"

    # 1. Generate or load pattern
    if pattern_file.exists():
        pattern = json.load(pattern_file.open())
        print(f"Using cached pattern: {pattern}")
    else:
        print("Generating pattern via LLM...")

        # Configure LLM
        llm_config = LLMConfig(
            provider="openai/gpt-4o-mini",
            api_token="env:OPENAI_API_KEY",
        )

        # Get sample HTML for context
        async with AsyncWebCrawler() as crawler:
            result = await crawler.arun("https://example.com/products")
            html = result.fit_html

        # Generate pattern (one-time LLM usage)
        pattern = RegexExtractionStrategy.generate_pattern(
            label="price",
            html=html,
            query="Product prices in USD format",
            llm_config=llm_config,
        )

        # Cache pattern for future use
        json.dump(pattern, pattern_file.open("w"), indent=2)

    # 2. Use pattern for extraction (no LLM calls)
    strategy = RegexExtractionStrategy(custom=pattern)
    config = CrawlerRunConfig(extraction_strategy=strategy)

    async with AsyncWebCrawler() as crawler:
        result = await crawler.arun(
            url="https://example.com/products",
            config=config
        )

        if result.success:
            data = json.loads(result.extracted_content)
            for item in data[:10]:
                print(f"Extracted: {item['value']}")
            print(f"Total matches: {len(data)}")

asyncio.run(extract_with_generated_pattern())
Copy
This pattern allows you to: 1. Use an LLM once to generate a highly optimized regex for your specific site 2. Save the pattern to disk for reuse 3. Extract data using only regex (no further LLM calls) in production

Extraction Results Format
The RegexExtractionStrategy returns results in a consistent format:

[
{
"url": "https://example.com",
"label": "email",
"value": "<EMAIL>",
"span": [145, 163]
},
{
"url": "https://example.com",
"label": "url",
"value": "https://support.example.com",
"span": [210, 235]
}
]
Copy
Each match includes: - url: The source URL - label: The pattern name that matched (e.g., "email", "phone_us") - value: The extracted text - span: The start and end positions in the source content

5. Why "No LLM" Is Often Better
   Zero Hallucination: Pattern-based extraction doesn't guess text. It either finds it or not.
   Guaranteed Structure: The same schema or regex yields consistent JSON across many pages, so your downstream pipeline can rely on stable keys.
   Speed: LLM-based extraction can be 10–1000x slower for large-scale crawling.
   Scalable: Adding or updating a field is a matter of adjusting the schema or regex, not re-tuning a model.
   When might you consider an LLM? Possibly if the site is extremely unstructured or you want AI summarization. But always try a schema or regex approach first for repeated or consistent data patterns.

6. Base Element Attributes & Additional Fields
   It's easy to extract attributes (like href, src, or data-xxx) from your base or nested elements using:

{
"name": "href",
"type": "attribute",
"attribute": "href",
"default": null
}
Copy
You can define them in baseFields (extracted from the main container element) or in each field's sub-lists. This is especially helpful if you need an item's link or ID stored in the parent <div>.

7. Putting It All Together: Larger Example
   Consider a blog site. We have a schema that extracts the URL from each post card (via baseFields with an "attribute": "href"), plus the title, date, summary, and author:

schema = {
"name": "Blog Posts",
"baseSelector": "a.blog-post-card",
"baseFields": [
{"name": "post_url", "type": "attribute", "attribute": "href"}
],
"fields": [
{"name": "title", "selector": "h2.post-title", "type": "text", "default": "No Title"},
{"name": "date", "selector": "time.post-date", "type": "text", "default": ""},
{"name": "summary", "selector": "p.post-summary", "type": "text", "default": ""},
{"name": "author", "selector": "span.post-author", "type": "text", "default": ""}
]
}
Copy
Then run with JsonCssExtractionStrategy(schema) to get an array of blog post objects, each with "post_url", "title", "date", "summary", "author".

8. Tips & Best Practices
   Inspect the DOM in Chrome DevTools or Firefox's Inspector to find stable selectors.
   Start Simple: Verify you can extract a single field. Then add complexity like nested objects or lists.
   Test your schema on partial HTML or a test page before a big crawl.
   Combine with JS Execution if the site loads content dynamically. You can pass js_code or wait_for in CrawlerRunConfig.
   Look at Logs when verbose=True: if your selectors are off or your schema is malformed, it'll often show warnings.
   Use baseFields if you need attributes from the container element (e.g., href, data-id), especially for the "parent" item.
   Performance: For large pages, make sure your selectors are as narrow as possible.
   Consider Using Regex First: For simple data types like emails, URLs, and dates, RegexExtractionStrategy is often the fastest approach.
9. Schema Generation Utility
   While manually crafting schemas is powerful and precise, Crawl4AI now offers a convenient utility to automatically generate extraction schemas using LLM. This is particularly useful when:

You're dealing with a new website structure and want a quick starting point
You need to extract complex nested data structures
You want to avoid the learning curve of CSS/XPath selector syntax
Using the Schema Generator
The schema generator is available as a static method on both JsonCssExtractionStrategy and JsonXPathExtractionStrategy. You can choose between OpenAI's GPT-4 or the open-source Ollama for schema generation:

from crawl4ai.extraction_strategy import JsonCssExtractionStrategy, JsonXPathExtractionStrategy
from crawl4ai import LLMConfig

# Sample HTML with product information

html = """

<div class="product-card">
    <h2 class="title">Gaming Laptop</h2>
    <div class="price">$999.99</div>
    <div class="specs">
        <ul>
            <li>16GB RAM</li>
            <li>1TB SSD</li>
        </ul>
    </div>
</div>
"""

# Option 1: Using OpenAI (requires API token)

css_schema = JsonCssExtractionStrategy.generate_schema(
html,
schema_type="css",
llm_config = LLMConfig(provider="openai/gpt-4o",api_token="your-openai-token")
)

# Option 2: Using Ollama (open source, no token needed)

xpath_schema = JsonXPathExtractionStrategy.generate_schema(
html,
schema_type="xpath",
llm_config = LLMConfig(provider="ollama/llama3.3", api_token=None) # Not needed for Ollama
)

# Use the generated schema for fast, repeated extractions

strategy = JsonCssExtractionStrategy(css_schema)
Copy
LLM Provider Options
OpenAI GPT-4 (openai/gpt4o)
Default provider
Requires an API token
Generally provides more accurate schemas
Set via environment variable: OPENAI_API_KEY

Ollama (ollama/llama3.3)

Open source alternative
No API token required
Self-hosted option
Good for development and testing
Benefits of Schema Generation
One-Time Cost: While schema generation uses LLM, it's a one-time cost. The generated schema can be reused for unlimited extractions without further LLM calls.
Smart Pattern Recognition: The LLM analyzes the HTML structure and identifies common patterns, often producing more robust selectors than manual attempts.
Automatic Nesting: Complex nested structures are automatically detected and properly represented in the schema.
Learning Tool: The generated schemas serve as excellent examples for learning how to write your own schemas.
Best Practices
Review Generated Schemas: While the generator is smart, always review and test the generated schema before using it in production.
Provide Representative HTML: The better your sample HTML represents the overall structure, the more accurate the generated schema will be.
Consider Both CSS and XPath: Try both schema types and choose the one that works best for your specific case.
Cache Generated Schemas: Since generation uses LLM, save successful schemas for reuse.
API Token Security: Never hardcode API tokens. Use environment variables or secure configuration management.
Choose Provider Wisely:
Use OpenAI for production-quality schemas
Use Ollama for development, testing, or when you need a self-hosted solution 10. Conclusion
With Crawl4AI's LLM-free extraction strategies - JsonCssExtractionStrategy, JsonXPathExtractionStrategy, and now RegexExtractionStrategy - you can build powerful pipelines that:

Scrape any consistent site for structured data.
Support nested objects, repeating lists, or pattern-based extraction.
Scale to thousands of pages quickly and reliably.
Choosing the Right Strategy:

Use RegexExtractionStrategy for fast extraction of common data types like emails, phones, URLs, dates, etc.
Use JsonCssExtractionStrategy or JsonXPathExtractionStrategy for structured data with clear HTML patterns
If you need both: first extract structured data with JSON strategies, then use regex on specific fields
Remember: For repeated, structured data, you don't need to pay for or wait on an LLM. Well-crafted schemas and regex patterns get you the data faster, cleaner, and cheaper—the real power of Crawl4AI.

Last Updated: 2025-05-02

That's it for Extracting JSON (No LLM)! You've seen how schema-based approaches (either CSS or XPath) and regex patterns can handle everything from simple lists to deeply nested product catalogs—instantly, with minimal overhead. Enjoy building robust scrapers that produce consistent, structured JSON for your data pipelines!

AsyncWebCrawler
The AsyncWebCrawler is the core class for asynchronous web crawling in Crawl4AI. You typically create it once, optionally customize it with a BrowserConfig (e.g., headless, user agent), then run multiple arun() calls with different CrawlerRunConfig objects.

Recommended usage:

1. Create a BrowserConfig for global browser settings.

2. Instantiate AsyncWebCrawler(config=browser_config).

3. Use the crawler in an async context manager (async with) or manage start/close manually.

4. Call arun(url, config=crawler_run_config) for each page you want.

1. Constructor Overview
class AsyncWebCrawler:
def **init**(
self,
crawler_strategy: Optional[AsyncCrawlerStrategy] = None,
config: Optional[BrowserConfig] = None,
always_bypass_cache: bool = False, # deprecated
always_by_pass_cache: Optional[bool] = None, # also deprecated
base_directory: str = ...,
thread_safe: bool = False,
\*\*kwargs,
):
"""
Create an AsyncWebCrawler instance.

        Args:
            crawler_strategy:
                (Advanced) Provide a custom crawler strategy if needed.
            config:
                A BrowserConfig object specifying how the browser is set up.
            always_bypass_cache:
                (Deprecated) Use CrawlerRunConfig.cache_mode instead.
            base_directory:
                Folder for storing caches/logs (if relevant).
            thread_safe:
                If True, attempts some concurrency safeguards. Usually False.
            **kwargs:
                Additional legacy or debugging parameters.
        """
    )

### Typical Initialization

````python
from crawl4ai import AsyncWebCrawler, BrowserConfig

browser_cfg = BrowserConfig(
    browser_type="chromium",
    headless=True,
    verbose=True
)

crawler = AsyncWebCrawler(config=browser_cfg)
Copy
Notes:

Legacy parameters like always_bypass_cache remain for backward compatibility, but prefer to set caching in CrawlerRunConfig.
2. Lifecycle: Start/Close or Context Manager
2.1 Context Manager (Recommended)
async with AsyncWebCrawler(config=browser_cfg) as crawler:
    result = await crawler.arun("https://example.com")
    # The crawler automatically starts/closes resources
Copy
When the async with block ends, the crawler cleans up (closes the browser, etc.).

2.2 Manual Start & Close
crawler = AsyncWebCrawler(config=browser_cfg)
await crawler.start()

result1 = await crawler.arun("https://example.com")
result2 = await crawler.arun("https://another.com")

await crawler.close()
Copy
Use this style if you have a long-running application or need full control of the crawler’s lifecycle.

3. Primary Method: arun()
async def arun(
    self,
    url: str,
    config: Optional[CrawlerRunConfig] = None,
    # Legacy parameters for backward compatibility...
) -> CrawlResult:
    ...
Copy
3.1 New Approach
You pass a CrawlerRunConfig object that sets up everything about a crawl—content filtering, caching, session reuse, JS code, screenshots, etc.

import asyncio
from crawl4ai import CrawlerRunConfig, CacheMode

run_cfg = CrawlerRunConfig(
    cache_mode=CacheMode.BYPASS,
    css_selector="main.article",
    word_count_threshold=10,
    screenshot=True
)

async with AsyncWebCrawler(config=browser_cfg) as crawler:
    result = await crawler.arun("https://example.com/news", config=run_cfg)
    print("Crawled HTML length:", len(result.cleaned_html))
    if result.screenshot:
        print("Screenshot base64 length:", len(result.screenshot))
Copy
3.2 Legacy Parameters Still Accepted
For backward compatibility, arun() can still accept direct arguments like css_selector=..., word_count_threshold=..., etc., but we strongly advise migrating them into a CrawlerRunConfig.

4. Batch Processing: arun_many()
async def arun_many(
    self,
    urls: List[str],
    config: Optional[CrawlerRunConfig] = None,
    # Legacy parameters maintained for backwards compatibility...
) -> List[CrawlResult]:
    """
    Process multiple URLs with intelligent rate limiting and resource monitoring.
    """
Copy
4.1 Resource-Aware Crawling
The arun_many() method now uses an intelligent dispatcher that:

Monitors system memory usage
Implements adaptive rate limiting
Provides detailed progress monitoring
Manages concurrent crawls efficiently
4.2 Example Usage
Check page Multi-url Crawling for a detailed example of how to use arun_many().

### 4.3 Key Features

1. **Rate Limiting**

   - Automatic delay between requests
   - Exponential backoff on rate limit detection
   - Domain-specific rate limiting
   - Configurable retry strategy

2. **Resource Monitoring**

   - Memory usage tracking
   - Adaptive concurrency based on system load
   - Automatic pausing when resources are constrained

3. **Progress Monitoring**

   - Detailed or aggregated progress display
   - Real-time status updates
   - Memory usage statistics

4. **Error Handling**

   - Graceful handling of rate limits
   - Automatic retries with backoff
   - Detailed error reporting

---

## 5. `CrawlResult` Output

Each `arun()` returns a **`CrawlResult`** containing:

- `url`: Final URL (if redirected).
- `html`: Original HTML.
- `cleaned_html`: Sanitized HTML.
- `markdown_v2`: Deprecated. Instead just use regular `markdown`
- `extracted_content`: If an extraction strategy was used (JSON for CSS/LLM strategies).
- `screenshot`, `pdf`: If screenshots/PDF requested.
- `media`, `links`: Information about discovered images/links.
- `success`, `error_message`: Status info.

For details, see [CrawlResult doc](./crawl-result.md).

---

## 6. Quick Example

Below is an example hooking it all together:

```python
import asyncio
from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig, CacheMode
from crawl4ai.extraction_strategy import JsonCssExtractionStrategy
import json

async def main():
    # 1. Browser config
    browser_cfg = BrowserConfig(
        browser_type="firefox",
        headless=False,
        verbose=True
    )

    # 2. Run config
    schema = {
        "name": "Articles",
        "baseSelector": "article.post",
        "fields": [
            {
                "name": "title",
                "selector": "h2",
                "type": "text"
            },
            {
                "name": "url",
                "selector": "a",
                "type": "attribute",
                "attribute": "href"
            }
        ]
    }

    run_cfg = CrawlerRunConfig(
        cache_mode=CacheMode.BYPASS,
        extraction_strategy=JsonCssExtractionStrategy(schema),
        word_count_threshold=15,
        remove_overlay_elements=True,
        wait_for="css:.post"  # Wait for posts to appear
    )

    async with AsyncWebCrawler(config=browser_cfg) as crawler:
        result = await crawler.arun(
            url="https://example.com/blog",
            config=run_cfg
        )

        if result.success:
            print("Cleaned HTML length:", len(result.cleaned_html))
            if result.extracted_content:
                articles = json.loads(result.extracted_content)
                print("Extracted articles:", articles[:2])
        else:
            print("Error:", result.error_message)

asyncio.run(main())
Copy
Explanation:

We define a BrowserConfig with Firefox, no headless, and verbose=True. 
We define a CrawlerRunConfig that bypasses cache, uses a CSS extraction schema, has a word_count_threshold=15, etc. 
We pass them to AsyncWebCrawler(config=...) and arun(url=..., config=...).
7. Best Practices & Migration Notes
1. Use BrowserConfig for global settings about the browser’s environment.  2. Use CrawlerRunConfig for per-crawl logic (caching, content filtering, extraction strategies, wait conditions).  3. Avoid legacy parameters like css_selector or word_count_threshold directly in arun(). Instead:

run_cfg = CrawlerRunConfig(css_selector=".main-content", word_count_threshold=20)
result = await crawler.arun(url="...", config=run_cfg)
Copy
4. Context Manager usage is simplest unless you want a persistent crawler across many calls.

8. Summary
AsyncWebCrawler is your entry point to asynchronous crawling:

Constructor accepts BrowserConfig (or defaults). 
arun(url, config=CrawlerRunConfig) is the main method for single-page crawls. 
arun_many(urls, config=CrawlerRunConfig) handles concurrency across multiple URLs. 
For advanced lifecycle control, use start() and close() explicitly. 
Migration:

If you used AsyncWebCrawler(browser_type="chromium", css_selector="..."), move browser settings to BrowserConfig(...) and content/crawl logic to CrawlerRunConfig(...).
This modular approach ensures your code is clean, scalable, and easy to maintain. For any advanced or rarely used parameters, see the BrowserConfig docs.
````
