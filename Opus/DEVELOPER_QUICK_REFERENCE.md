# Opus0 Developer Quick Reference

## Quick Start Commands

### Local Development
```bash
# Backend
cd ai-backend
poetry install && poetry run playwright install
poetry run python run.py

# Frontend  
cd web-client
npm install && npm run dev

# Docker
docker compose up --build
```

### Environment Setup
```bash
# Copy environment template
cp ai-backend/.env.example ai-backend/.env

# Required API Keys
OPENAI_API_KEY=sk-...
GOOGLE_API_KEY=...
MONGODB_URL=mongodb://localhost:27017/opus0
```

## Architecture Overview

### Agent Hierarchy
```
Manager Tier:    TaskPlanner → OperationsManager → Aggregator
Factory Tier:    AgentFactory (with LLM Router)
Worker Tier:     7 Specialized Agents
```

### Key Components
- **TaskPlanner**: Breaks down user requests into subtasks
- **OperationsManager**: Coordinates subtask execution
- **AgentFactory**: Routes subtasks to appropriate workers
- **Workers**: Execute specialized tasks (LLM, Research, Code, etc.)
- **Aggregator**: Combines results into final response

## File Structure

### Critical Files
```
ai-backend/app/
├── main.py                     # FastAPI entry point
├── agents/
│   ├── agent_factory.py        # Task routing logic
│   ├── aggregator_agent.py     # Response aggregation
│   ├── base_agent.py          # Common agent functionality
│   ├── managers/
│   │   ├── task_planner.py     # Task decomposition
│   │   └── operations_manager.py # Task coordination
│   └── workers/               # Specialized worker agents
├── api/routes/chat.py         # Main chat endpoint
└── utils/
    ├── communication.py       # Inter-agent messaging
    ├── task_status.py        # Task state management
    └── live_updates.py       # Real-time UI updates
```

### Data Directories
```
data/sessions/{chat_id}/
├── agent_comm/          # Inter-agent messages
├── knowledge_base/      # Task status & outputs
├── public_pdfs/         # Generated documents
└── user_uploads/        # User files
```

## Agent Development

### Creating a New Worker Agent

1. **Inherit from BaseAgent:**
```python
from app.agents.base_agent import BaseAgent

class MyWorkerAgent(BaseAgent):
    def __init__(self, agent_id: str, chat_id: str):
        super().__init__(agent_id, agent_type="my_worker")
        self.chat_id = chat_id
```

2. **Implement execute_task:**
```python
async def execute_task(self, task_details: Dict[str, Any]):
    # Extract task information
    subtask_id = task_details["subtask_id"]
    description = task_details["subtask_description"]
    
    # Update status
    self.update_task_status("in_progress")
    
    # Process task
    result = await self.process_subtask(description)
    
    # Save output
    output_file = f"subtask_{subtask_id}_output.md"
    self.save_output(output_file, result)
    
    # Report completion
    self.send_completion_message(subtask_id, output_file)
```

3. **Add to AgentFactory routing:**
```python
# In agent_factory.py router prompt
"my_worker": "Description of when to use this agent"

# In process_subtask method
elif chosen_agent_type == "my_worker":
    agent = MyWorkerAgent(
        agent_id=f"my_worker_agent_{subtask_id}", 
        chat_id=self.chat_id
    )
```

### Agent Communication

**Send Message:**
```python
from app.utils.communication import send_message

send_message(
    chat_id=self.chat_id,
    recipient="manager",
    message={
        "subtask_id": subtask_id,
        "status": "completed",
        "output_file": "result.md"
    }
)
```

**Receive Message:**
```python
from app.utils.communication import receive_message

message = receive_message(self.chat_id, agent_id="worker")
if message:
    # Process message
    pass
```

## API Development

### Adding New Endpoints

1. **Create route file:**
```python
# app/api/routes/my_route.py
from fastapi import APIRouter

router = APIRouter()

@router.get("/my-endpoint")
async def my_endpoint():
    return {"message": "Hello World"}
```

2. **Register in main.py:**
```python
from app.api.routes.my_route import router as my_router
app.include_router(my_router, prefix="/api")
```

### Socket.IO Events

**Add new event handler:**
```python
@sio.on("my_event")
async def handle_my_event(sid, data):
    # Process event
    await sio.emit("my_response", {"result": "data"}, room=sid)
```

## Database Operations

### MongoDB Queries
```python
from app.db import chat_histories_collection

# Insert
chat_histories_collection.insert_one({
    "conversation_id": chat_id,
    "messages": []
})

# Update
chat_histories_collection.update_one(
    {"conversation_id": chat_id},
    {"$push": {"messages": new_message}}
)

# Find
doc = chat_histories_collection.find_one({
    "conversation_id": chat_id
})
```

## Testing

### Unit Tests
```bash
# Run all tests
poetry run pytest

# Run specific test file
poetry run pytest tests/test_agents.py

# Run with coverage
poetry run pytest --cov=app
```

### Integration Tests
```bash
# Test agent communication
poetry run pytest tests/integration/test_agent_flow.py

# Test API endpoints
poetry run pytest tests/integration/test_api.py
```

## Debugging

### Logging
```python
import logging
logger = logging.getLogger(__name__)

logger.info("Information message")
logger.error("Error message")
logger.debug("Debug message")
```

### Performance Monitoring
```python
# Use BaseAgent timing utilities
with self.time_block("operation_name"):
    # Your code here
    pass

# Token counting
self.log_input_tokens(model_name, token_count)
self.log_output_tokens(model_name, token_count)
```

### Common Issues

1. **Agent not receiving messages:**
   - Check file permissions in `agent_comm/` directory
   - Verify message format matches expected schema

2. **Task status not updating:**
   - Ensure `task_status.json` is writable
   - Check for JSON formatting errors

3. **LLM API errors:**
   - Verify API keys in `.env` file
   - Check rate limits and quotas
   - Monitor token usage

## Deployment

### Docker Build
```bash
# Backend
docker build -t opus0-backend ./ai-backend

# Frontend
docker build -t opus0-frontend ./web-client

# Full stack
docker compose build
```

### Environment Variables
```bash
# Production settings
LOG_LEVEL=INFO
MAX_CONCURRENT_WORKERS=10
MONGODB_URL=mongodb://prod-server:27017/opus0
```

## Performance Tips

1. **Optimize LLM calls:**
   - Use appropriate model sizes
   - Implement response caching
   - Monitor token usage

2. **Database optimization:**
   - Use indexes for frequent queries
   - Implement connection pooling
   - Monitor query performance

3. **File I/O optimization:**
   - Use async file operations
   - Implement file caching
   - Clean up temporary files

## Security Considerations

1. **API Security:**
   - Validate all inputs
   - Implement rate limiting
   - Use HTTPS in production

2. **Agent Security:**
   - Sandbox code execution
   - Validate agent outputs
   - Monitor for malicious activities

3. **Data Protection:**
   - Encrypt sensitive data
   - Implement access controls
   - Regular security audits
